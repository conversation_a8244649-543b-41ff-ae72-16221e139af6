package cn.dutp.job.task;


import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.job.domain.DtbBookChapter;
import cn.dutp.job.domain.DtbBookChapterContent;
import cn.dutp.job.domain.DtbBookChapterResource;
import cn.dutp.job.domain.DutpTask;
import cn.dutp.job.mapper.DtbBookChapterMapper;
import cn.dutp.job.mapper.DutpTaskMapper;
import cn.dutp.job.service.IDtbBookChapterResourceService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.dutp.job.domain.constant.BookChapterContentEnum.BOOK_CHAPTER_CONTENT;


/**
 * 统计章节资源任务
 */
@Component("statisticalChapterResourceTask")
@Slf4j
public class StatisticalChapterResourceTask {

    @Autowired
    private DtbBookChapterMapper chapterMapper;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DutpTaskMapper taskMapper;

    @Autowired
    private IDtbBookChapterResourceService bookChapterResourceService;

    /**
     * 每天0点20秒执行
     */
    public void statisticalChapterData() {
        log.info("统计章节资源任务开始执行");
        List<DutpTask> taskList = taskMapper.selectList(new LambdaQueryWrapper<DutpTask>()
                .select(DutpTask::getDataId, DutpTask::getTaskId)
                .ne(DutpTask::getTaskState, 2)
                .eq(DutpTask::getTaskType, 8));
        if (ObjectUtil.isNotEmpty(taskList)) {
            for (DutpTask task : taskList) {
                try {
                    Long bookId = task.getDataId();
                    if (ObjectUtil.isEmpty(bookId)) {
                        continue;
                    }
                    task.setTaskState(1);
                    task.setStartTime(new Date());
                    taskMapper.updateById(task);
                    DtbBookChapter dtbBookChapter = new DtbBookChapter();
                    dtbBookChapter.setBookId(bookId);
                    List<DtbBookChapter> chapterList = chapterMapper.dtbBookChapterMapper(dtbBookChapter);

                    if (ObjectUtil.isEmpty(chapterList)) {
                        continue;
                    }

                    for (DtbBookChapter chapter : chapterList) {
                        // 查询章节内容
                        Query query = new Query(Criteria.where("chapterId").is(chapter.getChapterId()));
                        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
                        if (ObjectUtil.isEmpty(chapterContent)) {
                            continue;
                        }
                        String contentJson = chapterContent.getContent();
                        if (ObjectUtil.isEmpty(contentJson)) {
                            continue;
                        }

                        JSONObject jsonObject = JSONUtil.parseObj(contentJson);
                        JSONArray pageList = jsonObject.getJSONArray("content");
                        List<DtbBookChapterResource> resourceList = new ArrayList<>();
                        if (ObjectUtil.isEmpty(pageList)) {
                            continue;
                        }
                        analysisNode(resourceList, pageList, chapter.getChapterId(), bookId, 1);
                        if (ObjectUtil.isEmpty(resourceList)) {
                            continue;
                        }

                        // 保存资源
                        bookChapterResourceService.saveBatch(resourceList);

                    }

                    task.setTaskState(2);
                } catch (Exception e) {
                    task.setTaskState(3);
                    log.info("统计章节资源任务执行失败：{}", e);
                }
                task.setEndTime(new Date());
                taskMapper.updateById(task);
            }
        }
        log.info("统计章节资源任务执行结束");
    }

    /**
     * 分析节点
     *
     * @param resourceList
     * @param nodeList
     * @param chapterId
     * @param bookId
     */
    private void analysisNode(List<DtbBookChapterResource> resourceList, JSONArray nodeList, Long chapterId, Long bookId, Integer pageNumber) {
        for (Object parentNode : nodeList) {
            JSONObject node = (JSONObject) parentNode;
            String type = node.getStr("type");
            Integer pageNum = pageNumber;
            if ("page".equals(type)) {
                JSONObject attrs = node.getJSONObject("attrs");
                pageNum = attrs.getInt("pageNumber");
                if (ObjectUtil.isEmpty(pageNum)) {
                    pageNum = 1;
                }
            }


            if (type.equals("imageLayout") || type.equals("imageInLine") || type.equals("imageIcon") || type.equals("imageGallery")) {
                // 图片
                if (type.equals("imageGallery")) {
                    // 画廊
                    JSONObject attrs = node.getJSONObject("attrs");
                    JSONArray imgList = attrs.getJSONArray("imgList");
                    String id = attrs.getStr("id");
                    for (Object o : imgList) {
                        JSONObject imgAttrs = (JSONObject) o;
                        String fileName = imgAttrs.getStr("name");
                        String fileUrl = imgAttrs.getStr("src");
                        Long fileSiz = imgAttrs.getLong("size");
                        DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 1, pageNum, id);
                        resourceList.add(resource);
                    }
                } else {
                    // 非画廊
                    JSONObject attrs = node.getJSONObject("attrs");
                    String fileName = attrs.getStr("name");
                    String fileUrl = attrs.getStr("src");
                    Long fileSiz = attrs.getLong("size");
                    String id = attrs.getStr("id");
                    DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 1, pageNum, id);
                    resourceList.add(resource);
                }
            } else if (type.equals("resourceCover")) {
                // 资源封面组件
                JSONObject attrs = node.getJSONObject("attrs");
                Integer rcType = attrs.getInt("rcType");
                String fileName = attrs.getStr("title");
                String fileUrl = attrs.getStr("url");
                Long fileSiz = attrs.getLong("size");
                String id = attrs.getStr("id");
                if (rcType == 0) {
                    // 3D
                    DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 6, pageNum, id);
                    resourceList.add(resource);
                } else if (rcType == 1 || rcType == 2) {
                    // AR/VR
                    DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 5, pageNum, id);
                    resourceList.add(resource);
                } else if (rcType == 3) {
                    // 仿真
                    DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 4, pageNum, id);
                    resourceList.add(resource);
                } else if (rcType == 4) {
                    // 游戏

                } else if (rcType == 5) {
                    // 教学资源
                    DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 8, pageNum, id);
                    resourceList.add(resource);
                } else if (rcType == 6) {
                    // 扩展阅读

                } else if (rcType == 7) {
                    // 实训
                    DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 9, pageNum, id);
                    resourceList.add(resource);
                }
            } else if (type.equals("audio")) {
                // 音频
                JSONObject attrs = node.getJSONObject("attrs");
                String fileName = attrs.getStr("name");
                String fileUrl = attrs.getStr("src");
                Long fileSiz = attrs.getLong("size");
                String id = attrs.getStr("id");
                DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 2, pageNum, id);
                resourceList.add(resource);
            } else if (type.equals("video")) {
                // 视频
                JSONObject attrs = node.getJSONObject("attrs");
                String fileName = attrs.getStr("name");
                String fileUrl = attrs.getStr("src");
                Long fileSiz = attrs.getLong("size");
                String id = attrs.getStr("id");
                DtbBookChapterResource resource = generateDtbBookChapterResource(chapterId, bookId, fileName, fileUrl, fileSiz, 3, pageNum, id);
                resourceList.add(resource);
            }

            JSONArray childrenNodeList = node.getJSONArray("content");
            if (ObjectUtil.isNotEmpty(childrenNodeList)) {
                analysisNode(resourceList, childrenNodeList, chapterId, bookId, pageNum);
            }
        }
    }

    /**
     * 生成章节资源
     *
     * @param chapterId
     * @param bookId
     * @param fileName
     * @param fileUrl
     * @param fileSiz
     * @param fileType
     * @return
     */
    @NotNull
    private static DtbBookChapterResource generateDtbBookChapterResource(Long chapterId, Long bookId, String fileName, String fileUrl, Long fileSiz, Integer fileType, Integer pageNum, String id) {
        DtbBookChapterResource resource = new DtbBookChapterResource();
        resource.setChapterId(chapterId);
        resource.setBookId(bookId);
        resource.setFileName(fileName);
        resource.setFileSize(fileSiz);
        resource.setFileUrl(fileUrl);
        resource.setFileType(fileType);
        resource.setPageNumber(pageNum);
        resource.setDomId(id);
        return resource;
    }

}
