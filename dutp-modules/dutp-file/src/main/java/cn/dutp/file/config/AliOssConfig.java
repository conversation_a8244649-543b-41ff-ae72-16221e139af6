package cn.dutp.file.config;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Minio 配置信息
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "ali-oss")
public class AliOssConfig {

    private String regionId;

    private String roleArn;

    private String ossEndPoint;

    private String stsEndPoint;

    private String accessKeyId;

    private String accessKeySecret;

    private Integer urlExpirationMinute;

    private String bucket;

    private final static String key = "pVwKSycbZvgJ+jrNGsDA8g==";

    public void setAccessKeyId(String accessKeyId) {
        if (ObjectUtil.isNotEmpty(accessKeyId)) {
            SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, Base64.decode(key));
            this.accessKeyId = aes.decryptStr(accessKeyId, CharsetUtil.CHARSET_UTF_8);
            // System.out.println("accessKeyId---->" + this.accessKeyId);
        } else {
            this.accessKeyId = accessKeyId;
        }
    }

    public void setAccessKeySecret(String accessKeySecret) {
        if (ObjectUtil.isNotEmpty(accessKeySecret)) {
            SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, Base64.decode(key));
            this.accessKeySecret = aes.decryptStr(accessKeySecret, CharsetUtil.CHARSET_UTF_8);
            // System.out.println("accessKeySecret---->" + this.accessKeySecret);
        } else {
            this.accessKeySecret = accessKeySecret;
        }
    }
}
