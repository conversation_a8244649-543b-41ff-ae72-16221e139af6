package cn.dutp.system.service;

import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.DutpUserWithCode;

import java.util.List;

/**
 * DUTP-BASE-001用户【教师，学生，无身份者】Service接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface IDutpUserService
{
    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public DutpUser selectDutpUserByUserName(String userName);
    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public DutpUser selectUserById(Long userId);
    /**
     * 根据手机号查询用户
     * @param phoneNumber 手机号
     * @return 结果
     */
    public DutpUser selectUserByPhoneNumber(String phoneNumber);
    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(DutpUser user);
    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(DutpUser user);
    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(DutpUser user);
    /**
     * 忘记密码(短信验证码自行验证)
     *
     * @param user 用户信息
     * @return 结果
     */
    public int forgetPwd(DutpUserWithCode user);

    /*
     * 获取用户列表 用户下拉
     */
    List<DutpUser> listNotPage(DutpUser user);

    /**
     * 根据教材id查询教材绑定的用户列表
     * @param user
     * @return
     */
    List<DutpUser> listUserByBookIdAndSchoolId(DutpUser user);
}
