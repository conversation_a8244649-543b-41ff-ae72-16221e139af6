package cn.dutp.book.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * 高级电子教材PDF转UMO Editor JSON转换器
 * Java主程序调用Python进行复杂PDF解析，输出UMO Editor兼容的JSON格式
 * 
 * 特性：
 * - 支持复杂电子教材排版解析
 * - 分页处理和章节识别
 * - 图片引用和编号识别
 * - 多层次标题和列表处理
 * - 可配置的标签映射
 */
public class AdvancedTextbookPDFConverter {
    
    private static final Logger logger = Logger.getLogger(AdvancedTextbookPDFConverter.class.getName());
    private final ObjectMapper objectMapper;
    private final Map<String, String> tagMapping;
    private final String pythonScriptPath;
    private final String tempDir;
    
    // 默认配置
    private static final int DEFAULT_TIMEOUT_SECONDS = 300; // 5分钟超时
    private static final String DEFAULT_PYTHON_CMD = "python";
    private static final int DEFAULT_PAGE_HEIGHT = 800; // 默认页面高度
    
    public AdvancedTextbookPDFConverter() {
        this(createDefaultConfig());
    }
    
    public AdvancedTextbookPDFConverter(Map<String, Object> config) {
        this.objectMapper = new ObjectMapper();
        this.tagMapping = createDefaultTagMapping();
        this.pythonScriptPath = (String) config.getOrDefault("pythonScriptPath", "C:\\Users\\<USER>\\Desktop\\minmax给的第二版方案\\code\\textbook_parser.py");
        this.tempDir = (String) config.getOrDefault("tempDir", System.getProperty("java.io.tmpdir"));
        
        // 加载自定义标签映射
        if (config.containsKey("customTagMapping")) {
            @SuppressWarnings("unchecked")
            Map<String, String> customMapping = (Map<String, String>) config.get("customTagMapping");
            this.tagMapping.putAll(customMapping);
        }
        
        logger.info("PDF转换器初始化完成，Python脚本路径: " + pythonScriptPath);
    }
    
    /**
     * 转换PDF文件为UMO Editor JSON格式
     */
    public String convertPdfToUmoJson(String pdfPath) throws Exception {
        return convertPdfToUmoJson(new File(pdfPath));
    }
    
    /**
     * 转换PDF文件为UMO Editor JSON格式
     */
    public String convertPdfToUmoJson(File pdfFile) throws Exception {
        if (!pdfFile.exists()) {
            throw new FileNotFoundException("PDF文件不存在: " + pdfFile.getAbsolutePath());
        }
        
        logger.info("开始转换PDF: " + pdfFile.getName());
        
        // 1. 调用Python脚本解析PDF
        String rawContent = callPythonParser(pdfFile.getAbsolutePath());
        
        // 2. 解析Python返回的结构化数据
        JsonNode parsedData = objectMapper.readTree(rawContent);
        
        // 3. 转换为UMO Editor格式
        ObjectNode umoDocument = convertToUmoFormat(parsedData);
        
        // 4. 应用标签映射
        applyTagMapping(umoDocument);
        
        String result = objectMapper.writeValueAsString(umoDocument);
        logger.info("PDF转换完成，生成JSON长度: " + result.length());
        
        return result;
    }
    
    /**
     * 转换PDF输入流为UMO Editor JSON格式
     */
    public String convertPdfStreamToUmoJson(InputStream pdfStream, String filename) throws Exception {
        // 创建临时文件
        File tempFile = new File(tempDir, "temp_" + System.currentTimeMillis() + "_" + filename);
        
        try {
            // 写入临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = pdfStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            
            // 转换临时文件
            return convertPdfToUmoJson(tempFile);
            
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
    
    /**
     * 调用Python脚本解析PDF
     */
    private String callPythonParser(String pdfPath) throws Exception {
        List<String> command = Arrays.asList(
            DEFAULT_PYTHON_CMD,
            pythonScriptPath,
            pdfPath
        );
        
        logger.info("执行Python命令: " + String.join(" ", command));
        
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.directory(new File("."));
        pb.redirectErrorStream(true);
        
        Process process = pb.start();
        
        // 读取输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        
        // 等待进程完成
        boolean finished = process.waitFor(DEFAULT_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("Python脚本执行超时");
        }
        
        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new RuntimeException("Python脚本执行失败，退出代码: " + exitCode + "\n输出: " + output.toString());
        }
        
        String result = output.toString().trim();
        logger.info("Python脚本执行成功，输出长度: " + result.length());
        
        return result;
    }
    
    /**
     * 转换为UMO Editor格式
     */
    private ObjectNode convertToUmoFormat(JsonNode parsedData) {
        ObjectNode document = objectMapper.createObjectNode();
        document.put("type", "doc");
        
        ArrayNode content = objectMapper.createArrayNode();
        
        // 获取解析的页面数据
        JsonNode pages = parsedData.get("pages");
        if (pages != null && pages.isArray()) {
            int pageNumber = 1;
            
            for (JsonNode pageData : pages) {
                ObjectNode page = createPageNode(pageData, pageNumber++);
                content.add(page);
            }
        } else {
            // 如果没有分页信息，创建单页
            ObjectNode singlePage = createPageNode(parsedData, 1);
            content.add(singlePage);
        }
        
        document.set("content", content);
        return document;
    }
    
    /**
     * 创建页面节点
     */
    private ObjectNode createPageNode(JsonNode pageData, int pageNumber) {
        ObjectNode page = objectMapper.createObjectNode();
        page.put("type", "page");
        
        // 页面属性
        ObjectNode attrs = objectMapper.createObjectNode();
        attrs.put("id", generateId());
        attrs.put("extend", false);
        attrs.put("class", "bellCss");
        attrs.putNull("HTMLAttributes");
        attrs.put("pageNumber", pageNumber);
        attrs.put("force", pageNumber > 1);
        attrs.set("slots", objectMapper.createObjectNode());
        attrs.put("pagesPosition", "bottom");
        attrs.put("pagesAlign", "center");
        attrs.put("pagesShow", true);
        page.set("attrs", attrs);
        
        // 页面内容
        ArrayNode pageContent = objectMapper.createArrayNode();
        
        // 处理页面中的各种元素
        JsonNode elements = pageData.get("elements");
        if (elements != null && elements.isArray()) {
            for (JsonNode element : elements) {
                ObjectNode umoElement = convertElementToUmo(element);
                if (umoElement != null) {
                    pageContent.add(umoElement);
                }
            }
        }
        
        page.set("content", pageContent);
        return page;
    }
    
    /**
     * 转换元素为UMO格式
     */
    private ObjectNode convertElementToUmo(JsonNode element) {
        String type = element.get("type").asText();
        
        switch (type) {
            case "chapter_header":
                return createChapterHeader(element);
            case "heading":
                return createHeading(element);
            case "paragraph":
                return createParagraph(element);
            case "ordered_list":
                return createOrderedList(element);
            case "bullet_list":
                return createBulletList(element);
            case "image_reference":
                return createImageReference(element);
            case "special_box":
                return createSpecialBox(element);
            default:
                return createParagraph(element); // 默认作为段落处理
        }
    }
    
    /**
     * 创建章节头部
     */
    private ObjectNode createChapterHeader(JsonNode element) {
        ObjectNode header = objectMapper.createObjectNode();
        header.put("type", "chapterHeader");
        
        ObjectNode attrs = createBasicAttrs();
        attrs.put("vnode", true);
        attrs.putNull("id");
        attrs.put("title", element.get("title").asText(""));
        attrs.putNull("bgColor");
        attrs.putNull("bgImg");
        attrs.put("textAlign", "center");
        attrs.put("height", "258");
        
        header.set("attrs", attrs);
        return header;
    }
    
    /**
     * 创建标题节点
     */
    private ObjectNode createHeading(JsonNode element) {
        ObjectNode heading = objectMapper.createObjectNode();
        heading.put("type", "heading");
        
        ObjectNode attrs = createBasicAttrs();
        attrs.put("level", element.get("level").asInt(1));
        attrs.put("data-toc-id", generateId());
        
        heading.set("attrs", attrs);
        heading.set("content", createTextContent(element.get("text").asText("")));
        
        return heading;
    }
    
    /**
     * 创建段落节点
     */
    private ObjectNode createParagraph(JsonNode element) {
        ObjectNode paragraph = objectMapper.createObjectNode();
        paragraph.put("type", "paragraph");
        
        ObjectNode attrs = createBasicAttrs();
        // 根据内容判断是否需要缩进
        JsonNode indentNode = element.get("indent");
        if (indentNode != null) {
            attrs.put("indent", indentNode.asInt(1));
        } else {
            attrs.put("indent", 1);
        }
        
        paragraph.set("attrs", attrs);
        
        String text = element.has("text") ? element.get("text").asText("") : "";
        paragraph.set("content", createTextContent(text));
        
        return paragraph;
    }
    
    /**
     * 创建有序列表
     */
    private ObjectNode createOrderedList(JsonNode element) {
        ObjectNode list = objectMapper.createObjectNode();
        list.put("type", "orderedList");
        
        ObjectNode attrs = createBasicAttrs();
        attrs.put("start", 1);
        attrs.put("listType", "decimal-leading-zero");
        
        list.set("attrs", attrs);
        
        ArrayNode content = objectMapper.createArrayNode();
        JsonNode items = element.get("items");
        if (items != null && items.isArray()) {
            for (JsonNode item : items) {
                ObjectNode listItem = createListItem(item);
                content.add(listItem);
            }
        }
        
        list.set("content", content);
        return list;
    }
    
    /**
     * 创建无序列表
     */
    private ObjectNode createBulletList(JsonNode element) {
        ObjectNode list = objectMapper.createObjectNode();
        list.put("type", "bulletList");
        
        list.set("attrs", createBasicAttrs());
        
        ArrayNode content = objectMapper.createArrayNode();
        JsonNode items = element.get("items");
        if (items != null && items.isArray()) {
            for (JsonNode item : items) {
                ObjectNode listItem = createListItem(item);
                content.add(listItem);
            }
        }
        
        list.set("content", content);
        return list;
    }
    
    /**
     * 创建列表项
     */
    private ObjectNode createListItem(JsonNode item) {
        ObjectNode listItem = objectMapper.createObjectNode();
        listItem.put("type", "listItem");
        
        ObjectNode attrs = createBasicAttrs();
        attrs.putNull("indent");
        listItem.set("attrs", attrs);
        
        ArrayNode content = objectMapper.createArrayNode();
        ObjectNode paragraph = createParagraph(item);
        content.add(paragraph);
        
        listItem.set("content", content);
        return listItem;
    }
    
    /**
     * 创建图片引用节点
     */
    private ObjectNode createImageReference(JsonNode element) {
        ObjectNode imageRef = objectMapper.createObjectNode();
        imageRef.put("type", "paragraph");
        
        ObjectNode attrs = createBasicAttrs();
        attrs.put("textAlign", "center");
        imageRef.set("attrs", attrs);
        
        ArrayNode content = objectMapper.createArrayNode();
        ObjectNode textNode = objectMapper.createObjectNode();
        textNode.put("type", "text");
        textNode.put("text", element.get("text").asText(""));
        
        // 添加特殊标记表示这是图片引用
        ArrayNode marks = objectMapper.createArrayNode();
        ObjectNode mark = objectMapper.createObjectNode();
        mark.put("type", "textStyle");
        ObjectNode markAttrs = objectMapper.createObjectNode();
        markAttrs.put("fontWeight", "bold");
        markAttrs.put("fontStyle", "italic");
        mark.set("attrs", markAttrs);
        marks.add(mark);
        textNode.set("marks", marks);
        
        content.add(textNode);
        imageRef.set("content", content);
        
        return imageRef;
    }
    
    /**
     * 创建特殊框（如提示框）
     */
    private ObjectNode createSpecialBox(JsonNode element) {
        ObjectNode box = objectMapper.createObjectNode();
        box.put("type", "paragraph");
        
        ObjectNode attrs = createBasicAttrs();
        attrs.put("containerColor", "#f0f0f0");
        attrs.put("backgroundBorder", "1px solid #ddd");
        
        box.set("attrs", attrs);
        box.set("content", createTextContent(element.get("text").asText("")));
        
        return box;
    }
    
    /**
     * 创建基础属性
     */
    private ObjectNode createBasicAttrs() {
        ObjectNode attrs = objectMapper.createObjectNode();
        attrs.put("id", generateId());
        attrs.put("extend", false);
        attrs.put("class", "bellCss");
        attrs.put("columnCount", 0);
        attrs.putNull("indent");
        attrs.put("textAlign", "left");
        attrs.put("lineHeight", 1.5);
        attrs.set("margin", objectMapper.createObjectNode());
        attrs.put("backgroundImage", "");
        attrs.put("containerColor", "");
        attrs.put("backgroundSize", "");
        attrs.put("backgroundBorder", "");
        
        return attrs;
    }
    
    /**
     * 创建文本内容
     */
    private ArrayNode createTextContent(String text) {
        ArrayNode content = objectMapper.createArrayNode();
        
        if (text != null && !text.trim().isEmpty()) {
            ObjectNode textNode = objectMapper.createObjectNode();
            textNode.put("type", "text");
            
            // 创建默认文本样式
            ArrayNode marks = objectMapper.createArrayNode();
            ObjectNode textStyle = objectMapper.createObjectNode();
            textStyle.put("type", "textStyle");
            
            ObjectNode styleAttrs = objectMapper.createObjectNode();
            styleAttrs.putNull("fontFamily");
            styleAttrs.put("fontSize", "16px");
            styleAttrs.putNull("color");
            styleAttrs.put("letterSpacing", "0px");
            textStyle.set("attrs", styleAttrs);
            
            marks.add(textStyle);
            textNode.set("marks", marks);
            textNode.put("text", text);
            
            content.add(textNode);
        }
        
        return content;
    }
    
    /**
     * 应用标签映射
     */
    private void applyTagMapping(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objNode = (ObjectNode) node;
            if (objNode.has("type")) {
                String originalType = objNode.get("type").asText();
                if (tagMapping.containsKey(originalType)) {
                    objNode.put("type", tagMapping.get(originalType));
                }
            }
            
            // 递归处理子节点
            objNode.fields().forEachRemaining(entry -> {
                applyTagMapping(entry.getValue());
            });
            
        } else if (node.isArray()) {
            for (JsonNode childNode : node) {
                applyTagMapping(childNode);
            }
        }
    }
    
    /**
     * 生成唯一ID
     */
    private String generateId() {
        return "id_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
    }
    
    /**
     * 创建默认配置
     */
    private static Map<String, Object> createDefaultConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("pythonScriptPath", "C:\\Users\\<USER>\\Desktop\\minmax给的第二版方案\\code\\textbook_parser.py");
        config.put("tempDir", System.getProperty("E:\\temp"));
        return config;
    }
    
    /**
     * 创建默认标签映射
     */
    private Map<String, String> createDefaultTagMapping() {
        Map<String, String> mapping = new HashMap<>();
        
        // 默认映射 - 如果需要可以覆盖
        // mapping.put("heading", "customHeading");
        // mapping.put("paragraph", "customParagraph");
        
        return mapping;
    }
    
    /**
     * 设置自定义标签映射
     */
    public void setCustomTagMapping(Map<String, String> customMapping) {
        this.tagMapping.clear();
        this.tagMapping.putAll(createDefaultTagMapping());
        this.tagMapping.putAll(customMapping);
        
        logger.info("更新标签映射: " + customMapping);
    }
    
    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
//        if (args.length < 1) {
//            System.err.println("用法: java AdvancedTextbookPDFConverter <PDF文件路径>");
//            System.exit(1);
//        }
        
        try {
            AdvancedTextbookPDFConverter converter = new AdvancedTextbookPDFConverter();
            
            // 示例：设置自定义标签映射
            Map<String, String> customMapping = new HashMap<>();
            // customMapping.put("heading", "umo-heading");
            // customMapping.put("paragraph", "umo-paragraph");
            converter.setCustomTagMapping(customMapping);
            
            String result = converter.convertPdfToUmoJson("C:\\Users\\<USER>\\Desktop\\编辑器PDF相关\\222222.pdf");
            
            // 输出结果
            System.out.println(result);
            
            // 也可以保存到文件
            String outputPath = "C:\\Users\\<USER>\\Desktop\\编辑器PDF相关\\222222.pdf".replace(".pdf", "_umo.json");
            Files.write(Paths.get(outputPath), result.getBytes("UTF-8"));
            
            System.err.println("转换完成，结果已保存到: " + outputPath);
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "转换失败", e);
            System.err.println("转换失败: " + e.getMessage());
            System.exit(1);
        }
    }
}
