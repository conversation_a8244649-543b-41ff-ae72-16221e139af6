package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbArVrResourceMapper;
import cn.dutp.book.domain.DtbArVrResource;
import cn.dutp.book.service.IDtbArVrResourceService;

/**
 * AR/VR资源库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class DtbArVrResourceServiceImpl extends ServiceImpl<DtbArVrResourceMapper, DtbArVrResource> implements IDtbArVrResourceService
{
    @Autowired
    private DtbArVrResourceMapper dtbArVrResourceMapper;

    /**
     * 查询AR/VR资源库
     *
     * @param resourceId AR/VR资源库主键
     * @return AR/VR资源库
     */
    @Override
    public DtbArVrResource selectDtbArVrResourceByResourceId(Long resourceId)
    {
        return this.getById(resourceId);
    }

    /**
     * 查询AR/VR资源库列表
     *
     * @param dtbArVrResource AR/VR资源库
     * @return AR/VR资源库
     */
    @Override
    public List<DtbArVrResource> selectDtbArVrResourceList(DtbArVrResource dtbArVrResource)
    {
        LambdaQueryWrapper<DtbArVrResource> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbArVrResource.getName())) {
                lambdaQueryWrapper.like(DtbArVrResource::getName
                ,dtbArVrResource.getName());
            }
                if(ObjectUtil.isNotEmpty(dtbArVrResource.getCoverUrl())) {
                lambdaQueryWrapper.eq(DtbArVrResource::getCoverUrl
                ,dtbArVrResource.getCoverUrl());
            }
                if(ObjectUtil.isNotEmpty(dtbArVrResource.getResourceUrl())) {
                lambdaQueryWrapper.eq(DtbArVrResource::getResourceUrl
                ,dtbArVrResource.getResourceUrl());
            }
                if(ObjectUtil.isNotEmpty(dtbArVrResource.getDescription())) {
                lambdaQueryWrapper.eq(DtbArVrResource::getDescription
                ,dtbArVrResource.getDescription());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增AR/VR资源库
     *
     * @param dtbArVrResource AR/VR资源库
     * @return 结果
     */
    @Override
    public boolean insertDtbArVrResource(DtbArVrResource dtbArVrResource)
    {
        return this.save(dtbArVrResource);
    }

    /**
     * 修改AR/VR资源库
     *
     * @param dtbArVrResource AR/VR资源库
     * @return 结果
     */
    @Override
    public boolean updateDtbArVrResource(DtbArVrResource dtbArVrResource)
    {
        return this.updateById(dtbArVrResource);
    }

    /**
     * 批量删除AR/VR资源库
     *
     * @param resourceIds 需要删除的AR/VR资源库主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbArVrResourceByResourceIds(List<Long> resourceIds)
    {
        return this.removeByIds(resourceIds);
    }

}
