<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookChapterAuditLogMapper">

    <resultMap type="DtbBookChapterAuditLog" id="DtbBookChapterAuditLogResult">
        <result property="logId" column="log_id"/>
        <result property="bookId" column="book_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="chapterStatus" column="chapter_status"/>
        <result property="auditType" column="audit_type"/>
        <result property="versionId" column="version_id"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDtbBookChapterAuditLogVo">
        select log_id,
               book_id,
               chapter_id,
               auditor_id,
               chapter_status,
               audit_type,
               version_id,
               remark,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from dtb_book_chapter_audit_log
    </sql>



    <select id="selectDtbBookChapterAuditLogByLogId" parameterType="Long" resultMap="DtbBookChapterAuditLogResult">
        <include refid="selectDtbBookChapterAuditLogVo"/>
        where log_id = #{logId}
    </select>
    <select id="getRejectedReasonInfo" resultType="cn.dutp.book.domain.DtbBookChapterAuditLog">
        SELECT
            c.chapter_name,
            a.remark,
            a.audit_type
        FROM
            dtb_book_chapter c
                INNER JOIN dtb_book b ON c.book_id = b.book_id
                INNER JOIN dtb_book_chapter_audit_log a ON c.chapter_id = a.chapter_id
        WHERE
            a.version_id = b.current_version_id
          AND a.del_flag = '0'
          AND a.chapter_status = 3
          AND c.chapter_id = #{chapterId}
        ORDER BY
            a.create_time DESC
            LIMIT 1
    </select>
    <select id="selectDtbBookChapterAuditLogList" resultType="cn.dutp.book.domain.DtbBookChapterAuditLog">
        SELECT
            c.chapter_id,
            c.chapter_name,
            b.book_no,
            b.book_id,
            b.book_name,
            b.isbn,
            b.issn,
            b.book_organize,
            l.audit_type,
            l.log_id,
            l.chapter_status,
            l.promoter_user_id,
            l.create_time,
            l.revoked_reason,
            u.nick_name
        FROM
            dtb_book_chapter c
                INNER JOIN ( SELECT ROW_NUMBER() OVER ( PARTITION BY chapter_id ORDER BY create_time DESC ) AS row_num,audit_type,chapter_status,promoter_user_id,create_time,chapter_id,auditor_id,log_id,revoked_reason  FROM dtb_book_chapter_audit_log ) l ON l.chapter_id = c.chapter_id
                INNER JOIN dtb_book b ON b.book_id = c.book_id
                INNER JOIN sys_user u ON u.user_id = l.promoter_user_id
        WHERE
            l.row_num = 1
        and l.auditor_id = #{auditorId}
        <if test="bookName != null  and bookName != ''"> and b.book_name like concat('%', #{bookName}, '%')</if>
        <if test="chapterName != null  and chapterName != ''"> and c.chapter_name like concat('%', #{chapterName}, '%')</if>
        <if test="isbn != null  and isbn != ''"> and b.isbn like concat('%', #{isbn}, '%') </if>
        <if test="issn != null  and issn != ''"> and b.issn like concat('%', #{issn}, '%') </if>
        <if test="bookNo != null  and bookNo != ''"> and b.book_no = #{bookNo}</if>
        <if test="chapterStatus != null "> and l.chapter_status = #{chapterStatus}</if>
        <if test="auditType != null "> and l.audit_type = #{auditType}</if>
        <if test="bookOrganize != null "> and b.book_organize = #{bookOrganize}</if>
        ORDER BY
            l.create_time DESC
    </select>
    <select id="selectWaitAuditLog" resultType="cn.dutp.book.domain.DtbBookChapterAuditLog">
        select log_id from dtb_book_chapter_audit_log
            where del_flag = '0'
                and book_id = #{bookId}
                and chapter_id = #{chapterId}
                and auditor_id = #{auditorId}
                and chapter_status = #{chapterStatus}
                and audit_type = #{auditType}
                and version_id = #{versionId}
                <if test="promoterUserId != null "> and promoter_user_id = #{promoterUserId} </if>
                limit 1
    </select>

    <insert id="insertDtbBookChapterAuditLog" parameterType="DtbBookChapterAuditLog" useGeneratedKeys="true"
            keyProperty="logId">
        insert into dtb_book_chapter_audit_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="chapterId != null">chapter_id,</if>
            <if test="auditorId != null">auditor_id,</if>
            <if test="chapterStatus != null">chapter_status,</if>
            <if test="auditType != null">audit_type,</if>
            <if test="versionId != null">version_id,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="chapterId != null">#{chapterId},</if>
            <if test="auditorId != null">#{auditorId},</if>
            <if test="chapterStatus != null">#{chapterStatus},</if>
            <if test="auditType != null">#{auditType},</if>
            <if test="versionId != null">#{versionId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDtbBookChapterAuditLog" parameterType="DtbBookChapterAuditLog">
        update dtb_book_chapter_audit_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="chapterId != null">chapter_id = #{chapterId},</if>
            <if test="auditorId != null">auditor_id = #{auditorId},</if>
            <if test="chapterStatus != null">chapter_status = #{chapterStatus},</if>
            <if test="auditType != null">audit_type = #{auditType},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteDtbBookChapterAuditLogByLogId" parameterType="Long">
        delete
        from dtb_book_chapter_audit_log
        where log_id = #{logId}
    </delete>

    <delete id="deleteDtbBookChapterAuditLogByLogIds" parameterType="String">
        delete from dtb_book_chapter_audit_log where log_id in
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>