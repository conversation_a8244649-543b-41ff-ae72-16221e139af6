package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserFlashCard;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 助记卡Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDtbUserFlashCardService extends IService<DtbUserFlashCard>
{
    /**
     * 查询助记卡
     *
     * @param cardId 助记卡主键
     * @return 助记卡
     */
    public DtbUserFlashCard selectDtbUserFlashCardByCardId(Long cardId);

    /**
     * 查询助记卡列表
     *
     * @param dtbUserFlashCard 助记卡
     * @return 助记卡集合
     */
    public List<DtbUserFlashCard> selectDtbUserFlashCardList(DtbUserFlashCard dtbUserFlashCard);

    /**
     * 新增助记卡
     *
     * @param dtbUserFlashCard 助记卡
     * @return 结果
     */
    public boolean insertDtbUserFlashCard(DtbUserFlashCard dtbUserFlashCard);

    /**
     * 修改助记卡
     *
     * @param dtbUserFlashCard 助记卡
     * @return 结果
     */
    public boolean updateDtbUserFlashCard(DtbUserFlashCard dtbUserFlashCard);

    /**
     * 批量删除助记卡
     *
     * @param cardIds 需要删除的助记卡主键集合
     * @return 结果
     */
    public boolean deleteDtbUserFlashCardByCardIds(List<Long> cardIds);

    /**
     * AI生成助记卡
     *
     * @param sourceText 选中的文本内容
     * @param cardType 卡片类型：1简答题/2选择题/3主题卡片
     * @param userId 用户ID
     * @return 生成的助记卡预览数据（未保存到数据库）
     */
    public DtbUserFlashCard generateFlashCard(String sourceText, Integer cardType, Long userId);

}
