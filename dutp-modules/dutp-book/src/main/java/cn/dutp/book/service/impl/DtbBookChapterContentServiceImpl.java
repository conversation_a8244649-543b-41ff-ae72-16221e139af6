package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbBookChapterCatalog;
import cn.dutp.book.domain.DtbBookChapterContent;
import cn.dutp.book.domain.DtbUserBook;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.ChapterQueryVO;
import cn.dutp.book.domain.vo.ChapterStatVO;
import cn.dutp.book.mapper.DtbBookChapterCatalogMapper;
import cn.dutp.book.mapper.DtbBookChapterMapper;
import cn.dutp.book.mapper.DtbBookMapper;
import cn.dutp.book.mapper.DtbUserBookMapper;
import cn.dutp.book.service.IDtbBookChapterContentService;
import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数字教材章节目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Slf4j
@Service
public class DtbBookChapterContentServiceImpl extends ServiceImpl<DtbBookChapterMapper, DtbBookChapter> implements IDtbBookChapterContentService {
    @Autowired
    private MongoService mongoService;
    @Autowired
    private DtbBookChapterMapper dtbBookChapterMapper;
    @Autowired
    private DtbBookMapper dtbBookMapper;
    @Autowired
    private DtbBookChapterCatalogMapper dtbBookChapterCatalogMapper;
    @Autowired
    private DtbUserBookMapper dtbUserBookMapper;
    public static final String BOOK_CHAPTER_CONTENT = "bookChapterContent";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 查询章节内容
     *
     * @param chapterId 章节id
     * @return 章节内容
     */
    @Override
    public DtbBookChapterContent getChapterContentInfo(Long chapterId) {
        // 查询mongodbId
        // DtbBookChapter dtbBookChapter = dtbBookChapterMapper.selectOne(new LambdaQueryWrapper<DtbBookChapter>()
        //         .select(DtbBookChapter::getChapterId, DtbBookChapter::getChapterObjectId)
        //         .eq(DtbBookChapter::getChapterId, chapterId));
        Long chapter = dtbBookChapterMapper.queryChapter(chapterId);
        if (ObjectUtil.isEmpty(chapter)) {
            throw new ServiceException("章节不存在");
        }
        // TODO 暂时没想好怎么更新OjectId到数据库
        // if (ObjectUtil.isEmpty(dtbBookChapter.getChapterObjectId())){
        //     return new DtbBookChapterContent();
        // }
        // 查询章节内容
        Query query = new Query(Criteria.where("chapterId").is(chapterId));
        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
        return chapterContent;
    }


    @Override
    public AjaxResult getQueryChapterContent(ReaderCommonForm readerCommonForm) {
        Long chapterId = readerCommonForm.getChapterId();
        Long bookId = readerCommonForm.getBookId();
        String keyword = readerCommonForm.getKeyword();

        List<DtbBookChapter> chapterList = dtbBookChapterMapper.queryBookChapterListByBookId(bookId);

        List<Long> chapterIds = chapterList.stream().map(DtbBookChapter::getChapterId).collect(Collectors.toList());
        // JS函数 用于mongo检索逻辑
        String jsFunction = String.format(
                "function(contentStr,chapterId) {" +
                        "  try {" +
                        "    var parsed = JSON.parse(contentStr);" +
                        "    var keyword = '%s';" +
                        "    var results = [];" +
                        "    var stack = [{ node: parsed, ancestors: [] }];" +
                        "    while (stack.length > 0) {" +
                        "      var { node, ancestors } = stack.pop();" +
                        "      if (!node) continue;" +
                        "      var pageValue = null;" +
                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
                        "        if (ancestors[i].attrs && ancestors[i].attrs.pageNumber) {" +
                        "          pageValue = ancestors[i].attrs.pageNumber;" +
                        "          break;" +
                        "        }" +
                        "      }" +
                        "      var idValue = null;" +
                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
                        "        if (ancestors[i] && ancestors[i].attrs && ancestors[i].attrs.id) {" +
                        "          idValue = ancestors[i].attrs.id;" +
                        "          break;" +
                        "        }" +
                        "      }" +
                        "      if (node.text && node.text.includes(keyword)) {" +
                        "        var sentences = node.text.split(/[。！？]/);" +
                        "        sentences.forEach(function(sentence) {" +
                        "          if (sentence.includes(keyword)) {" +
                        "            results.push({" +
                        "              chapterId: chapterId," +
                        "              sentence: sentence.trim()," +
                        "              pageNumber: pageValue != null ? (pageValue | 0) : null," +
                        "              textId: idValue" +
                        "            });" +
                        "          }" +
                        "        });" +
                        "      }" +
                        "       if (node.content && node.content.length > 0) {" +
                        "         var newAncestors = ancestors.concat([node]);" +
                        "         for (var j = node.content.length - 1; j >= 0; j--) {" +
                        "           stack.push({ " +
                        "             node: node.content[j], " +
                        "             ancestors: newAncestors " +
                        "           });" +
                        "         }" +
                        "       }" +
                        "     }" +
                        "    return results;" +
                        "  } catch(e) { return []; }" +
                        "}",
                keyword.replace("'", "\\'")
        );


        // 构建自定义投影
        AggregationOperation customProject = context -> new Document("$project",
                new Document("chapterId", 1)
                        .append("content", 1)
                        .append("matchedData", new Document("$function",
                                new Document("body", jsFunction)
                                        .append("args", Arrays.asList("$content", "$chapterId"))
                                        .append("lang", "js")
                        ))
        );

        // 构建管道 包含两个查询逻辑 当前章节的内容和其他章节匹配到的数量
        AggregationOperation facetOperation = context -> new Document("$facet",
                new Document("currentChapterResults", Arrays.asList(
                        new Document("$match", new Document("chapterId", chapterId)),
                        new Document("$unwind", "$matchedData"),
                        new Document("$replaceRoot", new Document("newRoot", "$matchedData"))
                ))
                        .append("otherChaptersCounts", Arrays.asList(
                                new Document("$match", new Document("chapterId", new Document("$in", chapterIds))
                                        .append("chapterId", new Document("$ne", chapterId))),
                                new Document("$unwind", "$matchedData"),
                                new Document("$group", new Document("_id", "$chapterId")
                                        .append("count", new Document("$sum", 1))
                                )
                        ))
        );

        // 组合完整的查询条件
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("chapterId").in(chapterIds)),
                customProject,
                facetOperation
        );


        // 执行查询
        AggregationResults<Document> results = mongoTemplate.aggregate(
                aggregation, "bookChapterContent", Document.class
        );

        // 结果解析
        Document resultDoc = results.getMappedResults().get(0);
        List<ChapterQueryVO> currentResults = parseCurrentResults(resultDoc.get("currentChapterResults", List.class));
        // 处理otherStat数据 遍历chapterList 拼装
        List<ChapterStatVO> otherStats = parseOtherStats(resultDoc.get("otherChaptersCounts", List.class), chapterList);
        return AjaxResult.success().put("current", currentResults).put("others", otherStats);
    }

    @Override
    public Map<String, Object> getAllQueryChapterContent(ReaderCommonForm readerCommonForm) {
        Map<String, Object> result = new HashMap<>();
        String keyWord = readerCommonForm.getKeyword();
        if (StringUtils.isBlank(keyWord)) {
            throw new ServiceException("关键词不能为空");
        }
        // 数量
        Integer total = 0;
        // 关键字内容
        Map<Long, String> contentMap = new HashMap<>();
        List<DtbBook> res = new ArrayList<>();
        QueryWrapper<DtbBook> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DtbBook::getDelFlag, 0)
                .eq(DtbBook::getBookOrganize, 1)
                .ne(DtbBook::getMasterFlag, 3)
                .eq(DtbBook::getShelfState, 1);
        List<DtbBook> allBook = dtbBookMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(allBook)) {
            List<DtbBookChapter> allChapter = dtbBookChapterMapper.selectByBookAndVersion(allBook);
            if (!CollectionUtils.isEmpty(allChapter)) {
                Map<Long, List<DtbBookChapter>> chapterMap = allChapter.stream()
                        .collect(Collectors.groupingBy(DtbBookChapter::getBookId));
                ReaderCommonForm form = new ReaderCommonForm();
                form.setKeyword(keyWord);
                outerLoop:
                for (Map.Entry<Long, List<DtbBookChapter>> entry : chapterMap.entrySet()) {
                    Long bookId = entry.getKey();
                    List<DtbBookChapter> itemList = entry.getValue();
                    for (DtbBookChapter chapter : itemList) {
                        form.setChapterId(chapter.getChapterId());
                        form.setBookId(bookId);
                        Document content = getChapterByKeyword(form);
                        DtbBook dtbBook = allBook.stream().filter(item -> item.getBookId().equals(bookId)).findFirst().orElse(null);
                        analysisDocument(content, dtbBook);
                        if (StringUtils.isNotBlank(dtbBook.getKeyContent())) {
                            total++;
                            res.add(dtbBook);
                            continue outerLoop;
                        }
                    }
                }

            }
        }
        result.put("total", total);
        result.put("contentMap", contentMap);
        result.put("res", res);
        return result;
    }

    private DtbBook analysisDocument(Document content, DtbBook dtbBook) {
        //                        Document firstMatchedData = matchedDataList.get(0);
        List<Document> matchedDataList = (List<Document>) content.get("matchedData");
        if (matchedDataList.size() > 0) {

            if (!matchedDataList.isEmpty()) {
                Document firstMatchedData = matchedDataList.get(0);
                String sentence = firstMatchedData.getString("sentence");
                dtbBook.setKeyContent(sentence);
                dtbBook.setPageNumber(Math.round(firstMatchedData.getDouble("pageNumber")));
                dtbBook.setChapterId(firstMatchedData.getLong("chapterId"));
                dtbBook.setTextId(firstMatchedData.getString("textId"));
            }
            ;
        }
        return dtbBook;
    }

    private Document getChapterByKeyword(ReaderCommonForm form) {
        // JS函数 用于mongo检索逻辑
        String jsFunction = String.format(
                "function(contentStr, chapterId) {" +
                        "  try {" +
                        "    var parsed = JSON.parse(contentStr);" +
                        "    var keyword = '%s';" +
                        "    var results = [];" +
                        "    var stack = [{ node: parsed, ancestors: [] }];" +
                        "    while (stack.length > 0) {" +
                        "      var { node, ancestors } = stack.pop();" +
                        "      if (!node) continue;" +
                        "      var pageValue = null;" +
                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
                        "        if (ancestors[i].attrs && ancestors[i].attrs.pageNumber) {" +
                        "          pageValue = ancestors[i].attrs.pageNumber;" +
                        "          break;" +
                        "        }" +
                        "      }" +
                        "      var idValue = null;" +
                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
                        "        if (ancestors[i] && ancestors[i].attrs && ancestors[i].attrs.id) {" +
                        "          idValue = ancestors[i].attrs.id;" +
                        "          break;" +
                        "        }" +
                        "      }" +
                        "      if (node.text && node.text && node.text.includes(keyword)) {" +
                        "        var sentences = node.text.split(/[。！？]/);" +
                        "        for (var i = 0; i < sentences.length; i++) {" +
                        "          if (sentences[i].includes(keyword)) {" +
                        "            results.push({" +
                        "              chapterId: chapterId," +
                        "              sentence: sentences[i].trim()," +
                        "              pageNumber: pageValue != null ? (pageValue | 0) : null," +
                        "              textId: idValue" +
                        "            });" +
                        "            break;" +
                        "          }" +
                        "        }" +
                        "      }" +
                        "      if (node.content && node.content.length > 0) {" +
                        "        var newAncestors = ancestors.concat([node]);" +
                        "        for (var j = node.content.length - 1; j >= 0; j--) {" +
                        "          stack.push({ " +
                        "            node: node.content[j], " +
                        "            ancestors: newAncestors " +
                        "          });" +
                        "        }" +
                        "      }" +
                        "    }" +
                        "    return results;" +
                        "  } catch(e) { return []; }" +
                        "}",
                form.getKeyword().replace("'", "\\'")
        );

        // 构建自定义投影
        AggregationOperation customProject = context -> new Document("$project",
                new Document("chapterId", 1)
                        .append("content", 1)
                        .append("matchedData", new Document("$function",
                                new Document("body", jsFunction)
                                        .append("args", Arrays.asList("$content", "$chapterId"))
                                        .append("lang", "js")
                        ))
        );

        // 组合完整的查询条件
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("chapterId").is(form.getChapterId())),
                customProject,
                Aggregation.match(Criteria.where("matchedData").ne(null)),
                Aggregation.limit(1) // 只取第一条结果
        );

        // 执行查询
        AggregationResults<Document> results = mongoTemplate.aggregate(
                aggregation, "bookChapterContent", Document.class
        );

        // 结果解析
        if (!results.getMappedResults().isEmpty()) {
            Document resultDoc = results.getMappedResults().get(0);
            return resultDoc; // 直接返回查询到的整个对象
        }
        return null; // 如果没有找到结果
    }

//    private String getChapterByKeyword(ReaderCommonForm form) {
//        // JS函数 用于mongo检索逻辑
//        String jsFunction = String.format(
//                "function(contentStr, chapterId) {" +
//                        "  try {" +
//                        "    var parsed = JSON.parse(contentStr);" +
//                        "    var keyword = '%s';" +
//                        "    var results = [];" +
//                        "    var stack = [{ node: parsed, ancestors: [] }];" +
//                        "    while (stack.length > 0) {" +
//                        "      var { node, ancestors } = stack.pop();" +
//                        "      if (!node) continue;" +
//                        "      var pageValue = null;" +
//                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
//                        "        if (ancestors[i].attrs && ancestors[i].attrs.pageNumber) {" +
//                        "          pageValue = ancestors[i].attrs.pageNumber;" +
//                        "          break;" +
//                        "        }" +
//                        "      }" +
//                        "      var idValue = null;" +
//                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
//                        "        if (ancestors[i] && ancestors[i].attrs && ancestors[i].attrs.id) {" +
//                        "          idValue = ancestors[i].attrs.id;" +
//                        "          break;" +
//                        "        }" +
//                        "      }" +
//                        "      if (node.text && node.text && node.text.includes(keyword)) {" +
//                        "        var sentences = node.text.split(/[。！？]/);" +
//                        "        for (var i = 0; i < sentences.length; i++) {" +
//                        "          if (sentences[i].includes(keyword)) {" +
//                        "            results.push({" +
//                        "              chapterId: chapterId," +
//                        "              sentence: sentences[i].trim()," +
//                        "              pageNumber: pageValue != null ? (pageValue | 0) : null," +
//                        "              textId: idValue" +
//                        "            });" +
//                        "            break;" +
//                    "          }" +
//                    "        }" +
//                    "      }" +
//                    "      if (node.content && node.content.length > 0) {" +
//                    "        var newAncestors = ancestors.concat([node]);" +
//                    "        for (var j = node.content.length - 1; j >= 0; j--) {" +
//                    "          stack.push({ " +
//                    "            node: node.content[j], " +
//                    "            ancestors: newAncestors " +
//                    "          });" +
//                    "        }" +
//                    "      }" +
//                    "    }" +
//                    "    return results;" +
//                    "  } catch(e) { return []; }" +
//                    "}",
//                    form.getKeyword().replace("'", "\\'")
//    );
//
//            // 构建自定义投影
//            AggregationOperation customProject = context -> new Document("$project",
//                    new Document("chapterId", 1)
//                            .append("content", 1)
//                            .append("matchedData", new Document("$function",
//                                    new Document("body", jsFunction)
//                                            .append("args", Arrays.asList("$content", "$chapterId"))
//                                            .append("lang", "js")
//                            ))
//            );
//
//            // 组合完整的查询条件
//            Aggregation aggregation = Aggregation.newAggregation(
//                    Aggregation.match(Criteria.where("chapterId").is(form.getChapterId())),
//                    customProject,
//                    Aggregation.match(Criteria.where("matchedData").ne(null)),
//                    Aggregation.limit(1) // 只取第一条结果
//            );
//
//            // 执行查询
//            AggregationResults<Document> results = mongoTemplate.aggregate(
//                    aggregation, "bookChapterContent", Document.class
//            );
//
//            // 结果解析
//            if (!results.getMappedResults().isEmpty()) {
//                Document resultDoc = results.getMappedResults().get(0);
//                if (ObjectUtil.isEmpty(resultDoc.get("matchedData"))) {
//                    return null;
//                } else {
//                    List<Document> matchedDataList = (List<Document>) resultDoc.get("matchedData");
//                    if (!matchedDataList.isEmpty()) {
//                        Document firstMatchedData = matchedDataList.get(0);
//                        String sentence = firstMatchedData.getString("sentence");
//                        return sentence;
//                    };
//
////                    Object object = resultDoc.get("matchedData");
////                    ObjectMapper objectMapper = new ObjectMapper();
////                    Map<String, Object> matchedData = objectMapper.convertValue(object, Map.class);
////                    return matchedData.get("sentence").toString();
//                }
////                if (matchedData != null) {
////                    String sentence = matchedData.getString("sentence");
////                    return sentence; // 返回找到的句子
////                }
//            }
//            return null; // 如果没有找到结果
//        }


//    private String getChapterByKeyword(ReaderCommonForm form) {
//        // JS函数 用于mongo检索逻辑
//        String jsFunction = String.format(
//                "function(contentStr,chapterId) {" +
//                        "  try {" +
//                        "    var parsed = JSON.parse(contentStr);" +
//                        "    var keyword = '%s';" +
//                        "    var results = [];" +
//                        "    var stack = [{ node: parsed, ancestors: [] }];" +
//                        "    while (stack.length > 0) {" +
//                        "      var { node, ancestors } = stack.pop();" +
//                        "      if (!node) continue;" +
//                        "      var pageValue = null;" +
//                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
//                        "        if (ancestors[i].attrs && ancestors[i].attrs.pageNumber) {" +
//                        "          pageValue = ancestors[i].attrs.pageNumber;" +
//                        "          break;" +
//                        "        }" +
//                        "      }" +
//                        "      var idValue = null;" +
//                        "      for (var i = ancestors.length - 1; i >= 0; i--) {" +
//                        "        if (ancestors[i] && ancestors[i].attrs && ancestors[i].attrs.id) {" +
//                        "          idValue = ancestors[i].attrs.id;" +
//                        "          break;" +
//                        "        }" +
//                        "      }" +
//                        "      if (node.text && node.text.includes(keyword)) {" +
//                        "        var sentences = node.text.split(/[。！？]/);" +
//                        "        sentences.forEach(function(sentence) {" +
//                        "          if (sentence.includes(keyword)) {" +
//                        "            results.push({" +
//                        "              chapterId: chapterId," +
//                        "              sentence: sentence.trim()," +
//                        "              pageNumber: pageValue != null ? (pageValue | 0) : null," +
//                        "              textId: idValue" +
//                        "            });" +
//                        "          }" +
//                        "        });" +
//                        "      }" +
//                        "       if (node.content && node.content.length > 0) {" +
//                        "         var newAncestors = ancestors.concat([node]);" +
//                        "         for (var j = node.content.length - 1; j >= 0; j--) {" +
//                        "           stack.push({ " +
//                        "             node: node.content[j], " +
//                        "             ancestors: newAncestors " +
//                        "           });" +
//                        "         }" +
//                        "       }" +
//                        "     }" +
//                        "    return results;" +
//                        "  } catch(e) { return []; }" +
//                        "}",
//                form.getKeyword().replace("'", "\\'")
//        );
//
//        // 构建自定义投影
//        AggregationOperation customProject = context -> new Document("$project",
//                new Document("chapterId", 1)
//                        .append("content", 1)
//                        .append("matchedData", new Document("$function",
//                                new Document("body", jsFunction)
//                                        .append("args", Arrays.asList("$content", "$chapterId"))
//                                        .append("lang", "js")
//                        ))
//        );
//
//        // 组合完整的查询条件
//        Aggregation aggregation = Aggregation.newAggregation(
//                Aggregation.match(Criteria.where("chapterId").is(form.getChapterId())),
//                customProject,
//                Aggregation.match(Criteria.where("matchedData").ne(null)),
//                Aggregation.limit(1)
//        );
//        // 执行查询
//        AggregationResults<Document> results = mongoTemplate.aggregate(
//                aggregation, "bookChapterContent", Document.class
//        );
//        // 结果解析
//        if (!results.getMappedResults().isEmpty()) {
//            Document resultDoc = results.getMappedResults().get(0);
//            Document matchedData = resultDoc.get("matchedData", Document.class);
//            if (matchedData != null) {
//                String sentence = matchedData.getString("sentence");
//                return sentence;
//            }
//        }
//        return null;
//    }

    // 结果解析辅助方法
    private List<ChapterQueryVO> parseCurrentResults(List<Document> docs) {
        return docs.stream()
                .map(doc -> mongoTemplate.getConverter().read(ChapterQueryVO.class, doc))
                .collect(Collectors.toList());
    }

    private List<ChapterStatVO> parseOtherStats(List<Document> docs, List<DtbBookChapter> chapterList) {

        Map<Long, String> chapterNameMap = chapterList.stream()
                .collect(Collectors.toMap(
                        DtbBookChapter::getChapterId,
                        DtbBookChapter::getChapterName,
                        (oldValue, newValue) -> oldValue
                ));

        return docs.stream()
                .map(doc -> {
                    Long chapterId = doc.getLong("_id");
                    ChapterStatVO vo = new ChapterStatVO(chapterId, doc.getInteger("count"), "");
                    // 匹配章节名称
                    vo.setChapterName(chapterNameMap.getOrDefault(chapterId, "未知章节"));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 修改数字教材章节目录
     *
     * @param dtbBookChapterContent 数字教材章节目录
     * @return 结果
     */
    @Override
    public boolean updateChapterContentInfo(DtbBookChapterContent dtbBookChapterContent) {
        Long chapterId = dtbBookChapterContent.getChapterId();
        if (ObjectUtil.isEmpty(chapterId)) {
            throw new ServiceException("章节id不能为空");
        }
        DtbBookChapter dtbBookChapter = dtbBookChapterMapper.selectOne(new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getChapterId, DtbBookChapter::getBookId, DtbBookChapter::getVersionId)
                .eq(DtbBookChapter::getChapterId, chapterId));
        // 删除之前数据
        LambdaQueryWrapper<DtbBookChapterCatalog> lambdaDeleteQueryWrapper = new LambdaQueryWrapper<DtbBookChapterCatalog>()
                .eq(DtbBookChapterCatalog::getChapterId, chapterId);
        dtbBookChapterCatalogMapper.delete(lambdaDeleteQueryWrapper);
        Long bookId = dtbBookChapter.getBookId();
        Long versionId = dtbBookChapter.getVersionId();
        String contentJson = dtbBookChapterContent.getContent();
        JSONObject jsonObject = JSONUtil.parseObj(contentJson);
        JSONArray pageList = jsonObject.getJSONArray("content");
        LinkedList<Long> catalogIdList = new LinkedList<>();
        LinkedList<JSONObject> stack = new LinkedList<>();
        LinkedList<JSONObject> nodeStack = new LinkedList<>();
        int pageNumber = 0;
        for (Object page : pageList) {
            JSONObject pageObj = (JSONObject) page;
            JSONArray contentList = pageObj.getJSONArray("content");
            if (ObjectUtil.isEmpty(contentList)) {
                continue;
            }
            pageNumber++;
            for (Object content : contentList) {
                JSONObject contentObj = (JSONObject) content;
                nodeStack.add(contentObj);
                while (!nodeStack.isEmpty()) {
                    contentObj = nodeStack.removeFirst();
                    String type = contentObj.getStr("type");
                    // content为行内节点的块节点没必要往下递归
                    if ("paragraph".equals(type)) {
                        continue;
                    }

                    JSONObject attrs = contentObj.getJSONObject("attrs");
                    if ("heading".equals(type) || "jointHeader".equals(type) || ("imageLayout".equals(type) && !attrs.getBool("isNotAsCatalog", true))) {

                        Integer level = attrs.getInt("level");
                        String domId = attrs.getStr("id");

                        String text = getChapterCatalog(contentObj, type);


                        while (!stack.isEmpty() && stack.getFirst().getJSONObject("attrs").getInt("level", 1) >= level) {
                            stack.removeFirst();
                            catalogIdList.removeFirst();
                        }

                        Long parentId = 0l;
                        if (stack.isEmpty()) {
                            parentId = 0l;
                        } else {
                            parentId = catalogIdList.getFirst();
                        }

                        // 更新数据库
                        DtbBookChapterCatalog dtbBookChapterCatalog = new DtbBookChapterCatalog();
                        dtbBookChapterCatalog.setTitle(text);
                        dtbBookChapterCatalog.setParentId(parentId);
                        dtbBookChapterCatalog.setPageNumber(pageNumber);
                        dtbBookChapterCatalog.setChapterId(chapterId);
                        dtbBookChapterCatalog.setBookId(bookId);
                        dtbBookChapterCatalog.setVersionId(versionId);
                        dtbBookChapterCatalog.setDomId(domId);
                        dtbBookChapterCatalogMapper.insert(dtbBookChapterCatalog);

                        stack.addFirst(contentObj);
                        catalogIdList.addFirst(dtbBookChapterCatalog.getCatalogId());
                    }

                    JSONArray nodeList = contentObj.getJSONArray("content");
                    if (ObjectUtil.isEmpty(nodeList)) {
                        continue;
                    }
                    for (Object o : nodeList) {
                        nodeStack.addFirst((JSONObject) o);
                    }
                }

            }
        }

        // 更新总页数
        DtbBookChapter bookChapter = new DtbBookChapter();
        bookChapter.setChapterId(chapterId);
        bookChapter.setChapterTotalPage(pageList.size());
        dtbBookChapterMapper.updateById(bookChapter);

        Update update = new Update();
        update.set("chapterId", chapterId);
        update.set("content", contentJson);
        mongoService.updateOne(BOOK_CHAPTER_CONTENT, new Query(Criteria.where("chapterId").is(chapterId)), update, DtbBookChapterContent.class);
        return true;
    }

    /**
     * 获取标题文本
     *
     * @param contentObj
     * @param type
     * @return
     */
    private String getChapterCatalog(JSONObject contentObj, String type) {
        String text = "";
        if ("jointHeader".equals(type) || "imageLayout".equals(type)) {
            contentObj = contentObj.getJSONArray("content").getJSONObject(0);
        }
        JSONArray jsonArray = contentObj.getJSONArray("content");
        if (ObjectUtil.isNotEmpty(jsonArray)) {
            for (Object head : jsonArray) {
                JSONObject headText = (JSONObject) head;
                String headerTextStr = headText.getStr("text");
                if (ObjectUtil.isNotEmpty(headerTextStr) && !"null".equals(headerTextStr)) {
                    text += headerTextStr;
                }
            }
        }
        return text;
    }

    @Override
    public AjaxResult getChapterContentByChapterId(Long chapterId) {
        // 查询章节数据
        DtbBookChapter dtbBookChapter = dtbBookChapterMapper.selectOne(new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getChapterId, DtbBookChapter::getChapterObjectId, DtbBookChapter::getBookId, DtbBookChapter::getFree)
                .eq(DtbBookChapter::getChapterId, chapterId));
        if (ObjectUtil.isEmpty(dtbBookChapter)) {
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "章节不存在");
        }
        LambdaQueryWrapper<DtbBook> bookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bookLambdaQueryWrapper.eq(DtbBook::getBookId, dtbBookChapter.getBookId())
                .eq(DtbBook::getPublishStatus, 2);
        DtbBook dtbBook = dtbBookMapper.selectOne(bookLambdaQueryWrapper);
        if (ObjectUtil.isEmpty(dtbBook)) {
            return AjaxResult.error(HttpStatus.NO_BOOK, "教材不存在或已下架");
        }
        // 收费章节没有购买报错。
        if (dtbBookChapter.getFree().intValue() == 1 && dtbBook.getMasterFlag() != 3) {
            LambdaQueryWrapper<DtbUserBook> userBookLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userBookLambdaQueryWrapper.eq(DtbUserBook::getUserId, SecurityUtils.getUserId())
                    .eq(DtbUserBook::getBookId, dtbBookChapter.getBookId())
                    .gt(DtbUserBook::getExpireDate, new Date());
            DtbUserBook userBook = dtbUserBookMapper.selectOne(userBookLambdaQueryWrapper);
            if (ObjectUtil.isEmpty(userBook)) {
                return AjaxResult.error(HttpStatus.NO_BUY, "没有购买教材或已过期");
            }
        }
        int startPageNumber = dtbBookChapterMapper.getBookChapterStartPageNumber(chapterId, dtbBookChapter.getBookId());
        // 查询mongodbId
        Query query = new Query(Criteria.where("chapterId").is(chapterId));
        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
        if (chapterContent == null) {
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "没有可读的内容");
        }
        chapterContent.setStartPageNumber(startPageNumber);
        return AjaxResult.success(chapterContent);
    }

    @Override
    public AjaxResult getChapterContentSimpleByChapterId(Long chapterId) {
        // 查询章节数据
        DtbBookChapter dtbBookChapter = dtbBookChapterMapper.selectOne(new LambdaQueryWrapper<DtbBookChapter>()
                .select(DtbBookChapter::getChapterId, DtbBookChapter::getChapterObjectId, DtbBookChapter::getBookId, DtbBookChapter::getFree)
                .eq(DtbBookChapter::getChapterId, chapterId));
        if (ObjectUtil.isEmpty(dtbBookChapter)) {
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "章节不存在");
        }
        LambdaQueryWrapper<DtbBook> bookLambdaQueryWrapper = new LambdaQueryWrapper<>();
        bookLambdaQueryWrapper.eq(DtbBook::getBookId, dtbBookChapter.getBookId());
        DtbBook dtbBook = dtbBookMapper.selectOne(bookLambdaQueryWrapper);
        int startPageNumber = dtbBookChapterMapper.getBookChapterStartPageNumber(chapterId, dtbBookChapter.getBookId());
        // 查询mongodbId
        Query query = new Query(Criteria.where("chapterId").is(chapterId));
        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
        if (chapterContent == null) {
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "没有可读的内容");
        }
        chapterContent.setStartPageNumber(startPageNumber);
        return AjaxResult.success(chapterContent);
    }
}
