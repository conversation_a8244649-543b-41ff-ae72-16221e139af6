package cn.dutp.book.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserFlashCardMapper;
import cn.dutp.book.domain.DtbUserFlashCard;
import cn.dutp.book.service.IDtbUserFlashCardService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.JsonArray;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 助记卡Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DtbUserFlashCardServiceImpl extends ServiceImpl<DtbUserFlashCardMapper, DtbUserFlashCard> implements IDtbUserFlashCardService
{
    private static final Logger logger = LoggerFactory.getLogger(DtbUserFlashCardServiceImpl.class);
    
    @Autowired
    private DtbUserFlashCardMapper dtbUserFlashCardMapper;

    @Value("${ai.chatCompletionsUrl}")
    private String chatCompletionsUrl;

    @Value("${ai.apiPassword}")
    private String apiPassword;

    /**
     * 查询助记卡
     *
     * @param cardId 助记卡主键
     * @return 助记卡
     */
    @Override
    public DtbUserFlashCard selectDtbUserFlashCardByCardId(Long cardId)
    {
        return this.getById(cardId);
    }

    /**
     * 查询助记卡列表
     *
     * @param dtbUserFlashCard 助记卡
     * @return 助记卡
     */
    @Override
    public List<DtbUserFlashCard> selectDtbUserFlashCardList(DtbUserFlashCard dtbUserFlashCard)
    {
        LambdaQueryWrapper<DtbUserFlashCard> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getUserId
                ,dtbUserFlashCard.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getBookId
                ,dtbUserFlashCard.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getChapterId())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getChapterId
                ,dtbUserFlashCard.getChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getPageNumber())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getPageNumber
                ,dtbUserFlashCard.getPageNumber());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getSourceText())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getSourceText
                ,dtbUserFlashCard.getSourceText());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getCardType())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getCardType
                ,dtbUserFlashCard.getCardType());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getQuestion())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getQuestion
                ,dtbUserFlashCard.getQuestion());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getOptions())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getOptions
                ,dtbUserFlashCard.getOptions());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getAnswer())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getAnswer
                ,dtbUserFlashCard.getAnswer());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getMemoryStatus())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getMemoryStatus
                ,dtbUserFlashCard.getMemoryStatus());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getReviewCount())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getReviewCount
                ,dtbUserFlashCard.getReviewCount());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getLastReviewTime())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getLastReviewTime
                ,dtbUserFlashCard.getLastReviewTime());
            }
                if(ObjectUtil.isNotEmpty(dtbUserFlashCard.getNextReviewTime())) {
                lambdaQueryWrapper.eq(DtbUserFlashCard::getNextReviewTime
                ,dtbUserFlashCard.getNextReviewTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增助记卡
     *
     * @param dtbUserFlashCard 助记卡
     * @return 结果
     */
    @Override
    public boolean insertDtbUserFlashCard(DtbUserFlashCard dtbUserFlashCard)
    {
        return this.save(dtbUserFlashCard);
    }

    /**
     * 修改助记卡
     *
     * @param dtbUserFlashCard 助记卡
     * @return 结果
     */
    @Override
    public boolean updateDtbUserFlashCard(DtbUserFlashCard dtbUserFlashCard)
    {
        return this.updateById(dtbUserFlashCard);
    }

    /**
     * 批量删除助记卡
     *
     * @param cardIds 需要删除的助记卡主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserFlashCardByCardIds(List<Long> cardIds)
    {
        return this.removeByIds(cardIds);
    }

    /**
     * AI生成助记卡
     *
     * @param sourceText 选中的文本内容
     * @param cardType 卡片类型：1简答题/2选择题/3主题卡片
     * @param userId 用户ID
     * @return 生成的助记卡预览数据（未保存到数据库）
     */
    @Override
    public DtbUserFlashCard generateFlashCard(String sourceText, Integer cardType, Long userId) {
        try {
            // 根据卡片类型动态构建提示词
            String systemPrompt = "";
            String outputFormat = "";
            
            switch (cardType) {
                case 1: // 简答题
                    systemPrompt = "你是一个专业的教育助手。请根据用户提供的学习内容，提取关键概念生成一个简答题。" +
                        "要求问题清晰明确，答案简洁准确，便于记忆和复习。";
                    outputFormat = "{\n  \"question\": \"问题内容\",\n  \"answer\": \"答案内容\",\n  \"options\": \"\"\n}";
                    break;
                case 2: // 选择题
                    systemPrompt = "你是一个专业的教育助手。请根据用户提供的学习内容生成一个选择题。" +
                        "要求生成4个选项(A、B、C、D)，确保只有1个正确答案，其他3个为合理的干扰项。";
                    outputFormat = "{\n  \"question\": \"问题内容\",\n  \"answer\": \"正确答案\",\n  \"options\": \"[{\\\"key\\\":\\\"A\\\",\\\"value\\\":\\\"选项A\\\"},{\\\"key\\\":\\\"B\\\",\\\"value\\\":\\\"选项B\\\"},{\\\"key\\\":\\\"C\\\",\\\"value\\\":\\\"选项C\\\"},{\\\"key\\\":\\\"D\\\",\\\"value\\\":\\\"选项D\\\"}]\"\n}";
                    break;
                case 3: // 主题卡片
                    systemPrompt = "你是一个专业的教育助手。请根据用户提供的学习内容总结核心主题，" +
                        "提供全面的知识要点，帮助用户理解和记忆。";
                    outputFormat = "{\n  \"question\": \"主题标题\",\n  \"answer\": \"知识要点总结\",\n  \"options\": \"\"\n}";
                    break;
                default:
                    throw new IllegalArgumentException("不支持的卡片类型：" + cardType);
            }
            
            systemPrompt += "\n\n请严格按照以下JSON格式输出，不要添加任何其他文字：\n" + outputFormat;

            // 根据卡片类型构建用户问题
            String cardTypeText = "";
            switch (cardType) {
                case 1:
                    cardTypeText = "简答题";
                    break;
                case 2:
                    cardTypeText = "选择题";
                    break;
                case 3:
                    cardTypeText = "主题卡片";
                    break;
            }
            
            String userQuestion = String.format("请为以下内容生成一个%s类型的助记卡：\n\n%s\n\n卡片类型：%s", 
                cardTypeText, sourceText, cardType);

            // 构建AI请求体
            JsonObject requestBody = new JsonObject();
            requestBody.addProperty("model", "generalv3.5");
            requestBody.addProperty("user", userId.toString());

            JsonArray messages = new JsonArray();
            
            JsonObject systemMessage = new JsonObject();
            systemMessage.addProperty("role", "system");
            systemMessage.addProperty("content", systemPrompt);
            messages.add(systemMessage);

            JsonObject userMessage = new JsonObject();
            userMessage.addProperty("role", "user");
            userMessage.addProperty("content", userQuestion);
            messages.add(userMessage);

            requestBody.add("messages", messages);
            requestBody.addProperty("temperature", 0.5);
            requestBody.addProperty("max_tokens", 1024);
            
            JsonObject responseFormat = new JsonObject();
            responseFormat.addProperty("type", "json_object");
            requestBody.add("response_format", responseFormat);

            // 发送HTTP请求
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiPassword);

            HttpEntity<String> entity = new HttpEntity<>(requestBody.toString(), headers);
            ResponseEntity<String> response = restTemplate.postForEntity(chatCompletionsUrl, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String aiResponse = response.getBody();
                JSONObject responseJson = JSON.parseObject(aiResponse);
                
                if (responseJson.getInteger("code") == 0) {
                    JSONObject choices = responseJson.getJSONArray("choices").getJSONObject(0);
                    String content = choices.getJSONObject("message").getString("content");
                    JSONObject cardContent = JSON.parseObject(content);

                    // 创建助记卡预览对象（不保存到数据库）
                    DtbUserFlashCard flashCard = new DtbUserFlashCard();
                    flashCard.setUserId(userId);
                    flashCard.setSourceText(sourceText);
                    flashCard.setCardType(cardType);
                    flashCard.setQuestion(cardContent.getString("question"));
                    flashCard.setAnswer(cardContent.getString("answer"));
                    
                    // 如果是选择题，设置选项
                    if (cardType == 2) {
                        String options = cardContent.getString("options");
                        if (options != null && !options.trim().isEmpty()) {
                            flashCard.setOptions(options);
                        }
                    }

                    // 直接返回预览数据，不保存到数据库
                    return flashCard;
                    
                } else {
                    throw new RuntimeException("AI生成失败：" + responseJson.getString("message"));
                }
            } else {
                throw new RuntimeException("AI服务请求失败，状态码：" + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("AI生成助记卡失败", e);
            throw new RuntimeException("生成失败：" + e.getMessage());
        }
    }

}
