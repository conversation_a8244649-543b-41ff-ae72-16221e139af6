package cn.dutp.book.service.impl;

import cn.dutp.book.domain.*;
import cn.dutp.book.domain.vo.DtbBookPublishProcessVO;
import cn.dutp.book.mapper.*;
import cn.dutp.book.service.*;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.mongo.service.MongoService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookGroup;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.SysUser;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.print.Book;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import static cn.dutp.book.service.impl.DtbBookChapterContentServiceImpl.BOOK_CHAPTER_CONTENT;

/**
 * DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Service
public class DtbBookPublishProcessServiceImpl extends ServiceImpl<DtbBookPublishProcessMapper, DtbBookPublishProcess> implements IDtbBookPublishProcessService {
    @Autowired
    private DtbBookPublishProcessMapper dtbBookPublishProcessMapper;

    @Autowired
    private DtbBookPublishProcessAuditUserMapper dtbBookPublishProcessAuditUserMapper;

    @Autowired
    private DtbBookBookMapper dtbBookBookMapper;

    @Autowired
    private IDutpTaskService dutpTaskService;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    @Autowired
    private IDtbBookPublishStepService dtbBookPublishStepService;

    @Autowired
    private IDtbBookService dtbBookService;

    @Autowired
    private DtbBookCommonMapper commonMapper;

    @Autowired
    private IDtbUserBookService dtbUserBookService;

    @Autowired
    private IDtbBookGroupService dtbGroupService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
     private DtbBookPublishProcessChapterBackupMapper dtbBookPublishProcessChapterBackupMapper;

    @Autowired
    private MongoService mongoService;

    public static final String BOOK_CHAPTER_CONTENT = "bookChapterContent";
    public static final String BOOK_CHAPTER_CONTENT_BACKUP = "bookChapterContentBackup";

    /**
     * 查询DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param processId DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】主键
     * @return DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     */
    @Override
    public DtbBookPublishProcess selectDtbBookPublishProcessByProcessId(Long processId) {
        return this.getById(processId);
    }

    /**
     * 查询DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】列表
     *
     * @param dtbBookPublishProcess DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     * @return DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     */
    @Override
    public List<DtbBookPublishProcess> selectDtbBookPublishProcessList(DtbBookPublishProcess dtbBookPublishProcess) {
        Long userId = SecurityUtils.getUserId();
        // 获取系统管理员
        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode())
        {
            throw new ServiceException(adminIdList.getMsg());
        }
        boolean isAdmin = false;
        if (!CollectionUtils.isEmpty(adminIdList.getData())) {
            // 查询userId是否包含在adminIdList.getuserId中 如果在的话证明是管理员 isAdmin = true
            if (adminIdList.getData().stream().anyMatch(admin -> admin.getUserId().equals(userId))) {
                isAdmin = true;
            }
        }

        List<DtbBookPublishProcess> tbBookPublishProcessList = this.dtbBookPublishProcessMapper.getBackupList(userId,isAdmin, dtbBookPublishProcess);
        return tbBookPublishProcessList;
    }

    /**
     * 新增DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param dtbBookPublishProcess DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDtbBookPublishProcess(DtbBookPublishProcess dtbBookPublishProcess) {
        try {
            Long userId = SecurityUtils.getUserId();
            this.updateById(dtbBookPublishProcess);

            DtbBookPublishProcessAuditUser nowAuditUser = new DtbBookPublishProcessAuditUser();
            nowAuditUser.setAuditUserId(dtbBookPublishProcess.getAuditUserId());
            nowAuditUser.setHandleUserId(dtbBookPublishProcess.getDealUserId());
            nowAuditUser.setState(dtbBookPublishProcess.getState());
            nowAuditUser.setReason(dtbBookPublishProcess.getReason());
            dtbBookPublishProcessAuditUserMapper.updateById(nowAuditUser);

            DtbBook book = dtbBookService.getById(dtbBookPublishProcess.getBookId());
            if (book == null) {
                throw new ServiceException("书籍不存在");
            }

            handleRecordNo(dtbBookPublishProcess, book);

            if (dtbBookPublishProcess.getState() == 2) {
                handleApproval(dtbBookPublishProcess, book, userId);
            } else if (dtbBookPublishProcess.getState() == 3) {
                handleRejection(dtbBookPublishProcess, book, userId);
            }

            book.setBookId(dtbBookPublishProcess.getBookId());
            dtbBookBookMapper.updateById(book);
        } catch (Exception e) {
            log.error("插入数字教材发布流程失败", e);
            return false;
        }
        return true;
    }

    private void handleRecordNo(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book) {
        if (dtbBookPublishProcess.getState() == 2 && dtbBookPublishProcess.getStepId() == 12 && StringUtils.isEmpty(book.getRecordNo())) {
            book.setRecordNo(getRandomCode());
        }
    }

    /**
     * 审批通过
     * @param dtbBookPublishProcess
     * @param book
     * @param userId
     */
    private void handleApproval(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId) {
        if (dtbBookPublishProcess.getStepId() == 15) { // 发布审核节点
            DtbBookPublishProcess nextProcess = createNextProcess(dtbBookPublishProcess, userId, 16L);
            this.save(nextProcess);

            DtbBookPublishProcessAuditUser auditUser = createAuditUser(nextProcess, userId);
            dtbBookPublishProcessAuditUserMapper.insert(auditUser);

            book.setCurrentStepId(16L);
            book.setPublishStatus(2);
            if (book.getShelfState() == 4){
                book.setShelfState(2);
            }

            handleVersionUpdate(dtbBookPublishProcess, book, userId);
            handleMasterBook(book);
            addTasks(dtbBookPublishProcess, userId);
            sendApprovalMessages(dtbBookPublishProcess, book, userId);
        } else { // 其他节点
            DtbBookPublishProcess nextProcess = dtbBookPublishProcess.getNextProcess();
            if (nextProcess == null) {
                throw new ServiceException("送审信息不能为空");
            }
            nextProcess.setPromoterUserId(userId);
            this.save(nextProcess);

            DtbBookPublishProcessAuditUser auditUser = createAuditUser(nextProcess, nextProcess.getUserId());
            dtbBookPublishProcessAuditUserMapper.insert(auditUser);
            book.setCurrentStepId(nextProcess.getStepId());

            sendNextStepMessage(dtbBookPublishProcess, book, userId, nextProcess);
        }
    }

    /**
     * 审批驳回
     * @param dtbBookPublishProcess
     * @param book
     * @param userId
     */
    private void handleRejection(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId) {
        DtbBookPublishProcess nextProcess = dtbBookPublishProcess.getNextProcess();
        if (nextProcess == null) {
            throw new ServiceException("送审信息不能为空");
        }
        nextProcess.setPromoterUserId(userId);

        DtbBookPublishProcessAuditUser auditUser = new DtbBookPublishProcessAuditUser();
        if (dtbBookPublishProcess.getStepId() == 2) { // 来搞确认节点
            book.setCurrentStepId(2L);
            sendRejectionMessage(dtbBookPublishProcess, book, userId, nextProcess);
        } else { // 其他节点
            this.save(nextProcess);
            auditUser.setProcessId(nextProcess.getProcessId());
            auditUser.setUserId(nextProcess.getUserId());
            auditUser.setDeptId(nextProcess.getDeptId());
            book.setCurrentStepId(nextProcess.getStepId());
            sendNextRejectionMessage(dtbBookPublishProcess, book, userId, nextProcess);
        }
        dtbBookPublishProcessAuditUserMapper.insert(auditUser);


    }

    // 创建下个流程数据
    private DtbBookPublishProcess createNextProcess(DtbBookPublishProcess dtbBookPublishProcess, Long userId, Long stepId) {
        DtbBookPublishProcess nextProcess = new DtbBookPublishProcess();
        nextProcess.setBookId(dtbBookPublishProcess.getBookId());
        nextProcess.setStepId(stepId);
        nextProcess.setState(2);
        nextProcess.setVersionId(dtbBookPublishProcess.getVersionId());
        nextProcess.setReason(dtbBookPublishProcess.getReason());
        nextProcess.setProcessDate(dtbBookPublishProcess.getProcessDate());
        nextProcess.setAdditionFlag(dtbBookPublishProcess.getAdditionFlag());
        nextProcess.setRecordNo(dtbBookPublishProcess.getRecordNo());
        nextProcess.setPromoterUserId(userId);
        nextProcess.setPrevProcessId(dtbBookPublishProcess.getProcessId());
        nextProcess.setUserId(userId);
        nextProcess.setDealUserId(userId);
        return nextProcess;
    }

    /**
     * 新增下个流程审批人数据
     * @param process
     * @param userId
     * @return
     */
    private DtbBookPublishProcessAuditUser createAuditUser(DtbBookPublishProcess process, Long userId) {
        DtbBookPublishProcessAuditUser auditUser = new DtbBookPublishProcessAuditUser();
        auditUser.setUserId(userId);
        auditUser.setHandleUserId(userId);
        auditUser.setProcessId(process.getProcessId());
        auditUser.setState(2);
        auditUser.setReason(process.getReason());
        return auditUser;
    }


    /**
     * 修正数据更新版本
     * @param dtbBookPublishProcess
     * @param book
     * @param userId
     */
    private void handleVersionUpdate(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId) {
        if (!book.getCurrentVersionId().equals(dtbBookPublishProcess.getVersionId())) {
            List<DtbUserBook> dtbUserBookList = dtbUserBookService.list(new LambdaQueryWrapper<DtbUserBook>().eq(DtbUserBook::getBookId, book.getBookId()).gt(DtbUserBook::getExpireDate,new Date()));
            for (DtbUserBook userBook : dtbUserBookList) {
                sendMessage(userId, userBook.getUserId(), "您好，《" + book.getBookName() + "》已更新到新版本。", "教材更新提醒", 3,1,2,null);
            }
            book.setCurrentVersionId(dtbBookPublishProcess.getVersionId());
        }
    }

    /**
     * 副教材更新上架状态 如果主教材是已上架 副教材发版后自动更新为已上架
     * @param book
     */
    private void handleMasterBook(DtbBook book) {
        if (book.getMasterFlag() == 3) {
            DtbBook masterBook = dtbBookService.getById(book.getMasterBookId());
            if (masterBook != null && masterBook.getShelfState() == 1) {
                book.setShelfState(1);
            }
        }
    }

    /**
     * 创建资源统计任务
     * @param dtbBookPublishProcess
     * @param userId
     */
    private void addTasks(DtbBookPublishProcess dtbBookPublishProcess, Long userId) {
        List<DutpTask> dutpTaskList = new ArrayList<>();
        // 去掉归属人 因为这三个任务不在任务中心中显示了
        dutpTaskList.add(createTask(8, "统计章节资源", dtbBookPublishProcess.getBookId(), null));
        dutpTaskList.add(createTask(9, "统计章节试题和作业", dtbBookPublishProcess.getBookId(), null));
        dutpTaskList.add(createTask(11, "教材统计任务", dtbBookPublishProcess.getBookId(), null));
        dutpTaskService.saveBatch(dutpTaskList);
    }

    private DutpTask createTask(int taskType, String taskContent, Long dataId, Long userId) {
        DutpTask task = new DutpTask();
        task.setTaskType(taskType);
        task.setTaskContent(taskContent);
        task.setDataId(dataId);
        task.setUserId(userId);
        task.setTaskRate(0);
        task.setTaskState(0);
        return task;
    }

    /**
     * 发布审核环节通过发送消息
     */
    private void sendApprovalMessages(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId) {
        String nickName = commonMapper.queryNickNameByUserId(userId);
        DtbBook messageBookInfo = dtbBookService.getById(dtbBookPublishProcess.getBookId());

        List<DtbBookGroup> dtbBookGroupList = dtbGroupService.list(new LambdaQueryWrapper<DtbBookGroup>().eq(DtbBookGroup::getBookId, book.getBookId()));
        if (!CollectionUtils.isEmpty(dtbBookGroupList)) {
            dtbBookGroupList
                    .stream()
                    .map(DtbBookGroup::getUserId) // 提取 userId
                    .distinct() // 去重
                    .forEach(toUserId -> sendMessage(userId, toUserId, "您好，教材已审核通过，审批意见：" + dtbBookPublishProcess.getReason() + "。【教材名称：" + messageBookInfo.getBookName() + "；教材编号：" + messageBookInfo.getBookNo() + "】", "教材审核提醒", 1,1,1,dtbBookPublishProcess.getProcessId()));
        }

        R<List<SysUser>> adminIdList = remoteUserService.listUserByRoleId(2L, SecurityConstants.FROM_SOURCE);
        if (R.FAIL == adminIdList.getCode()) {
            throw new ServiceException(adminIdList.getMsg());
        }
        if (!CollectionUtils.isEmpty(adminIdList.getData())) {
            adminIdList.getData().forEach(e -> sendMessage(userId, e.getUserId(), "您好，教材已审核通过，审批意见：" + dtbBookPublishProcess.getReason() + "。【教材名称：" + messageBookInfo.getBookName() + "；教材编号：" + messageBookInfo.getBookNo() + "】", "教材审核提醒", 1,1,1,dtbBookPublishProcess.getProcessId()));
        }
    }

    /**
     * 其他节点通过发送消息
     */
    private void sendNextStepMessage(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId, DtbBookPublishProcess nextProcess) {
        DtbBookPublishStep dtbBookPublishStep = dtbBookPublishStepService.getStepById(nextProcess.getStepId());
        DtbBook messageBookInfo = dtbBookService.getById(dtbBookPublishProcess.getBookId());
        String nickName = commonMapper.queryNickNameByUserId(userId);
        sendMessage(userId, nextProcess.getUserId(), "您好，审批人“" + nickName + "”已通过审批，审批意见：" + dtbBookPublishProcess.getReason() + "。现已进入" + dtbBookPublishStep.getStepName() + "节点，请尽快处理。【教材名称：" + messageBookInfo.getBookName() + "；教材编号：" + messageBookInfo.getBookNo() + "】", "教材审核提醒", 1,1,1,nextProcess.getProcessId());
    }

    /**
     * 来搞确认节点驳回消息
     */
    private void sendRejectionMessage(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId, DtbBookPublishProcess nextProcess) {
        DtbBookPublishStep dtbBookPublishStep = dtbBookPublishStepService.getStepById(nextProcess.getStepId());
        DtbBook messageBookInfo = dtbBookService.getById(dtbBookPublishProcess.getBookId());
        String nickName = commonMapper.queryNickNameByUserId(userId);
        List<DtbBookGroup> dtbBookGroupList = dtbGroupService.list(new LambdaQueryWrapper<DtbBookGroup>().eq(DtbBookGroup::getBookId, book.getBookId()).in(DtbBookGroup::getRoleType, 1, 2));
        if (!CollectionUtils.isEmpty(dtbBookGroupList)) {
            dtbBookGroupList
                    .stream()
                    .map(DtbBookGroup::getUserId) // 提取 userId
                    .distinct() // 去重
                    .forEach(toUserId -> sendMessage(userId, toUserId, "您好，策划编辑“" +nickName+ "”已驳回申请，审批意见："+ dtbBookPublishProcess.getReason() +"。" +
                            "【教材名称："+ messageBookInfo.getBookName() +"；教材编号："+ messageBookInfo.getBookNo() +"】", "教材审核提醒", 1,1,1,nextProcess.getProcessId()));
        }

    }

    /**
     * 其他节点驳回消息
     */
    private void sendNextRejectionMessage(DtbBookPublishProcess dtbBookPublishProcess, DtbBook book, Long userId, DtbBookPublishProcess nextProcess) {
        DtbBookPublishStep dtbBookPublishStep = dtbBookPublishStepService.getStepById(nextProcess.getStepId());
        DtbBook messageBookInfo = dtbBookService.getById(dtbBookPublishProcess.getBookId());
        String nickName = commonMapper.queryNickNameByUserId(userId);
        sendMessage(userId, nextProcess.getUserId(), "您好，审批人“" + nickName + "”已驳回申请，审批意见：" + dtbBookPublishProcess.getReason() + "。" +
                "现已回退到“" + dtbBookPublishStep.getStepName() + "”节点，请尽快处理。【教材名称：" + messageBookInfo.getBookName() + "；教材编号：" + messageBookInfo.getBookNo() + "】", "教材审核提醒", 1,1,1,nextProcess.getProcessId());
    }

    private void sendMessage(Long fromUserId, Long toUserId, String content, String title, int messageType, int fromUserType, int toUserType, Long processId) {
        DutpUserMessage message = new DutpUserMessage();
        message.setContent(content);
        message.setTitle(title);
        message.setFromUserId(fromUserId);
        message.setToUserId(toUserId);
        message.setBusinessId(processId);
        message.setMessageType(messageType);
        message.setFromUserType(fromUserType);
        message.setToUserType(toUserType);
        remoteUserMessageService.addMessage(message);
    }


    /**
     * 节点审批 -- 补录保存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addProcessAddition(DtbBookPublishProcess dtbBookPublishProcess) {
        try{
            /*
             * 新增审批流程数据 state前端固定值 2通过
             * additionFlag前端固定值 1补录
             * 发起人为空
             * 上次流程id为空
             */
            this.save(dtbBookPublishProcess);

            // 录排登记通过时 生成录排登记号
            if(dtbBookPublishProcess.getStepId() == 12) {
                // book对象
                DtbBook book = dtbBookService.getById(dtbBookPublishProcess.getBookId());
                if (book == null){
                    throw new ServiceException("书籍不存在");
                }
                if (StringUtils.isEmpty(book.getRecordNo())) {
                    String recordNo = "";
                    recordNo = getRandomCode();
                    book.setRecordNo(recordNo);
                }

            }

            // 新增流程审批人数据
            DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser = new DtbBookPublishProcessAuditUser();
            // 因为是补录 所以审核人userId 也是流程的实际审批人 dealUserId
            dtbBookPublishProcessAuditUser.setUserId(dtbBookPublishProcess.getDealUserId());
            dtbBookPublishProcessAuditUser.setHandleUserId(dtbBookPublishProcess.getDealUserId());
            dtbBookPublishProcessAuditUser.setDeptId(dtbBookPublishProcess.getProcessDeptId());
            // 补录环节直接通过 和流程表同步
            dtbBookPublishProcessAuditUser.setState(dtbBookPublishProcess.getState());
            dtbBookPublishProcessAuditUser.setReason(dtbBookPublishProcess.getReason());
            dtbBookPublishProcessAuditUser.setProcessId(dtbBookPublishProcess.getProcessId());
            dtbBookPublishProcessAuditUserMapper.insert(dtbBookPublishProcessAuditUser);
        }catch (Exception e){
            return false;
        }
        return true;
    }

    /**
     * 节点审批 -- 补录编辑
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editProcessAddition(DtbBookPublishProcess dtbBookPublishProcess) {
        try{
            /*
             * 新增审批流程数据 state前端固定值 2通过
             * additionFlag前端固定值 1补录
             * 发起人为空
             * 上次流程id为空
             */
            this.updateById(dtbBookPublishProcess);

            // 新增流程审批人数据
            DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser = new DtbBookPublishProcessAuditUser();
            LambdaUpdateWrapper<DtbBookPublishProcessAuditUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(DtbBookPublishProcessAuditUser::getHandleUserId,dtbBookPublishProcess.getDealUserId());
            updateWrapper.set(DtbBookPublishProcessAuditUser::getDeptId,dtbBookPublishProcess.getProcessDeptId());
            updateWrapper.set(DtbBookPublishProcessAuditUser::getUserId,dtbBookPublishProcess.getDealUserId());
            updateWrapper.set(DtbBookPublishProcessAuditUser::getState,dtbBookPublishProcess.getState());
            updateWrapper.set(DtbBookPublishProcessAuditUser::getReason,dtbBookPublishProcess.getReason());
            updateWrapper.eq(DtbBookPublishProcessAuditUser::getProcessId,dtbBookPublishProcess.getProcessId());
            dtbBookPublishProcessAuditUserMapper.update(dtbBookPublishProcessAuditUser,updateWrapper);
        }catch (Exception e){
            return false;
        }
        return true;
    }


    @Override
    public DtbBookPublishProcess getProcessAddition(DtbBookPublishProcess dtbBookPublishProcess) {
        DtbBookPublishProcess res = new DtbBookPublishProcess();
        Long processId = dtbBookPublishProcess.getProcessId();
        res = this.getById(processId);
        DtbBookPublishProcessAuditUser dtbBookPublishProcessAuditUser = dtbBookPublishProcessAuditUserMapper.selectOne(new LambdaQueryWrapper<DtbBookPublishProcessAuditUser>().eq(DtbBookPublishProcessAuditUser::getProcessId,processId));
        res.setProcessDeptId(dtbBookPublishProcessAuditUser.getDeptId());
        res.setUserId(dtbBookPublishProcessAuditUser.getUserId());
        return res;
    }

    /**
     * 修改DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param dtbBookPublishProcess DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     * @return 结果
     */
    @Override
    public boolean updateDtbBookPublishProcess(DtbBookPublishProcess dtbBookPublishProcess) {
        return this.updateById(dtbBookPublishProcess);
    }

    /**
     * 批量删除DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】
     *
     * @param processIds 需要删除的DUTP-DTB_007数字教材发布流程【创建->审校->发布整个流程】主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookPublishProcessByProcessIds(List<Long> processIds) {
        return this.removeByIds(processIds);
    }

    @Override
    public List<DtbBookPublishProcessVO> processDataList(DtbBookPublishProcess dtbBookPublishProcess) {
        if (dtbBookPublishProcess.getBookId() == null) {
            throw new RuntimeException("书籍不存在");
        }
        // 获取审批详情集合
        List<DtbBookPublishProcess> processDataList = dtbBookPublishProcessMapper.getProcessDetail(dtbBookPublishProcess);


        // 构建树形结构
        Map<Long, DtbBookPublishProcessVO> rootNodeMap = processDataList.stream()
                .collect(Collectors.toMap(
                        DtbBookPublishProcess::getRootStepId,
                        process -> {
                            DtbBookPublishProcessVO vo = new DtbBookPublishProcessVO();
                            vo.setRootStepId(process.getRootStepId());
                            vo.setRootStepName(process.getRootStepName());
                            vo.setDtbBookPublishProcessList(new ArrayList<>()); // 初始化子节点列表
                            return vo;
                        },
                        (existing, duplicate) -> existing,
                        LinkedHashMap::new
                ));

        // 遍历数据，将子节点放入对应的父节点中
        processDataList.forEach(process -> {
            DtbBookPublishProcessVO parent = rootNodeMap.get(process.getRootStepId());
            if (parent != null) {
                parent.getDtbBookPublishProcessList().add(process);
            }
        });

        return new ArrayList<>(rootNodeMap.values());
    }

    /**
     * 获取上次流程的节点信息
     *
     * @param dtbBookPublishProcess
     */
    @Override
    public DtbBookPublishProcess getPrevProcessInfo(DtbBookPublishProcess dtbBookPublishProcess) {
        // 当前流程id
        Long bookId = dtbBookPublishProcess.getBookId();
        Long stepId = dtbBookPublishProcess.getStepId();
        return  dtbBookPublishProcessMapper.getPrevProcessInfo(bookId,stepId);
    }

    @Override
    public DtbBookPublishProcess getProcessInfoLink(Long processId) {
        return dtbBookPublishProcessMapper.getProcessInfoLink(processId);
    }

    @Override
    public AjaxResult updateChapterBackup(DtbBookPublishProcess dtbBookPublishProcess) {
        Query query = new Query(Criteria.where("chapterId").is(dtbBookPublishProcess.getChapterId()));
        DtbBookChapterContent chapterContent = mongoService.findOne(BOOK_CHAPTER_CONTENT, query, DtbBookChapterContent.class);
        long localTimestamp = System.currentTimeMillis(); // 当前本地时间戳
        Document document = new Document();
        document.put("chapterId", dtbBookPublishProcess.getChapterId());
        document.put("content", chapterContent.getContent());
        document.put("backupTime", localTimestamp);
        mongoService.insertOne(BOOK_CHAPTER_CONTENT_BACKUP, document);
        DtbBookPublishProcessChapterBackup dtbBookPublishProcessChapterBackup = new DtbBookPublishProcessChapterBackup();
        dtbBookPublishProcessChapterBackup.setProcessId(dtbBookPublishProcess.getProcessId());
        dtbBookPublishProcessChapterBackup.setStepId(dtbBookPublishProcess.getStepId());
        dtbBookPublishProcessChapterBackup.setChapterId(dtbBookPublishProcess.getChapterId());
        dtbBookPublishProcessChapterBackupMapper.insert(dtbBookPublishProcessChapterBackup);
        return AjaxResult.success();
    }

    /**
     * 生成录排登记号
     *
     * @param length
     * @return
     */
    public static String getRandomCode() {
        Random random = new Random();
        long id = random.nextLong();
        id = Math.abs(id % 10000000000000000L);
        return String.format("%016d", id);
    }
}
