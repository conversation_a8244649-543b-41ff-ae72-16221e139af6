package cn.dutp.book.service.impl;

import java.util.List;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.service.IDtbBookChapterService;
import cn.dutp.book.service.IDtbBookService;
import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbUserBookConfigMapper;
import cn.dutp.book.domain.DtbUserBookConfig;
import cn.dutp.book.service.IDtbUserBookConfigService;

/**
 * 教材配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class DtbUserBookConfigServiceImpl extends ServiceImpl<DtbUserBookConfigMapper, DtbUserBookConfig> implements IDtbUserBookConfigService
{
    @Autowired
    private DtbUserBookConfigMapper dtbUserBookConfigMapper;

    @Autowired
    private IDtbBookService iDtbBookService;

    @Autowired
    private IDtbBookChapterService iDtbBookChapterService;

    /**
     * 查询教材配置
     *
     * @param configId 教材配置主键
     * @return 教材配置
     */
    @Override
    public DtbUserBookConfig selectDtbUserBookConfigByConfigId(Long configId)
    {
        return this.getById(configId);
    }

    /**
     * 查询教材配置列表
     *
     * @param dtbUserBookConfig 教材配置
     * @return 教材配置
     */
    @Override
    public List<DtbUserBookConfig> selectDtbUserBookConfigList(DtbUserBookConfig dtbUserBookConfig)
    {
        LambdaQueryWrapper<DtbUserBookConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getUserId())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getUserId
                ,dtbUserBookConfig.getUserId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getBookId())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getBookId
                ,dtbUserBookConfig.getBookId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getTheme())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getTheme
                ,dtbUserBookConfig.getTheme());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getFontFamily())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getFontFamily
                ,dtbUserBookConfig.getFontFamily());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getFontSize())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getFontSize
                ,dtbUserBookConfig.getFontSize());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getColumnQuantity())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getColumnQuantity
                ,dtbUserBookConfig.getColumnQuantity());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLineHeight())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLineHeight
                ,dtbUserBookConfig.getLineHeight());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getReadRate())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getReadRate
                ,dtbUserBookConfig.getReadRate());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getReadMode())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getReadMode
                ,dtbUserBookConfig.getReadMode());
            }

                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLastSeeDate())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLastSeeDate
                ,dtbUserBookConfig.getLastSeeDate());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLastChapterId())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLastChapterId
                ,dtbUserBookConfig.getLastChapterId());
            }
                if(ObjectUtil.isNotEmpty(dtbUserBookConfig.getLastPageNumber())) {
                lambdaQueryWrapper.eq(DtbUserBookConfig::getLastPageNumber
                ,dtbUserBookConfig.getLastPageNumber());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教材配置
     *
     * @param dtbUserBookConfig 教材配置
     * @return 结果
     */
    @Override
    public boolean insertDtbUserBookConfig(DtbUserBookConfig dtbUserBookConfig)
    {
        return this.save(dtbUserBookConfig);
    }

    /**
     * 修改教材配置
     *
     * @param dtbUserBookConfig 教材配置
     * @return 结果
     */
    @Override
    public AjaxResult updateDtbUserBookConfig(DtbUserBookConfig dtbUserBookConfig)
    {
        DtbUserBookConfig dbconfig = this.getById(dtbUserBookConfig.getConfigId());
        if (dbconfig.getUserId().longValue() != SecurityUtils.getUserId().longValue()) {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "用户没有权限");
        }
        this.updateById(dtbUserBookConfig);
        return AjaxResult.success();
    }

    /**
     * 批量删除教材配置
     *
     * @param configIds 需要删除的教材配置主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbUserBookConfigByConfigIds(List<Long> configIds)
    {
        return this.removeByIds(configIds);
    }


    /**
     * 更新后修改修正后的最后阅读章节id
     * @param bookId
     * @return
     */
    @Override
    public AjaxResult updateBookVersion(DtbUserBookConfig dtbUserBookConfig) {
        Long userId = SecurityUtils.getUserId();
        Long bookId = dtbUserBookConfig.getBookId();
        if (ObjectUtil.isEmpty(bookId)){
            return AjaxResult.error("参数错误");
        }

        // 修正章节id之前 先去book表中拿最新的版本id
        DtbBook book = iDtbBookService.selectDtbBookByBookId(bookId);
        if (ObjectUtil.isEmpty(book)){
            return AjaxResult.error("书籍不存在");
        }
        Long versionId = book.getLastVersionId();

        // 需求是PC端和移动端同步更新 这里只用PC端查询 加上isMobile=0条件 更新的时候去掉isMobile条件
        try {
            DtbUserBookConfig dbconfig = this.getOne(new LambdaQueryWrapper<DtbUserBookConfig>()
                    .eq(DtbUserBookConfig::getUserId, userId)
                    .eq(DtbUserBookConfig::getIsMobile, 0)
                    .eq(DtbUserBookConfig::getBookId, bookId));

            if (ObjectUtil.isNotEmpty(dbconfig)){
                Long oldChapterId = dbconfig.getLastChapterId();

                // 用oldChapterId去章节表查询相同顺序的新版本章节id
                DtbBookChapter bookChapter = iDtbBookChapterService.getById(oldChapterId);

                if (ObjectUtil.isEmpty(bookId)){
                    return AjaxResult.error("章节不存在");
                }

                // 查询新版本的对应章节id
                LambdaQueryWrapper<DtbBookChapter> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(DtbBookChapter::getBookId, bookId)
                        .eq(DtbBookChapter::getVersionId, versionId)
                        .eq(DtbBookChapter::getSort, bookChapter.getSort())
                        .eq(DtbBookChapter::getDelFlag, 0);
                DtbBookChapter newBookChapter = iDtbBookChapterService.getOne(lambdaQueryWrapper);

                // 用户配置表的最后阅读章节id 更新成新版本的章节id
                if (ObjectUtil.isNotEmpty(newBookChapter)){
                    LambdaUpdateWrapper<DtbUserBookConfig> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    lambdaUpdateWrapper.eq(DtbUserBookConfig::getBookId, bookId);
                    lambdaUpdateWrapper.eq(DtbUserBookConfig::getUserId, userId);
                    lambdaUpdateWrapper.set(DtbUserBookConfig::getLastChapterId, newBookChapter.getChapterId());
                    this.update(lambdaUpdateWrapper);
                }else {
                    return AjaxResult.error("没有找到对应的章节");
                }
            }
        }catch (Exception e){
            return AjaxResult.error("查询配置错误");
        }
        return AjaxResult.success();
    }
}
