package cn.dutp.book.controller;

import cn.dutp.book.domain.DutpAiUserConfig;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpAiHistory;
import cn.dutp.book.service.*;
import cn.dutp.common.ai.common.ai.domain.ChatAiRequest;
import cn.dutp.common.core.web.controller.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/chatAi")
public class ChatAiController extends BaseController {

    @Autowired
    IDutpAiPromptService dutpAiPromptService;

    @Autowired
    IDutpAiVoiceService dutpAiVoiceService;

    @Autowired
    IDutpAiTranslationLanguageService dutpAiTranslationLanguageService;

    @Autowired
    IDutpAiHistoryService dutpAiHistoryService;

    @Autowired
    IDutpAiPromptService DutpAiPromptService;

    @Autowired
    IDutpAiWhiteBlackService dutpAiWhiteBlackService;

    @Autowired
    AiCommonServiceImpl aiCommonService;

//    @Autowired
//    AiWebsocketCommonService websocketCommonService;

    private final static Logger log = LoggerFactory.getLogger(ChatAiController.class);


    @Value("${ai.appid}")
    private String appid;

    @Value("${ai.apiSecret}")
    private String apiSecret;

    @Value("${ai.apiKey}")
    private String apiKey;

    // 文字纠错(校阅)
    @Value("${ai.textCorrectionHostUrl}")
    private String textCorrectionHostUrl;

    // 视频合规
    @Value("${ai.videoComplianceUrl}")
    private String videoComplianceUrl;

    @Value("${ai.videoComplianceQueryUrl}")
    private String videoComplianceQueryUrl;

    // 文字纠错 上传黑名单 (违禁词替换) 和白名单 (免审词设置)
    @Value("${ai.regUrl}")
    private String regUrl;

    // 文本合规(校阅)
    @Value("${ai.textComplianceUrl}")
    private String textComplianceUrl;

    // 文本合规 上传黑名单 (违禁词替换) 和白名单 (免审词设置)
    @Value("${ai.textComplianceListUrl}")
    private String textComplianceListUrl;

    // 图片合规
    @Value("${ai.imageComplianceUrl}")
    private String imageComplianceUrl;

    // 音频合规
    @Value("${ai.audioComplianceUrl}")
    private String audioComplianceUrl;

    // 翻译
    @Value("${ai.webitsUrl}")
    private String webitsUrl;

    // 文本改写（重写）
    @Value("${ai.textRewritingUrl}")
    private String textRewritingUrl;

    // 大模型
    @Value("${ai.chatCompletionsUrl}")
    private String chatCompletionsUrl;


    // 大模型
    @Value("${ai.apiPassword}")
    private String apiPassword;

    // 文生图
    @Value("${ai.textToPictureUrl}")
    private String textToPictureUrl;

    // 文生语音
    @Value("${ai.textToVoiceUrl}")
    private String textToVoiceUrl;

    // 百度获取token
    @Value("${ai.baiduTokenUrl}")
    private String baiduTokenUrl;

    // 百度图片增强应用
    @Value("${ai.clientId}")
    private String clientId;

    // 百度图片增强应用
    @Value("${ai.clientSecret}")
    private String clientSecret;

    // 百度图片增强-图片修复
    @Value("${ai.baiduInpaintUrl}")
    private String baiduInpaintUrl;

    // 百度图片增强-图片清晰度增强
    @Value("${ai.imageEnhancement}")
    private String baifduImageEnhancement;

    // 百度文生视频
    @Value("${ai.textToVideoUrl}")
    private String textToVideoUrl;

    private final static String uid = "white_list";//自定义设置（_或者字母组成），在调用文本纠错接口时需要上传
    private final static String res_id = "black_list";//自定义设置（_或者字母组成），在调用文本纠错接口时需要上传

    /**
     * 功能1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译
     */
    @PostMapping("/chatAi")
    public String chatAi(@RequestBody ChatAiRequest chatAiRequest) throws Exception {
        DutpAiHistory history = new DutpAiHistory();
        history.setQuestion(chatAiRequest.getQuestion());
        history.setPromptAbility(chatAiRequest.getAbility());
        history.setChapterId(chatAiRequest.getChapterId());
        chatAiRequest.setQuestion(chatAiRequest.getQuestion().replace("\"", "\\\""));
        // TODO 临时使用
//        history.setUserId(255L);
        history.setUserId(SecurityUtils.getUserId());
        /*1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲*/
        if (chatAiRequest.getAbility() == 1 || chatAiRequest.getAbility() == 2 || chatAiRequest.getAbility() == 3 ||
                chatAiRequest.getAbility() == 4 || chatAiRequest.getAbility() == 5 || chatAiRequest.getAbility() == 6 ||
                chatAiRequest.getAbility() == 7 || chatAiRequest.getAbility() == 8 || chatAiRequest.getAbility() == 9 ||
                chatAiRequest.getAbility() == 25 || chatAiRequest.getAbility() == 26 || chatAiRequest.getAbility() == 27) {
            return aiCommonService.getByPrompt(chatAiRequest, chatCompletionsUrl, apiPassword, history);
        }
        /*匹配案例*/
        if (chatAiRequest.getAbility() == 10) {

        }

        /*文生语音*/
        if (chatAiRequest.getAbility() == 11) {
            return aiCommonService.textToVoice(chatAiRequest,textToVoiceUrl,apiKey,apiSecret,appid, history);
//            return websocketCommonService.textToVoice(textToVoiceUrl,apiKey,apiSecret);
        }
        /*文生图片*/
        if (chatAiRequest.getAbility() == 12) {
            return aiCommonService.textToPicture(chatAiRequest, textToPictureUrl, apiKey, apiSecret, appid, history);
        }
        /*百度图片增强(清晰度增强)*/
        if (chatAiRequest.getAbility() == 13) {
            return aiCommonService.enhancedClarity(chatAiRequest, clientId, clientSecret, baiduTokenUrl, baifduImageEnhancement, history);
        }
        /*百度图像修复*/
        if (chatAiRequest.getAbility() == 14) {
            return aiCommonService.imageEnhancement(chatAiRequest, clientId, clientSecret, baiduTokenUrl, baiduInpaintUrl, history);
        }
        /*百度视频生成*/
        if (chatAiRequest.getAbility() == 15) {
            return aiCommonService.textToVideo(chatAiRequest,clientId,clientSecret,baiduTokenUrl,textToVideoUrl,history);
            // todo 待确定生成视频的处理
        }
        /*文字纠错*/
        if (chatAiRequest.getAbility() == 16) {
            return aiCommonService.webTextCorrection(chatAiRequest, appid, apiKey, apiSecret, textCorrectionHostUrl, history);
        }

        /*文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))*/
        if (chatAiRequest.getAbility() == 17) {
            return aiCommonService.uploadCorrectionWhiteBlackList(chatAiRequest, appid, uid, res_id, regUrl, history);
        }
        /*视频合规*/
        if (chatAiRequest.getAbility() == 18) {
            /*chatAiRequest.getQuestion()待校验的频公网地址*/
            return aiCommonService.videoCompliance(chatAiRequest, appid, apiKey, apiSecret, videoComplianceUrl, videoComplianceQueryUrl, history);
        }
        /*文本合规*/
        if (chatAiRequest.getAbility() == 19) {
            return aiCommonService.textCompliance(chatAiRequest, apiKey, appid, apiSecret, textComplianceUrl, history);
        }
        /*文本合规(上传黑白名单)*/
        if (chatAiRequest.getAbility() == 20) {

        }
        /*图片合规*/
        if (chatAiRequest.getAbility() == 21) {
            return aiCommonService.imageCompliance(chatAiRequest, appid, apiKey, apiSecret, imageComplianceUrl, history);
        }
        /*音频合规*/
        if (chatAiRequest.getAbility() == 22) {
            /*chatAiRequest.getQuestion()待校验的音频公网地址*/
            return aiCommonService.audioCompliance(chatAiRequest, audioComplianceUrl, appid, apiKey, apiSecret, videoComplianceQueryUrl, history);
        }
        /*翻译*/
        if (chatAiRequest.getAbility() == 23) {
            return aiCommonService.translate(chatAiRequest, appid, apiKey, apiSecret, webitsUrl, history);
        }
        /*重写(文本改写)*/
        if (chatAiRequest.getAbility() == 24) {
            return aiCommonService.textRewriting(chatAiRequest,textRewritingUrl,appid,apiKey,apiSecret,history);
        }
        return null;
    }

    @GetMapping("/getAiExperiment")
    public DutpAiUserConfig getAiExperiment() {
        return aiCommonService.getAiExperiment();
    }

    /**
     * 识别文字语言
     */
    @PostMapping("/getLanguage")
    public String getLanguage(@RequestBody ChatAiRequest chatAiRequest) {
        return aiCommonService.getLanguage(chatAiRequest);
    }
}
