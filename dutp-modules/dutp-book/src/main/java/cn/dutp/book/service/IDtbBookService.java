package cn.dutp.book.service;

import cn.dutp.book.domain.LearningStatisticsVo;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.domain.DtbBook;
import cn.dutp.domain.DtbBookVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * DUTP-DTB_002数字教材Service接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public interface IDtbBookService extends IService<DtbBook> {
    /**
     * 查询DUTP-DTB_002数字教材
     *
     * @param bookId DUTP-DTB_002数字教材主键
     * @return DUTP-DTB_002数字教材
     */
    public DtbBook selectDtbBookByBookId(Long bookId);

    /**
     * 查询DUTP-DTB_002数字教材列表
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return DUTP-DTB_002数字教材集合
     */
    public List<DtbBook> selectDtbBookList(DtbBook dtbBook);

    /**
     * 新增DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    public Long insertDtbBook(DtbBook dtbBook);

    /**
     * 修改DUTP-DTB_002数字教材
     *
     * @param dtbBook DUTP-DTB_002数字教材
     * @return 结果
     */
    public boolean updateDtbBook(DtbBook dtbBook);

    /**
     * 批量删除DUTP-DTB_002数字教材
     *
     * @param bookId 需要删除的DUTP-DTB_002数字教材主键
     * @return 结果
     */
    public boolean deleteDtbBookByBookId(Long bookId);

    List<DtbBookVo> selectCmsDtbBookList(DtbBook dtbBook);

    DtbBook searchOne(DtbBook dtbBook);

    int copyDtbBook(DtbBook dtbBook);

    /**
     * 学生教师端多维交叉查询
     */
    public List<DtbBook> miSearchEducation(DtbBook dtbBook);

    int amendDtbBook(DtbBook dtbBook);

    DtbBook queryProfile(DtbBook dtbBook);


    List<DtbBook> selectDtbBookListWithGroup(DtbBook dtbBook);

    DtbBook queryCaptionStyle(DtbBook dtbBook);

    /**
     * 根据bookId 查询出所有副教材 （发型管理)
     */
    List<DtbBook> listBookNature(List<Long> bookIds);

    /**
     * 召回数字教材(发行管理)
     *
     * @param dtbBook
     * @return
     */
    boolean recallDtbBook(DtbBook dtbBook);

    /**
     * 上下架数字教材(发行管理)
     *
     * @param dtbBook
     * @return
     */
    boolean shelfDtbBook(DtbBook dtbBook);

    int finalizedSubmit(DtbBook dtbBook);

    public boolean checkBookStatus(Long bookId);

    List<DtbBook> listOfAuthorAndEditor(DtbBook dtbBook);

    boolean editCaptionStyle(DtbBook dtbBook);

    AjaxResult getBooks(Long bookId);

    int importBook(MultipartFile file, Long bookId);
    int importChapterPdf(MultipartFile file, Long bookId);

    void exportBook(DtbBook dtbBook);

    void toggleBatchShelf(DtbBook dtbBook);

    List<DtbBook> getListByBookIds(List<Long> bookIds);

    /**
     * 学习统计查询
     */
    LearningStatisticsVo getLearningStatistics();
}
