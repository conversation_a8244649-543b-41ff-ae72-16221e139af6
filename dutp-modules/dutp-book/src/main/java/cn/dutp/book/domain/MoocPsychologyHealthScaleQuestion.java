package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 心里健康题目题干对象 mooc_psychology_health_scale_question
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("mooc_psychology_health_scale_question")
public class MoocPsychologyHealthScaleQuestion extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oldQuestionId;

    /**
     * 题干
     */
    @Excel(name = "题干")
    private String questionContent;

    /**
     * 量表id
     */
    @Excel(name = "量表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long scaleId;

    /**
     * 维度id(如果有)
     */
    @Excel(name = "维度id(如果有)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long facetId;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Long sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 选项
     */
    @TableField(exist = false)
    private List<MoocPsychologyHealthScaleQuestionOption> moocPsychologyHealthScaleQuestionOption;


    /**
     * 量表题干
     */
    @TableField(exist = false)
    private List<MoocPsychologyHealthScaleQuestion> moocPsychologyHealthScaleQuestion;

    /**
     * 用户选择的选项id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chooseOptionId;

    /**
     * 排序
     */
    @TableField(exist = false)
    private Long questionSort;


    /**
     * 跳转id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long jumpId;

    /**
     * 维度名称
     */
    @TableField(exist = false)
    private String facetName;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long optionId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("questionId", getQuestionId())
                .append("questionContent", getQuestionContent())
                .append("scaleId", getScaleId())
                .append("facetId", getFacetId())
                .append("sort", getSort())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
