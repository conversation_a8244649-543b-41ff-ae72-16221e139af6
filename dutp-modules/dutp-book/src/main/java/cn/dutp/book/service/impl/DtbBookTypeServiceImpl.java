package cn.dutp.book.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.utils.TreeUtil;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.book.mapper.DtbBookTypeMapper;
import cn.dutp.book.domain.DtbBookType;
import cn.dutp.book.service.IDtbBookTypeService;

/**
 * 中图分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Slf4j
@Service
public class DtbBookTypeServiceImpl extends ServiceImpl<DtbBookTypeMapper, DtbBookType> implements IDtbBookTypeService {
    @Autowired
    private DtbBookTypeMapper dtbBookTypeMapper;

    /**
     * 查询中图分类
     *
     * @param typeId 中图分类主键
     * @return 中图分类
     */
    @Override
    public DtbBookType selectDtbBookTypeByTypeId(Long typeId) {
        return this.getById(typeId);
    }

    /**
     * 查询中图分类列表
     *
     * @param dtbBookType 中图分类
     * @return 中图分类
     */
    @Override
    public List<DtbBookType> selectDtbBookTypeList(DtbBookType dtbBookType) {
        // 先获取所有分类数据
        LambdaQueryWrapper<DtbBookType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(DtbBookType::getTypeId, DtbBookType::getTypeName,
                DtbBookType::getTypeCode, DtbBookType::getParentId, DtbBookType::getSort);
        lambdaQueryWrapper.orderByAsc(DtbBookType::getSort);
        List<DtbBookType> allList = this.list(lambdaQueryWrapper);
        
        // 如果没有查询条件，直接返回所有数据的树形结构
        if ((ObjectUtil.isEmpty(dtbBookType.getTypeName()) && ObjectUtil.isEmpty(dtbBookType.getTypeCode()))) {
            return TreeUtil.makeTree(allList, o -> o.getParentId().longValue() == 0L,
                    (x, y) -> x.getTypeId().equals(y.getParentId()), DtbBookType::setChildren);
        }
        
        // 根据条件筛选匹配的节点
        List<DtbBookType> matchedList = new ArrayList<>();
        for (DtbBookType type : allList) {
            if ((ObjectUtil.isNotEmpty(dtbBookType.getTypeName()) && 
                    type.getTypeName() != null && 
                    type.getTypeName().contains(dtbBookType.getTypeName())) ||
                (ObjectUtil.isNotEmpty(dtbBookType.getTypeCode()) && 
                    type.getTypeCode() != null && 
                    type.getTypeCode().equals(dtbBookType.getTypeCode()))) {
                matchedList.add(type);
            }
        }
        
        // 获取所有匹配节点的ID
        Set<Long> matchedIds = matchedList.stream()
                .map(DtbBookType::getTypeId)
                .collect(Collectors.toSet());
        
        // 获取所有匹配节点的父节点ID（一直到根节点）
        Set<Long> parentIds = new HashSet<>();
        for (DtbBookType matched : matchedList) {
            collectParentIds(matched.getParentId(), allList, parentIds);
        }
        
        // 获取所有匹配节点的子节点ID
        Set<Long> childIds = new HashSet<>();
        for (Long matchedId : matchedIds) {
            collectChildIds(matchedId, allList, childIds);
        }
        
        // 合并所有需要显示的节点ID
        Set<Long> allNeededIds = new HashSet<>();
        allNeededIds.addAll(matchedIds);
        allNeededIds.addAll(parentIds);
        allNeededIds.addAll(childIds);
        
        // 筛选出所有需要显示的节点
        List<DtbBookType> resultList = allList.stream()
                .filter(type -> allNeededIds.contains(type.getTypeId()))
                .collect(Collectors.toList());
        
        // 构建树形结构
        return TreeUtil.makeTree(resultList, o -> o.getParentId().longValue() == 0L,
                (x, y) -> x.getTypeId().equals(y.getParentId()), DtbBookType::setChildren);
    }
    
    /**
     * 递归收集所有父节点ID
     * 
     * @param parentId 父节点ID
     * @param allList 所有节点列表
     * @param parentIds 收集的父节点ID集合
     */
    private void collectParentIds(Long parentId, List<DtbBookType> allList, Set<Long> parentIds) {
        if (parentId == null || parentId == 0L) {
            return;
        }
        
        parentIds.add(parentId);
        
        // 查找当前父节点的父节点
        for (DtbBookType type : allList) {
            if (type.getTypeId().equals(parentId)) {
                collectParentIds(type.getParentId(), allList, parentIds);
                break;
            }
        }
    }
    
    /**
     * 递归收集所有子节点ID
     * 
     * @param parentId 父节点ID
     * @param allList 所有节点列表
     * @param childIds 收集的子节点ID集合
     */
    private void collectChildIds(Long parentId, List<DtbBookType> allList, Set<Long> childIds) {
        for (DtbBookType type : allList) {
            if (type.getParentId().equals(parentId)) {
                childIds.add(type.getTypeId());
                collectChildIds(type.getTypeId(), allList, childIds);
            }
        }
    }

    /**
     * 新增中图分类
     *
     * @param dtbBookType 中图分类
     * @return 结果
     */
    @Override
    public boolean insertDtbBookType(DtbBookType dtbBookType) {
        // 检测重复
        checkIsRepeat(dtbBookType);

        // 检测层级
        checkLevel(dtbBookType);
        return this.save(dtbBookType);
    }


    /**
     * 检测是否形成环
     *
     * @param typeId
     * @param parentId
     */
    private void checkIsRing(Long typeId, Long parentId) {
        if (parentId.longValue() == 0l) {
            return;
        }
        if (typeId.longValue() == parentId.longValue()) {
            log.error("操作用户Id: {}, typeId: {}, parentId: {} 尝试成为环", SecurityUtils.getUserId(), typeId, parentId);
            throw new ServiceException("不允许自己成为自己的下级");
        }
        checkIsRing(typeId, dtbBookTypeMapper.getParentIdByTypeId(parentId));
    }

    /**
     * 检测是否重复
     *
     * @param dtbBookType
     */
    private void checkIsRepeat(DtbBookType dtbBookType) {
        // 分类名
        int count = dtbBookTypeMapper.checkIsRepeatByTypeName(dtbBookType.getTypeId(), dtbBookType.getTypeName());
        if (count > 0) {
            log.error("操作用户Id: {}, typeId: {}, typeName: {} 重复", SecurityUtils.getUserId(), dtbBookType.getTypeId(), dtbBookType.getTypeName());
            throw new ServiceException("分类名已被使用");
        }

        // 分类号
        count = dtbBookTypeMapper.checkIsRepeatByTypeCode(dtbBookType.getTypeId(), dtbBookType.getTypeCode());
        if (count > 0) {
            log.error("操作用户Id: {}, typeId: {}, typeCode: {} 重复", SecurityUtils.getUserId(), dtbBookType.getTypeId(), dtbBookType.getTypeCode());
            throw new ServiceException("分类号已被使用");
        }
    }

    /**
     * 检测层级 最大5级
     *
     * @param dtbBookType
     */
    private void checkLevel(DtbBookType dtbBookType) {
        long parentId = dtbBookType.getParentId().longValue();
        // 顶级目录
        if (parentId == 0l) {
            return;
        }

        int level = 1;
        // 顺着parentId往上计算level
        while (parentId != 0l) {
            parentId = dtbBookTypeMapper.getParentIdByTypeId(parentId);
            level++;
            if (level > 5) {
                log.error("操作用户Id: {}, typeId: {} 类型层级错误，最多只有五级", SecurityUtils.getUserId(), dtbBookType.getTypeId());
                throw new ServiceException("类型层级错误，最多只有五级");
            }
        }

        // 判断是否新增
        if (dtbBookType.getTypeId() == null) {
            return ;
        }
        // 顺着parentId往下计算level
        // 使用Deque模拟栈
        Deque<DtbBookType> stackDtbBookType = new ArrayDeque<>();
        Deque<Integer> stackLevel = new ArrayDeque<>();
        stackDtbBookType.push(dtbBookType);
        stackLevel.push(level);

        while (!stackDtbBookType.isEmpty()) {
            dtbBookType = stackDtbBookType.pop();
            level = stackLevel.pop();
            List<Long> typeIdList = dtbBookTypeMapper.getTypeIdListByParentId(dtbBookType.getTypeId());
            for (Long typeId : typeIdList) {
                DtbBookType bookType = new DtbBookType();
                bookType.setTypeId(typeId);
                if (level + 1 <= 5) {
                    stackDtbBookType.push(bookType);
                    stackLevel.push(level + 1);
                } else {
                    log.error("操作用户Id: {}, typeId: {} 类型层级错误，最多只有五级", SecurityUtils.getUserId(), dtbBookType.getTypeId());
                    throw new ServiceException("类型层级错误，最多只有五级");
                }
            }
        }
    }

    /**
     * 修改中图分类
     *
     * @param dtbBookType 中图分类
     * @return 结果
     */
    @Override
    public boolean updateDtbBookType(DtbBookType dtbBookType) {
        // 检测重复
        checkIsRepeat(dtbBookType);

        // 检测是否形成环
        checkIsRing(dtbBookType.getTypeId(), dtbBookType.getParentId());

        // 检测层级
        checkLevel(dtbBookType);
        return this.updateById(dtbBookType);
    }

    /**
     * 批量删除中图分类
     *
     * @param typeId 需要删除的中图分类主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTypeByTypeIds(Long typeId) {
        // 检测当前分类是否被教材使用
        int count = dtbBookTypeMapper.checkBookCountByTypeId(typeId);
        if (count > 0) {
            throw new ServiceException("该分类已绑定了教材，不可删除");
        }

        // 检查子级分类是否绑定了教材
        String bindChildTypeCode = checkChildrenBindBook(typeId);
        if (StringUtils.isNotEmpty(bindChildTypeCode)) {
            throw new ServiceException("该分类下子级分类\"" + bindChildTypeCode + "\"已绑定了教材，不可删除");
        }

        // 递归删除所有子分类
        Set<Long> visitedIds = new HashSet<>();
        deleteChildrenTypes(typeId, visitedIds);
        
        // 删除当前分类
        return this.removeById(typeId);
    }
    
    /**
     * 检查子级分类是否绑定了教材
     *
     * @param parentId 父分类ID
     * @return 绑定了教材的子级分类编码，如果没有则返回null
     */
    private String checkChildrenBindBook(Long parentId) {
        Set<Long> visitedIds = new HashSet<>();
        return checkChildrenBindBook(parentId, visitedIds);
    }
    
    /**
     * 检查子级分类是否绑定了教材（带环检测）
     *
     * @param parentId 父分类ID
     * @param visitedIds 已访问的分类ID集合
     * @return 绑定了教材的子级分类编码，如果没有则返回null
     */
    private String checkChildrenBindBook(Long parentId, Set<Long> visitedIds) {
        // 如果当前节点已被访问过，说明存在环
        if (visitedIds.contains(parentId)) {
            log.error("操作用户Id: {}, 检测到分类数据存在环形结构，parentId: {}", 
                    SecurityUtils.getUserId(), parentId);
            throw new ServiceException("检测到分类数据存在环形结构，请联系管理员");
        }
        
        // 标记当前节点已访问
        visitedIds.add(parentId);
        
        LambdaQueryWrapper<DtbBookType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DtbBookType::getParentId, parentId);
        List<DtbBookType> childTypes = this.list(queryWrapper);
        
        for (DtbBookType childType : childTypes) {
            // 检查当前子分类是否绑定了教材
            int count = dtbBookTypeMapper.checkBookCountByTypeId(childType.getTypeId());
            if (count > 0) {
                return childType.getTypeCode();
            }
            
            // 递归检查更深层次的子分类
            String bindChildTypeCode = checkChildrenBindBook(childType.getTypeId(), visitedIds);
            if (StringUtils.isNotEmpty(bindChildTypeCode)) {
                return bindChildTypeCode;
            }
        }
        return null;
    }
    
    /**
     * 递归删除所有子分类
     *
     * @param parentId 父分类ID
     * @param visitedIds 已访问的分类ID集合，用于检测环
     */
    private void deleteChildrenTypes(Long parentId, Set<Long> visitedIds) {
        // 如果当前节点已被访问过，说明存在环
        if (visitedIds.contains(parentId)) {
            log.error("操作用户Id: {}, 检测到分类数据存在环形结构，parentId: {}", 
                    SecurityUtils.getUserId(), parentId);
            throw new ServiceException("检测到分类数据存在环形结构，请联系管理员");
        }
        
        // 标记当前节点已访问
        visitedIds.add(parentId);
        
        List<Long> childTypeIds = dtbBookTypeMapper.getTypeIdListByParentId(parentId);
        for (Long childTypeId : childTypeIds) {
            // 先递归删除子分类的子分类
            deleteChildrenTypes(childTypeId, visitedIds);
            // 再删除子分类
            this.removeById(childTypeId);
        }
    }

}
