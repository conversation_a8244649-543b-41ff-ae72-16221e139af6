package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbUserFlashCard;
import cn.dutp.book.service.IDtbUserFlashCardService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;
import com.alibaba.fastjson.JSONObject;

/**
 * 助记卡Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/card")
public class DtbUserFlashCardController extends BaseController
{
    @Autowired
    private IDtbUserFlashCardService dtbUserFlashCardService;

    /**
     * 查询助记卡列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbUserFlashCard dtbUserFlashCard)
    {
        startPage();
        List<DtbUserFlashCard> list = dtbUserFlashCardService.selectDtbUserFlashCardList(dtbUserFlashCard);
        return getDataTable(list);
    }

    /**
     * 导出助记卡列表
     */
    @Log(title = "导出助记卡", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbUserFlashCard dtbUserFlashCard)
    {
        List<DtbUserFlashCard> list = dtbUserFlashCardService.selectDtbUserFlashCardList(dtbUserFlashCard);
        ExcelUtil<DtbUserFlashCard> util = new ExcelUtil<DtbUserFlashCard>(DtbUserFlashCard.class);
        util.exportExcel(response, list, "助记卡数据");
    }

    /**
     * 获取助记卡详细信息
     */
    @GetMapping(value = "/{cardId}")
    public AjaxResult getInfo(@PathVariable("cardId") Long cardId)
    {
        return success(dtbUserFlashCardService.selectDtbUserFlashCardByCardId(cardId));
    }

    /**
     * 新增助记卡
     */
    @Log(title = "新增助记卡", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DtbUserFlashCard dtbUserFlashCard)
    {
        dtbUserFlashCard.setUserId(SecurityUtils.getUserId());
        return toAjax(dtbUserFlashCardService.insertDtbUserFlashCard(dtbUserFlashCard));
    }

    /**
     * 修改助记卡
     */
    @Log(title = "修改助记卡", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbUserFlashCard dtbUserFlashCard)
    {
        return toAjax(dtbUserFlashCardService.updateDtbUserFlashCard(dtbUserFlashCard));
    }

    /**
     * 删除助记卡
     */
    @Log(title = "删除助记卡", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cardIds}")
    public AjaxResult remove(@PathVariable Long[] cardIds)
    {
        return toAjax(dtbUserFlashCardService.deleteDtbUserFlashCardByCardIds(Arrays.asList(cardIds)));
    }

    /**
     * AI生成助记卡（预览）
     * @param requestData 包含选中文本和卡片类型的请求数据
     * @return 生成的助记卡预览内容（未保存到数据库，需要前端调用add接口保存）
     */
    @Log(title = "AI生成助记卡", businessType = BusinessType.INSERT)
    @PostMapping("/generateCard")
    public AjaxResult generateCard(@RequestBody JSONObject requestData)
    {
        try {
            String sourceText = requestData.getString("sourceText");
            Integer cardType = requestData.getInteger("cardType");
            
            if (sourceText == null || sourceText.trim().isEmpty()) {
                return error("选中文本不能为空");
            }
            
            if (cardType == null || (cardType != 1 && cardType != 2 && cardType != 3)) {
                return error("卡片类型无效，请选择：1简答题/2选择题/3主题卡片");
            }

            // 调用Service层方法生成助记卡预览
            DtbUserFlashCard flashCard = dtbUserFlashCardService.generateFlashCard(
                sourceText, cardType, SecurityUtils.getUserId());
            
            return success(flashCard);
            
        } catch (Exception e) {
            logger.error("AI生成助记卡失败", e);
            return error("生成失败：" + e.getMessage());
        }
    }
}
