package cn.dutp.book.mapper;


import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.book.domain.DtbBookChapterData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 章节数据统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Repository
public interface DtbBookChapterDataMapper extends BaseMapper<DtbBookChapterData> {

    List<DtbBookChapterData> selectDtbBookChapterDataList(DtbBookChapterData dtbBookChapterData);

    List<DtbBookChapterData> dataOverview(DtbBookChapterData dtbBookChapterData);

    DtbBookChapterData dataOverviewOne(DtbBookChapterData dtbBookChapterData);

    DtbBookChapterData queryChapterDataByChapterSortAndBookId(@Param("bookId") Long oldBookId, @Param("sort") Integer sort);

    DtbBookChapterData queryChapterDataByChapterId(Long chapterId);
}
