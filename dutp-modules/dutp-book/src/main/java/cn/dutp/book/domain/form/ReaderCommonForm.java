package cn.dutp.book.domain.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class ReaderCommonForm {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    private Integer sort;
    private Integer noteType;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    private String copyText;
    private String color;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long shareId;
    private String keyword;
    /**
     * 0.资源 1实训
     */
    private Integer resourceType;

    private Integer isFree;

    private String fileType;

    private Long versionId;
}
