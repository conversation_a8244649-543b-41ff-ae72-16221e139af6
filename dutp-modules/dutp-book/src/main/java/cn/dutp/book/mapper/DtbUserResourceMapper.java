package cn.dutp.book.mapper;

import cn.dutp.book.domain.DtbUserResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户资源库Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Repository
public interface DtbUserResourceMapper extends BaseMapper<DtbUserResource> {


    /**
     * 查询回收站列表
     *
     * @param dtbUserResource 用户资源库
     * @return 用户资源库集合
     */
    List<DtbUserResource> selectDtbUserResourceListByRecycle(DtbUserResource dtbUserResource);

    /**
     * 根据资源ID列表查询用户资源
     *
     * @param resourceIds 资源ID列表
     * @return 用户资源列表
     */
    List<DtbUserResource> selectUserResourceByIds(@Param("resourceIds") List<Long> resourceIds);

    boolean updateDtbUserResource(DtbUserResource dtbUserResource);

    @Select("select file_url from dtb_user_resource where del_flag = 0 and file_type = 1 and hash = #{hash} and user_id = #{userId} limit 1")
    String queryByHash(@Param("hash") String hash, @Param("userId") Long userId);
}
