<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.book.mapper.DtbBookTemplateMapper">
    

    <select id="selectDtbBookTemplateByChapterId" resultType="cn.dutp.book.domain.vo.DtbBookTemplateVO">
        select
            dbt.template_id as id,
            dbt.modal,
            dbt.img_url,
            dbt.header_url,
            dbt.content_url,
            dbt.footer_url,
            dbt.chapter_header_url,
            dbt.chapter_font_color,
            dbt.chapter_header_height,
            dbt.joint_header_url,
            dbt.joint_font_color,
            dbt.joint_height,
            dbt.order_template_bg_url,
            dbt.order_template_margin_left,
            dbt.order_template_color,
            dbt.pages_font_color,
            dbt.pages_align,
            dbt.theme,
            dbt.pages_position
        from dtb_book_template dbt
            inner join dtb_book_chapter dbc on dbt.template_id = dbc.template_id
        where dbt.del_flag = 0 and dbc.chapter_id = #{chapterId}
    </select>
    <select id="selectDtbBookTemplateList" resultType="cn.dutp.book.domain.DtbBookTemplate">
        SELECT
            t.*
        <if test="templateId != null">
            ,CASE WHEN t.template_id = #{templateId} THEN true ELSE false END as is_use
        </if>
        FROM
            dtb_book_template t
        WHERE
            t.del_flag = 0
        <if test="modal != null and modal != ''">
            and t.modal like concat('%', #{modal}, '%')
        </if>
        <if test="serialNumber != null and serialNumber != ''">
            and t.serial_number like concat('%', #{serialNumber}, '%')
        </if>
        <if test="type != null">
            and t.type = #{type}
        </if>
        <if test="themeColor != null and themeColor != ''">
            and t.theme_color = #{themeColor}
        </if>
        <if test="isDefault != null and isDefault != ''">
            and t.is_default = #{isDefault}
        </if>
        <if test="sortBy != null">
            ORDER BY
            <if test="templateId != null">
                CASE WHEN t.template_id=#{templateId} THEN 0 ELSE 1 END
            </if>

            <if test="sortBy == 1">
                ,t.create_time DESC
            </if>
            <if test="sortBy == 2">
                ,t.create_time asc
            </if>
            <if test="sortBy == 3">
                ,t.update_time DESC
            </if>
            <if test="sortBy == 4">
                ,t.modal asc
            </if>
        </if>
        <if test="sortBy == null">
            ORDER BY
            <if test="templateId != null">
                CASE WHEN t.template_id=#{templateId} THEN 0 ELSE 1 END,
            </if>
                t.sort asc, t.create_time DESC
        </if>
    </select>

</mapper>