package cn.dutp.book.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 用户资源库对象 dtb_user_resource
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@TableName("dtb_user_resource")
public class DtbUserResource extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resourceId;

    /**
     * sys_user里的user_id
     */
    @Excel(name = "sys_user里的user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 文件名
     */
    @Excel(name = "文件名")
    private String fileName;

    /**
     * 文件地址
     */
    @Excel(name = "文件地址")
    private String fileUrl;

    /**
     * 文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题，8课件
     */
    @Excel(name = "文件类型1=图片，2=音频，3=视频，4=虚拟仿真，5=AR/VR，6=3D模型，7习题，8课件")
    private String fileType;

    /**
     * 文件大小
     */
    @Excel(name = "文件大小")
    private Long fileSize;

    /**
     * 习题的题目ID
     */
    @Excel(name = "习题的题目ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 文件夹id
     */
    @Excel(name = "文件夹id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    /**
     * 文件夹名称
     */
    @TableField(exist = false)
    private String folderName;

    /**
     * 文件hash
     */
    private String hash;
}
