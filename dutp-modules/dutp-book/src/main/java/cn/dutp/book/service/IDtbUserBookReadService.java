package cn.dutp.book.service;

import java.util.List;
import cn.dutp.book.domain.DtbUserBookRead;
import cn.dutp.common.core.web.domain.AjaxResult;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 用户阅读记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IDtbUserBookReadService extends IService<DtbUserBookRead>
{
    /**
     * 查询用户阅读记录
     *
     * @param readId 用户阅读记录主键
     * @return 用户阅读记录
     */
    public DtbUserBookRead selectDtbUserBookReadByReadId(Long readId);

    /**
     * 查询用户阅读记录列表
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 用户阅读记录集合
     */
    public List<DtbUserBookRead> selectDtbUserBookReadList(DtbUserBookRead dtbUserBookRead);

    /**
     * 新增用户阅读记录
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 结果
     */
    public AjaxResult insertDtbUserBookRead(DtbUserBookRead dtbUserBookRead);

    /**
     * 修改用户阅读记录
     *
     * @param dtbUserBookRead 用户阅读记录
     * @return 结果
     */
    public boolean updateDtbUserBookRead(DtbUserBookRead dtbUserBookRead);

    /**
     * 批量删除用户阅读记录
     *
     * @param readIds 需要删除的用户阅读记录主键集合
     * @return 结果
     */
    public boolean deleteDtbUserBookReadByReadIds(List<Long> readIds);

    AjaxResult updateUserReadTime(Long readId, Integer readTime);

    AjaxResult getReadRole();
}
