package cn.dutp.book.controller;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.book.domain.EsDtbBook;
import cn.dutp.book.esmapper.EsDtbBooksMapper;
import cn.dutp.book.mapper.DtbBookBookMapper;
import cn.dutp.book.mapper.DtbBookMapper;
import cn.dutp.book.service.IDtbBookService;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 搜索引擎
 *
 * <AUTHOR>
 */
@ConditionalOnProperty(value = "easy-es.enable", havingValue = "true")
@RequiredArgsConstructor
@RestController
@RequestMapping("/es")
@Slf4j
public class EsCrudController extends BaseController {
    private final EsDtbBooksMapper esDtbBooksMapper;

    @Autowired
    private DtbBookMapper dtbBookMapper;
    /**
     *
     * es查询
     *
     */
    @PostMapping("/searchBook")
    public AjaxResult searchBook(@RequestBody EsDtbBook book) {

        startPage();
        LambdaEsQueryWrapper<EsDtbBook> wrapper = new LambdaEsQueryWrapper<>();

        wrapper.eq(ObjectUtil.isNotEmpty(book.getTopSubjectId()), EsDtbBook::getTopSubjectId, book.getTopSubjectId());
        wrapper.eq(ObjectUtil.isNotEmpty(book.getSecondSubjectId()), EsDtbBook::getSecondSubjectId, book.getSecondSubjectId());
        wrapper.eq(ObjectUtil.isNotEmpty(book.getThirdSubjectId()), EsDtbBook::getThirdSubjectId, book.getThirdSubjectId());
        wrapper.eq(ObjectUtil.isNotEmpty(book.getForthSubjectId()), EsDtbBook::getForthSubjectId, book.getForthSubjectId());
        wrapper.eq(ObjectUtil.isNotEmpty(book.getBookType()), EsDtbBook::getBookType, book.getBookType());
        wrapper.not(w -> w.eq(EsDtbBook::getMasterFlag, DutpConstant.NUM_THREE));

        wrapper.and(w -> {
            if (StringUtils.isNotBlank(book.getBookName())) {
                w.or(orWrapper -> orWrapper.match(EsDtbBook::getBookName, book.getBookName()));
            }
            if (StringUtils.isNotBlank(book.getAuthorValue())) {
                w.or(orWrapper -> orWrapper.match(EsDtbBook::getAuthorValue, book.getAuthorValue()));
            }
            if (StringUtils.isNotBlank(book.getIsbn())) {
                w.or(orWrapper -> orWrapper.match(EsDtbBook::getIsbn, book.getIsbn()));
            }
        });
        return success(esDtbBooksMapper.pageQuery(wrapper,book.getPageNum(),book.getPageSize()));
    }
    /**
     *
     * es批量追加上架教材
     *
     */
    /**
     *
     * es批量追加上架教材
     *
     */
    @GetMapping("/insBatchEsBook")
    public AjaxResult insBatchEsBook() {
        // 在类中添加日志打印
//        LogFactory.useStdOutLogging(); // 使用标准输出打印SQL日志
        LambdaQueryWrapper<DtbBook> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DtbBook::getShelfState, DutpConstant.NUM_ONE,DutpConstant.NUM_FOUR);
        wrapper.in(DtbBook::getMasterFlag, DutpConstant.NUM_ONE,DutpConstant.NUM_TWO);
        wrapper.eq(DtbBook::getBookOrganize, DutpConstant.NUM_ONE);

        List<DtbBook> dtBookList = dtbBookMapper.selectList(wrapper);
//        logger.debug("Executed SQL: {}", wrapper.getSqlSegment());
        List<EsDtbBook> esDtbBookList = new ArrayList<>();
        for (DtbBook dtbBook : dtBookList) {
            EsDtbBook esDtbBook = new EsDtbBook();
            esDtbBook.setBookId(dtbBook.getBookId());
            esDtbBook.setBookName(dtbBook.getBookName());
            esDtbBook.setAuthorLabel(dtbBook.getAuthorLabel());
            esDtbBook.setAuthorValue(dtbBook.getAuthorValue());
            esDtbBook.setCover(dtbBook.getCover());
            esDtbBook.setIsbn(dtbBook.getIsbn());
            esDtbBook.setBookNo(dtbBook.getBookNo());
            esDtbBook.setPublishDate(DateUtil.formatDate(dtbBook.getPublishDate()));
            esDtbBook.setBookType(dtbBook.getBookType());
            esDtbBook.setPriceCounter(dtbBook.getPriceCounter());
            esDtbBook.setPriceSale(dtbBook.getPriceSale());
            esDtbBook.setShelfState(dtbBook.getShelfState());
            esDtbBook.setShelfTime(DateUtil.formatDate(dtbBook.getShelfTime()));
            esDtbBook.setReadQuantity(dtbBook.getReadQuantity());
            esDtbBook.setTopSubjectId(dtbBook.getTopSubjectId());
            esDtbBook.setSecondSubjectId(dtbBook.getSecondSubjectId());
            esDtbBook.setThirdSubjectId(dtbBook.getThirdSubjectId());
            esDtbBook.setForthSubjectId(dtbBook.getForthSubjectId());
            esDtbBook.setMasterFlag(dtbBook.getMasterFlag());
            esDtbBookList.add(esDtbBook);
        }
        esDtbBooksMapper.delete(new LambdaEsQueryWrapper<>());
        return success(esDtbBooksMapper.insertBatch(esDtbBookList));
    }
}
