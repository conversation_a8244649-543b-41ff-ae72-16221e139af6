package cn.dutp.book.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.book.domain.DtbBookQuestionAnswer;
import cn.dutp.book.service.IDtbBookQuestionAnswerService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 问题答案Controller
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@RestController
@RequestMapping("/questionAnswer")
public class DtbBookQuestionAnswerController extends BaseController
{
    @Autowired
    private IDtbBookQuestionAnswerService dtbBookQuestionAnswerService;

    /**
     * 查询问题答案列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        startPage();
        List<DtbBookQuestionAnswer> list = dtbBookQuestionAnswerService.selectDtbBookQuestionAnswerList(dtbBookQuestionAnswer);
        return getDataTable(list);
    }

    /**
     * 导出问题答案列表
     */
    @RequiresPermissions("book:questionAnswer:export")
    @Log(title = "导出问题答案", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        List<DtbBookQuestionAnswer> list = dtbBookQuestionAnswerService.selectDtbBookQuestionAnswerList(dtbBookQuestionAnswer);
        ExcelUtil<DtbBookQuestionAnswer> util = new ExcelUtil<DtbBookQuestionAnswer>(DtbBookQuestionAnswer.class);
        util.exportExcel(response, list, "问题答案数据");
    }

    /**
     * 获取问题答案详细信息
     */
    @RequiresPermissions("book:questionAnswer:query")
    @GetMapping(value = "/{answerId}")
    public AjaxResult getInfo(@PathVariable("answerId") Long answerId)
    {
        return success(dtbBookQuestionAnswerService.selectDtbBookQuestionAnswerByAnswerId(answerId));
    }

    /**
     * 新增问题答案
     */
    @Operation(
            summary = "新增问题答案",
            description = "阅读器答题结果保存",
            tags = "新增问题答案",
            parameters = {
                    @Parameter(name = "userQuestionId", description = "小题ID", required = true),
                    @Parameter(name = "bookQuestionId", description = "", required = false),
                    @Parameter(name = "chapterId", description = "章节ID", required = false),
                    @Parameter(name = "answerContent", description = "答题结果", required = false),
                    @Parameter(name = "score", description = "100表示完全正确0表示完全错误", required = false),
                    @Parameter(name = "bookId", description = "教材ID", required = false),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "成功"),
            }
    )
    @Log(title = "新增问题答案", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody String jsonString)
    {
        try {
            logger.info("接收到的原始JSON: " + jsonString);

            // 使用Jackson的宽松解析模式
            ObjectMapper objectMapper = new ObjectMapper();
            // 配置ObjectMapper以处理格式问题
            objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
            objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);

            DtbBookQuestionAnswer dtbBookQuestionAnswer = objectMapper.readValue(jsonString, DtbBookQuestionAnswer.class);

            return toAjax(dtbBookQuestionAnswerService.insertDtbBookQuestionAnswer(dtbBookQuestionAnswer));

        } catch (JsonProcessingException e) {
            logger.error("JSON解析失败: " + e.getMessage(), e);
            logger.error("问题JSON: " + jsonString);
            return AjaxResult.error("JSON格式错误，请检查数据格式: " + e.getMessage());
        } catch (Exception e) {
            logger.error("处理请求失败: " + e.getMessage(), e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }


    /**
     * 新增问题答案 - 使用对象接收（原始方法）
     */
    @Operation(
            summary = "新增问题答案（对象）",
            description = "阅读器答题结果保存 - 使用对象接收",
            tags = "新增问题答案"
    )
    @Log(title = "新增问题答案", businessType = BusinessType.INSERT)
    @PostMapping("/addByObject")
    public AjaxResult addByObject(HttpServletRequest request)
    {
        try {
            // 先尝试读取原始请求体进行调试
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            try (BufferedReader reader = request.getReader()) {
                while ((line = reader.readLine()) != null) {
                    jsonBuilder.append(line);
                }
            }

            String jsonString = jsonBuilder.toString();
            logger.info("addByObject接收到的原始JSON: " + jsonString);

            // 使用宽松的Jackson配置解析
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
            objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
            objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

            DtbBookQuestionAnswer dtbBookQuestionAnswer = objectMapper.readValue(jsonString, DtbBookQuestionAnswer.class);

            logger.info("解析成功的对象数据: " + dtbBookQuestionAnswer.toString());
            return toAjax(dtbBookQuestionAnswerService.insertDtbBookQuestionAnswer(dtbBookQuestionAnswer));

        } catch (JsonProcessingException e) {
            logger.error("addByObject JSON解析失败: " + e.getMessage(), e);
            return AjaxResult.error("JSON格式错误: " + e.getMessage());
        } catch (Exception e) {
            logger.error("addByObject处理请求失败: " + e.getMessage(), e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 修改问题答案
     */
    @Log(title = "修改问题答案", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DtbBookQuestionAnswer dtbBookQuestionAnswer)
    {
        return toAjax(dtbBookQuestionAnswerService.updateDtbBookQuestionAnswer(dtbBookQuestionAnswer));
    }

    /**
     * 删除问题答案
     */
    @RequiresPermissions("book:questionAnswer:remove")
    @Log(title = "删除问题答案", businessType = BusinessType.DELETE)
    @DeleteMapping("/{answerIds}")
    public AjaxResult remove(@PathVariable Long[] answerIds)
    {
        return toAjax(dtbBookQuestionAnswerService.deleteDtbBookQuestionAnswerByAnswerIds(Arrays.asList(answerIds)));
    }
}
