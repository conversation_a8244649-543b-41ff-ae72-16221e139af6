package cn.dutp.book.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 助记卡对象 dtb_user_flash_card
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@TableName("dtb_user_flash_card")
public class DtbUserFlashCard extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** $column.columnComment */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long cardId;

    /** 用户ID */
        @Excel(name = "用户ID")
    private Long userId;

    /** 教材ID */
        @Excel(name = "教材ID")
    private Long bookId;

    /** 章节ID */
        @Excel(name = "章节ID")
    private Long chapterId;

    /** 页码 */
        @Excel(name = "页码")
    private Long pageNumber;

    /** 原文内容（用户选中的内容） */
        @Excel(name = "原文内容", readConverterExp = "用=户选中的内容")
    private String sourceText;

    /** 卡片类型：1简答题 2选择题 3主题卡片 */
        @Excel(name = "卡片类型：1简答题 2选择题 3主题卡片")
    private Integer cardType;

    /** 问题内容（简答题/选择题） */
        @Excel(name = "问题内容", readConverterExp = "简=答题/选择题")
    private String question;

    /** 选择题选项（JSON格式） */
        @Excel(name = "选择题选项", readConverterExp = "J=SON格式")
    private String options;

    /** 答案内容 */
        @Excel(name = "答案内容")
    private String answer;

    /** 记忆状态：0未学习 1需复习 2已掌握 */
        @Excel(name = "记忆状态：0未学习 1需复习 2已掌握")
    private Integer memoryStatus;

    /** 复习次数 */
        @Excel(name = "复习次数")
    private Long reviewCount;

    /** 最后复习时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "最后复习时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastReviewTime;

    /** 下次复习时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "下次复习时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date nextReviewTime;

    /** 删除标志（0存在 2删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cardId", getCardId())
            .append("userId", getUserId())
            .append("bookId", getBookId())
            .append("chapterId", getChapterId())
            .append("pageNumber", getPageNumber())
            .append("sourceText", getSourceText())
            .append("cardType", getCardType())
            .append("question", getQuestion())
            .append("options", getOptions())
            .append("answer", getAnswer())
            .append("memoryStatus", getMemoryStatus())
            .append("reviewCount", getReviewCount())
            .append("lastReviewTime", getLastReviewTime())
            .append("nextReviewTime", getNextReviewTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }
