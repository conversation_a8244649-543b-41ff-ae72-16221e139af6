package cn.dutp.book.controller;

import cn.dutp.api.common.utils.RSAUtils;
import cn.dutp.book.domain.*;
import cn.dutp.book.domain.form.ReaderCommonForm;
import cn.dutp.book.domain.vo.DtbBookResourceVO;
import cn.dutp.book.domain.vo.DtbUserQuestionVO;
import cn.dutp.book.service.*;
import cn.dutp.common.core.constant.HttpStatus;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.log.enums.OperatorType;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.form.BookFaultForm;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * DUTP-DTB_002数字教材Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/readerApi")
public class DtbBookReaderApiController extends BaseController {
    @Autowired
    private IDtbUserBookReadService dtbUserBookReadService;
    @Autowired
    private IDtbBookService iDtbBookService;
    @Autowired
    private RSAUtils rsaUtils;
    @Autowired
    private IDtbUserBookService dtbUserBookService;
    @Autowired
    private IDtbBookChapterService iDtbBookChapterService;
    @Autowired
    private IDtbUserBookNoteService dtbUserBookNoteService;
    @Autowired
    private IDtbUserBookMarkService dtbUserBookMarkService;
    @Autowired
    private IDtbUserBookLineService dtbUserBookLineService;
    @Autowired
    private IDtbBookResourceService dtbBookResourceService;
    @Autowired
    private IDtbBookChapterContentService dtbBookChapterContentService;
    @Autowired
    private IDtbUserBookResourceLogService dtbUserBookResourceLogService;
    @Autowired
    private IDtbUserBookConfigService dtbUserBookConfigService;
    @Autowired
    private RemoteUserMessageService remoteUserMessageService;
    @Autowired
    private IDutpAiVoiceService dutpAiVoiceService;
    @Autowired
    private IDutpAiTranslationLanguageService dutpAiTranslationLanguageService;

    @Tag(name = "获取章节内容", description = "获取章节内容，章节id需加密")
    @GetMapping("/getChapterContent")
    public AjaxResult getChapterContent(@RequestParam String id) {
        Long chapterId;
        try {
//            chapterId = Long.parseLong(rsaUtils.decrypt(id));
            chapterId = Long.parseLong(id);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "无效的章节");
        }
        return dtbBookChapterContentService.getChapterContentByChapterId(chapterId);
    }


    @Tag(name = "检索章节内容", description = "检索章节内容")
    @GetMapping("/getQueryChapterContent")
    public AjaxResult getQueryChapterContent(ReaderCommonForm readerCommonForm) {
        return dtbBookChapterContentService.getQueryChapterContent(readerCommonForm);
    }

    @Tag(name = "检索所有教材章节内容", description = "检索所有教材章节内容")
    @GetMapping("/getAllQueryChapterContent")
    public Map<String, Object> getAllQueryChapterContent(ReaderCommonForm readerCommonForm) {
        return dtbBookChapterContentService.getAllQueryChapterContent(readerCommonForm);
    }

    @Tag(name = "获取用户和教材的关联ID", description = "获取用户和教材的关联ID，教材ID需要加密")
    @GetMapping("/getUserBookId")
    public AjaxResult getUserBookId(@RequestParam String id) {
        Long bookId;
        try {
//            bookId = Long.parseLong(rsaUtils.decrypt(id));
            bookId = Long.parseLong(id);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "无效的章节");
        }
        return dtbUserBookService.getUserBookId(bookId);
    }

    @Tag(name = "开始阅读", description = "点击书籍进入阅读，id是bookId,需要加密")
    @GetMapping("/startRead")
    public AjaxResult startRead(@RequestParam String id, @RequestParam(required = false) String cId, @RequestParam(required = false, defaultValue = "0") Integer isMobile) {
        Long bookId = 0l;
        Long chapterId = null;
        try {
//            String  decryptString = rsaUtils.decrypt(id);
            if (ObjectUtil.isNotEmpty(cId)) {
                chapterId = Long.valueOf(cId);
            }

            bookId = Long.valueOf(id);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(HttpStatus.NO_BOOK, "无效的教材");
        }
        return dtbUserBookService.userStartRead(bookId, chapterId,isMobile);
    }

    @Tag(name = "保存用户配置", description = "保存用户阅读相关配置")
    @Log(title = "保存用户配置", businessType = BusinessType.UPDATE, operatorType = OperatorType.READER)
    @PostMapping("/saveBookConfig")
    public AjaxResult saveBookConfig(@RequestBody DtbUserBookConfig dtbUserBookConfig) {
        return dtbUserBookConfigService.updateDtbUserBookConfig(dtbUserBookConfig);
    }

    @Tag(name = "获取章节", description = "用户阅读获取章节")
    @GetMapping("/getChapter/{bookId}")
    public AjaxResult getChapter(@PathVariable Long bookId) {
        return iDtbBookChapterService.getBookChapters(bookId);
    }

    @Tag(name = "保存用户阅读信息", description = "保存用户阅读信息，前端翻页时保存")
    @Log(title = "保存用户阅读信息", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    @PostMapping("/saveBookRead")
    public AjaxResult saveBookRead(@RequestBody DtbUserBookRead dtbUserBookRead) {
        return dtbUserBookReadService.insertDtbUserBookRead(dtbUserBookRead);
    }

    /**
     * sort 1章节顺序2时间顺序
     * noteType 1我的笔记2班级笔记3教师笔记
     *
     * @return
     */
    @Tag(name = "查询笔记", description = "查询读者的笔记列表")
    @GetMapping("/getBookNote")
    public AjaxResult getBookNote(ReaderCommonForm readerForm) {
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNull(userId)) {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "请登录");
        } else {
            readerForm.setUserId(userId);
            return dtbUserBookNoteService.selectReaderUserBookNoteList(readerForm);
        }
    }

    @Tag(name = "保存笔记", description = "保存读者的笔记")
    @Log(title = "保存笔记", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    @PostMapping("/saveBookNote")
    public AjaxResult saveBookNote(@RequestBody DtbUserBookNote dtbUserBookNote) {
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNull(userId)) {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "请登录");
        } else {
            dtbUserBookNote.setUserId(SecurityUtils.getUserId());
            return dtbUserBookNoteService.insertDtbUserBookNote(dtbUserBookNote);
        }
    }

    @Tag(name = "修改笔记", description = "修改读者的笔记")
    @Log(title = "修改笔记", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    @PostMapping("/updateBookNote")
    public AjaxResult updateBookNote(@RequestBody DtbUserBookNote dtbUserBookNote) {
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNull(userId)) {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "请登录");
        } else {
            dtbUserBookNote.setUserId(SecurityUtils.getUserId());
            return dtbUserBookNoteService.updateDtbUserBookNote(dtbUserBookNote);
        }
    }

    @Tag(name = "删除笔记", description = "读者删除笔记")
    @Log(title = "保存笔记", businessType = BusinessType.DELETE, operatorType = OperatorType.READER)
    @DeleteMapping("/deleteBookNote/{noteId}")
    public AjaxResult deleteBookNote(@PathVariable Long noteId) {
        List<Long> ids = new ArrayList<>();
        ids.add(noteId);
        dtbUserBookNoteService.deleteDtbUserBookNoteByNoteIds(ids);
        return AjaxResult.success();
    }

    /**
     * 导出笔记
     *
     * @param dtbUserBookNote
     * @return
     */
    @PostMapping("/exportBookNote")
    @Log(title = "导出笔记", businessType = BusinessType.EXPORT, operatorType = OperatorType.READER)
    public void exportBookNote(HttpServletResponse response, DtbUserBookNote dtbUserBookNote) {
        dtbUserBookNote.setUserId(SecurityUtils.getUserId());
        dtbUserBookNoteService.exportBookNote(response, dtbUserBookNote);
    }

    @Tag(name = "查询书签", description = "查询读者书签")
    @GetMapping("/getBookMark/{bookId}")
    public AjaxResult getBookMark(@PathVariable Long bookId) {
        DtbUserBookMark dtbUserBookMark = new DtbUserBookMark();
        dtbUserBookMark.setBookId(bookId);
        dtbUserBookMark.setUserId(SecurityUtils.getUserId());
        return dtbUserBookMarkService.selectReaderUserBookMarkList(dtbUserBookMark);
    }

    @Tag(name = "保存书签", description = "保存读者书签")
    @PostMapping("/saveBookMark")
    @Log(title = "保存书签", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    public AjaxResult saveBookMark(@RequestBody DtbUserBookMark dtbUserBookMark) {
        Long userId = SecurityUtils.getUserId();
        if (ObjectUtil.isNull(userId)) {
            return AjaxResult.error(HttpStatus.FORBIDDEN, "请登录");
        } else {
            dtbUserBookMark.setUserId(SecurityUtils.getUserId());
            return dtbUserBookMarkService.insertDtbUserBookMark(dtbUserBookMark);
        }
    }

    @Tag(name = "书签列表删除书签", description = "书签列表删除读者书签")
    @PostMapping("/deleteBookMark/{markId}")
    @Log(title = "书签列表删除书签", businessType = BusinessType.DELETE, operatorType = OperatorType.READER)
    public AjaxResult deleteBookMark(@PathVariable Long markId) {
        return dtbUserBookMarkService.deleteBookMark(markId);
    }

    @Tag(name = "页码删除书签", description = "当前页删除读者书签")
    @GetMapping("/deleteBookMarkByPage/{pageNumber}/{chapterId}")
    @Log(title = "页码删除书签", businessType = BusinessType.DELETE, operatorType = OperatorType.READER)
    public AjaxResult deleteBookMarkByPage(@PathVariable Integer pageNumber, @PathVariable Long chapterId) {
        dtbUserBookMarkService.deleteDtbUserBookMarkByPage(pageNumber, chapterId);
        return AjaxResult.success();
    }

    @Tag(name = "查询划线", description = "查询读者划线")
    @GetMapping("/getBookLine")
    public AjaxResult getBookLine(ReaderCommonForm readerCommonForm) {
        DtbUserBookLine bookLine = new DtbUserBookLine();
        bookLine.setBookId(readerCommonForm.getBookId());
        bookLine.setUserId(SecurityUtils.getUserId());
        bookLine.setColor(readerCommonForm.getColor());
        return dtbUserBookLineService.selectReaderUserBookLineList(bookLine);
    }

    @Tag(name = "保存划线", description = "保存读者划线")
    @PostMapping("/saveBookLine")
    @Log(title = "保存划线", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    public AjaxResult saveBookLine(@RequestBody DtbUserBookLine dtbUserBookLine) {
        dtbUserBookLine.setUserId(SecurityUtils.getUserId());
        return dtbUserBookLineService.insertDtbUserBookLine(dtbUserBookLine);
    }

    /**
     * 导出划线
     *
     * @param dtbUserBookLine
     * @return
     */
    @PostMapping("/exportBookLine")
    @Log(title = "导出划线", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    public void exportBookLine(HttpServletResponse response, DtbUserBookLine dtbUserBookLine) {
        dtbUserBookLine.setUserId(SecurityUtils.getUserId());
        dtbUserBookLineService.exportBookLine(response, dtbUserBookLine);
    }

    @Tag(name = "删除划线", description = "删除读者划线")
    @GetMapping("/deleteBookLine")
    @Log(title = "删除划线", businessType = BusinessType.DELETE, operatorType = OperatorType.READER)
    public AjaxResult deleteBookLine(DtbUserBookLine dtbUserBookLine) {
        dtbUserBookLine.setUserId(SecurityUtils.getUserId());
        return dtbUserBookLineService.deleteReaderDtbUserBookLine(dtbUserBookLine);
    }

    @Tag(name = "保存资源记录", description = "保存读者资源记录")
    @PostMapping("/saveBookResourceLog")
    @Log(title = "保存资源记录", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    public AjaxResult saveBookResourceLog(@RequestBody DtbUserBookResourceLog dtbUserBookResourceLog) {
        dtbUserBookResourceLog.setUserId(SecurityUtils.getUserId());
        dtbUserBookResourceLogService.insertDtbUserBookResourceLog(dtbUserBookResourceLog);
        return AjaxResult.success();
    }

    @Tag(name = "复制文字", description = "复制文字")
    @GetMapping("/bookCopyText")
    public AjaxResult bookCopyText(ReaderCommonForm readerCommonForm) {
        return dtbUserBookService.checkBookCopyText(readerCommonForm.getCopyText());
    }

    @Tag(name = "获取教材资源", description = "获取教材资源")
    @GetMapping("/getBookResource")
    public TableDataInfo getBookResource(ReaderCommonForm readerCommonForm) {
        startPage();
        List<DtbBookResourceVO> resources = dtbBookResourceService.getReaderBookResource(readerCommonForm);
        return getDataTable(resources);
    }

    @Tag(name = "获取教材资源根据章节筛选", description = "获取教材资源根据章节筛选")
    @GetMapping("/getBookResourceByChapter")
    public AjaxResult getBookResourceByChapter(ReaderCommonForm readerCommonForm) {
        return dtbBookResourceService.getReaderBookResourceByChapter(readerCommonForm);
    }

    @Tag(name = "获取教材资源试题", description = "获取教材资源试题")
    @GetMapping("/getBookQuestion")
    public TableDataInfo getBookQuestion(ReaderCommonForm readerCommonForm) {
        startPage();
        List<DtbUserQuestionVO> questions = dtbBookResourceService.getReaderBookQuestion(readerCommonForm);
        return getDataTable(questions);
    }

    @Tag(name = "获取关联教材", description = "获取该教材的关联教材")
    @GetMapping("/getBooks/{bookId}")
    public AjaxResult getBooks(@PathVariable Long bookId) {
        return iDtbBookService.getBooks(bookId);
    }

    @Operation(
            summary = "保存用户纠错",
            description = "阅读器保存纠错",
            tags = "保存用户纠错",
            parameters = {
                    @Parameter(name = "bookId", description = "教材ID", required = true),
                    @Parameter(name = "chapterId", description = "章节ID", required = true),
                    @Parameter(name = "pageNumber", description = "当前页码", required = true),
                    @Parameter(name = "fromWordId", description = "起始文字ID", required = true),
                    @Parameter(name = "endWordId", description = "结束文字ID", required = true),
                    @Parameter(name = "faultText", description = "纠错文本【教材内文字】", required = true),
                    @Parameter(name = "comment", description = "纠错留言【用户留言】", required = true),
                    @Parameter(name = "faultType", description = "纠错类型 字符串逗号隔开 // 1错别字2逻辑错误3内容错误4图片错误5其他", required = true),
                    @Parameter(name = "images", description = "纠错上传的图片JSON数组 例如[{\"fileName\":\"1.png\",\"fileUrl\":\"http://www.baidu.com\"},{\"fileName\":\"2.png\",\"fileUrl\":\"http://www.baidu.com\"}]", required = true)
            },
            responses = {
//                    @ApiResponse(responseCode = "200", description = "成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DutpSaleArea.class))),
                    @ApiResponse(responseCode = "200", description = "成功"),
            }
    )
    @PostMapping("/saveBookFault")
    @Log(title = "保存用户纠错", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    public AjaxResult saveBookFault(@RequestBody BookFaultForm bookFaultForm) {
        bookFaultForm.setUserId(SecurityUtils.getUserId());
        remoteUserMessageService.saveReaderBookFault(bookFaultForm);
        return AjaxResult.success();
    }

    @Operation(
            summary = "更新用户阅读时间",
            description = "更新用户阅读时间，每分钟更新一次阅读记录，时间不用累加，在切换页码时要立即更新。",
            tags = "更新用户阅读时间",
            parameters = {
                    @Parameter(name = "readId", description = "string 阅读记录ID", required = true),
                    @Parameter(name = "readTime", description = "int 阅读时间，默认60，单位是秒", required = true),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "成功"),
            }
    )
    @GetMapping("/updateReadTime/{readId}/{readTime}")
    @Log(title = "更新用户阅读时间", businessType = BusinessType.INSERT, operatorType = OperatorType.READER)
    public AjaxResult updateReadTime(@PathVariable Long readId, @PathVariable Integer readTime) {
        return dtbUserBookReadService.updateUserReadTime(readId, readTime);
    }


    /**
     * 更新后修改修正后的最后阅读章节id
     * @param bookId
     * @return
     */
    @PutMapping("/updateBookVersion")
    public AjaxResult updateBookVersion(@RequestBody DtbUserBookConfig dtbUserBookConfig) {
        return dtbUserBookConfigService.updateBookVersion(dtbUserBookConfig);
    }


    // AI部分

    /**
     * 获取朗读角色
     *
     * @param dutpAiVoice
     * @return
     */
    @GetMapping("/getReadRole")
    public AjaxResult getReadRole(DutpAiVoice dutpAiVoice) {
        return AjaxResult.success(dutpAiVoiceService.selectDutpAiVoiceList(dutpAiVoice));
    }

    /**
     * 获取语种
     *
     * @param dutpAiTranslationLanguage
     * @return
     */
    @GetMapping("/getLanguage")
    public AjaxResult getLanguage(DutpAiTranslationLanguage dutpAiTranslationLanguage) {
        return AjaxResult.success(dutpAiTranslationLanguageService.selectDutpAiTranslationLanguageList(dutpAiTranslationLanguage));
    }


    // 简版阅读器接口 编辑端预览调用

    @Tag(name = "获取章节 -- 简版", description = "用户阅读获取章节")
    @GetMapping("/getChaptersSimple/{bookId}/{fromType}")
    public AjaxResult getChaptersSimple(@PathVariable("bookId") Long bookId,@PathVariable("fromType") Integer fromType) {
        return iDtbBookChapterService.getBookChaptersSimple(bookId,fromType);
    }

    @Tag(name = "获取章节内容 -- 简版", description = "获取章节内容，章节id需加密")
    @GetMapping("/getChapterContentSimple")
    public AjaxResult getChapterContentSimple(@RequestParam String id) {
        Long chapterId;
        try {
//            chapterId = Long.parseLong(rsaUtils.decrypt(id));
            chapterId = Long.parseLong(id);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(HttpStatus.NO_CHAPTER, "无效的章节");
        }
        return dtbBookChapterContentService.getChapterContentSimpleByChapterId(chapterId);
    }
}
