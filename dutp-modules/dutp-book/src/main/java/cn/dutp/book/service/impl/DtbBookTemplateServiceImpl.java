package cn.dutp.book.service.impl;

import cn.dutp.book.domain.DtbBookChapter;
import cn.dutp.book.domain.DtbBookTemplate;
import cn.dutp.book.domain.vo.DtbBookTemplateVO;
import cn.dutp.book.mapper.DtbBookChapterMapper;
import cn.dutp.book.mapper.DtbBookCommonMapper;
import cn.dutp.book.mapper.DtbBookMapper;
import cn.dutp.book.mapper.DtbBookTemplateMapper;
import cn.dutp.book.service.IDtbBookTemplateService;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.PageUtils;
import cn.dutp.domain.DtbBook;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DUTP-DTB-029教材模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Service
public class DtbBookTemplateServiceImpl extends ServiceImpl<DtbBookTemplateMapper, DtbBookTemplate> implements IDtbBookTemplateService {
    @Autowired
    private DtbBookTemplateMapper dtbBookTemplateMapper;
    @Autowired
    private DtbBookChapterMapper chapterMapper;
    @Autowired
    private DtbBookMapper bookMapper;
    @Autowired
    private DtbBookCommonMapper commonMapper;

    /**
     * 查询DUTP-DTB-029教材模板
     *
     * @param templateId DUTP-DTB-029教材模板主键
     * @return DUTP-DTB-029教材模板
     */
    @Override
    public DtbBookTemplate selectDtbBookTemplateByTemplateId(Long templateId) {
        return this.getById(templateId);
    }

    @Override
    public DtbBookTemplateVO selectDtbBookTemplateByChapterId(Long chapterId) {
        return dtbBookTemplateMapper.selectDtbBookTemplateByChapterId(chapterId);
    }

    /**
     * 查询DUTP-DTB-029教材模板列表
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return DUTP-DTB-029教材模板
     */
    @Override
    public List<DtbBookTemplate> selectDtbBookTemplateList(DtbBookTemplate dtbBookTemplate) {
        LambdaQueryWrapper<DtbBookTemplate> dtbBookTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dtbBookTemplate.getModal())) {
            dtbBookTemplateLambdaQueryWrapper.like(DtbBookTemplate::getModal, dtbBookTemplate.getModal());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplate.getSerialNumber())) {
            dtbBookTemplateLambdaQueryWrapper.like(DtbBookTemplate::getSerialNumber, dtbBookTemplate.getSerialNumber());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplate.getType())) {
            dtbBookTemplateLambdaQueryWrapper.eq(DtbBookTemplate::getType, dtbBookTemplate.getType());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplate.getThemeColor())) {
            dtbBookTemplateLambdaQueryWrapper.eq(DtbBookTemplate::getThemeColor, dtbBookTemplate.getThemeColor());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplate.getIsDefault())) {
            dtbBookTemplateLambdaQueryWrapper.eq(DtbBookTemplate::getIsDefault, dtbBookTemplate.getIsDefault());
        }
        if (ObjectUtil.isNotEmpty(dtbBookTemplate.getSortBy())) {
            if (1 == dtbBookTemplate.getSortBy()) {
                dtbBookTemplateLambdaQueryWrapper.orderByDesc(DtbBookTemplate::getCreateTime);
            }
            if (2 == dtbBookTemplate.getSortBy()) {
                dtbBookTemplateLambdaQueryWrapper.orderByAsc(DtbBookTemplate::getCreateTime);
            }
            if (3 == dtbBookTemplate.getSortBy()) {
                dtbBookTemplateLambdaQueryWrapper.orderByDesc(DtbBookTemplate::getUpdateTime);
            }
            if (4 == dtbBookTemplate.getSortBy()) {
                dtbBookTemplateLambdaQueryWrapper.orderByAsc(DtbBookTemplate::getModal);
            }
        } else {
            dtbBookTemplateLambdaQueryWrapper.orderByAsc(DtbBookTemplate::getSort);
            dtbBookTemplateLambdaQueryWrapper.orderByDesc(DtbBookTemplate::getCreateTime);
        }
        List<DtbBookTemplate> list = this.list(dtbBookTemplateLambdaQueryWrapper);
        for (DtbBookTemplate bookTemplate : list) {
            if (ObjectUtil.isNotEmpty(bookTemplate.getUserId())) {
                String nickName = commonMapper.queryNickNameByUserId(bookTemplate.getUserId());
                bookTemplate.setNickName(nickName);
            }
        }
        return list;
    }

    /**
     * 新增DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    @Override
    public boolean insertDtbBookTemplate(DtbBookTemplate dtbBookTemplate) {
        dtbBookTemplate.setSort(1);
        // 获取最新编号
        String maxSerialNumber = dtbBookTemplateMapper.queryMaxNum();
        if (ObjectUtil.isEmpty(maxSerialNumber)) {
            dtbBookTemplate.setSerialNumber("001");
        } else {
            dtbBookTemplate.setSerialNumber(incrementString(maxSerialNumber));
        }

        return this.save(dtbBookTemplate);
    }

    /**
     * 获取下一个编号
     *
     * @param numStr
     * @return
     */
    public static String incrementString(String numStr) {
        long num = Long.parseLong(numStr);
        num++;

        String incrementedStr = String.valueOf(num);
        while (incrementedStr.length() < 3) {
            incrementedStr = "0" + incrementedStr;
        }

        return incrementedStr;
    }

    /**
     * 修改DUTP-DTB-029教材模板
     *
     * @param dtbBookTemplate DUTP-DTB-029教材模板
     * @return 结果
     */
    @Override
    public boolean updateDtbBookTemplate(DtbBookTemplate dtbBookTemplate) {
        return this.updateById(dtbBookTemplate);
    }


    /**
     * 批量删除DUTP-DTB-029教材模板
     *
     * @param templateId 需要删除的DUTP-DTB-029教材模板主键
     * @return 结果
     */
    @Override
    public boolean deleteDtbBookTemplateByTemplateId(Long templateId) {
        DtbBookTemplate bookTemplate = this.getOne(new LambdaQueryWrapper<DtbBookTemplate>()
                .select(DtbBookTemplate::getIsDefault)
                .eq(DtbBookTemplate::getTemplateId, templateId));
        if (ObjectUtil.isEmpty(bookTemplate)) {
            throw new ServiceException("模板不存在");
        }
        if (bookTemplate.getIsDefault() == 1) {
            throw new ServiceException("默认模板不能删除");
        }
        return this.removeById(templateId);
    }

    @Override
    public List<DtbBookTemplate> templateListByBookId(DtbBookTemplate bookTemplate) {
        Long templateId = chapterMapper.queryTemplateIdByBookId(bookTemplate.getBookId());
        PageUtils.startPage();
        bookTemplate.setTemplateId(templateId);
        List<DtbBookTemplate> list = dtbBookTemplateMapper.selectDtbBookTemplateList(bookTemplate);

        if (list != null && templateId != null) {
            for (DtbBookTemplate dtbBookTemplate : list) {
                if (templateId.equals(dtbBookTemplate.getTemplateId())) {
                    dtbBookTemplate.setIsUse(true);
                } else {
                    dtbBookTemplate.setIsUse(false);
                }
            }
        }
        return list;
    }

    @Override
    public int updateBookTemplate(DtbBookTemplate dtbBookTemplate) {
        if (dtbBookTemplate.getBookId() == null) {
            throw new ServiceException("bookId不能为空");
        }
        DtbBook book = bookMapper.selectOne(new LambdaQueryWrapper<DtbBook>()
                .select(DtbBook::getCurrentVersionId)
                .eq(DtbBook::getBookId, dtbBookTemplate.getBookId()));
        if (book == null) {
            throw new ServiceException("bookId不存在");
        }
        DtbBookChapter dtbBookChapter = new DtbBookChapter();
        dtbBookChapter.setVersionId(book.getCurrentVersionId());
        dtbBookChapter.setTemplateId(dtbBookTemplate.getTemplateId());
        chapterMapper.updateChapterTemplate(dtbBookChapter);
        return 1;
    }

    @Override
    public int editDefault(DtbBookTemplate dtbBookTemplate) {
        dtbBookTemplateMapper.cancelDefault();
        return dtbBookTemplateMapper.setDefault(dtbBookTemplate);
    }

}
