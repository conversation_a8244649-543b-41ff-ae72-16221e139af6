<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.im.mapper.DutpCustomerMapper">
    
    <resultMap type="DutpCustomer" id="DutpCustomerResult">
        <result property="repId"    column="rep_id"    />
        <result property="userId"    column="user_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDutpCustomerVo">
        select rep_id, user_id, nick_name, status, remark, del_flag, create_by, create_time, update_by, update_time from dutp_customer
    </sql>

    <select id="selectDutpCustomerList" parameterType="DutpCustomer" resultMap="DutpCustomerResult">
        <include refid="selectDutpCustomerVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectDutpCustomerByRepId" parameterType="Long" resultMap="DutpCustomerResult">
        <include refid="selectDutpCustomerVo"/>
        where rep_id = #{repId}
    </select>

    <insert id="insertDutpCustomer" parameterType="DutpCustomer" useGeneratedKeys="true" keyProperty="repId">
        insert into dutp_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDutpCustomer" parameterType="DutpCustomer">
        update dutp_customer
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rep_id = #{repId}
    </update>

    <delete id="deleteDutpCustomerByRepId" parameterType="Long">
        delete from dutp_customer where rep_id = #{repId}
    </delete>

    <delete id="deleteDutpCustomerByRepIds" parameterType="String">
        delete from dutp_customer where rep_id in 
        <foreach item="repId" collection="array" open="(" separator="," close=")">
            #{repId}
        </foreach>
    </delete>

    <select id="selectUserList" parameterType="cn.dutp.system.api.domain.SysUser" resultType="cn.dutp.system.api.domain.SysUser">
        SELECT
            u.user_id,
            u.dept_id,
            u.user_name,
            u.nick_name,
            u.email,
            u.avatar,
            u.phonenumber,
            u.STATUS,
            u.del_flag,
            r.role_name as remark
        FROM
            sys_user u
        left join
            sys_user_role s on s.user_id = u.user_id
        left join
            sys_role r on r.role_id = s.role_id
        <where>
            u.status = 0 and u.del_flag = 0 and u.user_id != 1
            and u.user_id not in(select user_id from dutp_customer where del_flag = 0)
            <if test="nickName != null">
                and (u.nick_name like concat('%', #{nickName}, '%') or u.user_name  like concat('%', #{nickName}, '%') or u.phonenumber like concat('%', #{nickName}, '%') )</if>
        </where>
    </select>

    <update id="updateDutpCustomerStatus" parameterType="DutpCustomer">
        update
            dutp_customer
        set
            status = #{status}
        where
            del_flag = 0 and user_id = #{userId}
    </update>
    <select id="selectDutpCustomerAllList" parameterType="DutpCustomer" resultType="DutpCustomer">
        <![CDATA[
            select
                dc.rep_id,
                dc.user_id,
                dc.nick_name,
                dc.status,
                dc.remark,
                dc.del_flag,
                dc.create_by,
                dc.create_time,
                dc.update_by,
                dc.update_time,
                dc.seat_name,
                su.avatar,
                (
                    select
                        count(0)
                    from
                        dutp_customer_chat_detail dd
                    where
                        dd.user_type = 3 and (dd.chat_content = '会话关闭') and dd.user_id = dc.user_id
                        and (dd.create_time >= CURDATE() AND dd.create_time < CURDATE() + INTERVAL 1 DAY)
                ) as talkedCount,
                (
                    select
                        count(0)
                    from
                        dutp_customer_chat_detail de
                    where
                        de.user_type = 3 and de.chat_content = '正在会话' and de.user_id = dc.user_id
                        and (de.create_time >= CURDATE() AND de.create_time < CURDATE() + INTERVAL 1 DAY)
                ) as talkingCount
            from
                dutp_customer dc
            left join
                sys_user su on su.user_id = dc.user_id and su.del_flag = 0
                ]]>
            <where>
                dc.del_flag = 0
                 <if test="status != null "> and dc.status = #{status}</if>
            </where>

    </select>
</mapper>