package cn.dutp.im.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.im.domain.DutpCustomerQuickReply;
import cn.dutp.im.service.IDutpCustomerQuickReplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * DUTP-BASE-024客服快捷回复Controller
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@RestController
@RequestMapping("/reply")
public class DutpCustomerQuickReplyController extends BaseController
{
    @Autowired
    private IDutpCustomerQuickReplyService dutpCustomerQuickReplyService;

    /**
     * 查询DUTP-BASE-024客服机器人快捷回复列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        startPage();
        List<DutpCustomerQuickReply> list = dutpCustomerQuickReplyService.selectDutpCustomerQuickReplyList(dutpCustomerQuickReply);
        return getDataTable(list);
    }

    /**
     * 查询DUTP-BASE-024客服人员快捷回复列表
     */
    @GetMapping("/getListByUserId")
    public AjaxResult getListByUserId()
    {
        List<DutpCustomerQuickReply> list = dutpCustomerQuickReplyService.getListByUserId();
        return success(list);
    }

    /**
     * 导出DUTP-BASE-024客服快捷回复列表
     */
    @RequiresPermissions("im:reply:export")
    @Log(title = "客服快捷回复", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        List<DutpCustomerQuickReply> list = dutpCustomerQuickReplyService.selectDutpCustomerQuickReplyList(dutpCustomerQuickReply);
        ExcelUtil<DutpCustomerQuickReply> util = new ExcelUtil<DutpCustomerQuickReply>(DutpCustomerQuickReply.class);
        util.exportExcel(response, list, "DUTP-BASE-024客服快捷回复数据");
    }

    /**
     * 获取DUTP-BASE-024客服快捷回复详细信息
     */
    @RequiresPermissions("im:reply:query")
    @GetMapping(value = "/{replyId}")
    public AjaxResult getInfo(@PathVariable("replyId") Long replyId)
    {
        return success(dutpCustomerQuickReplyService.selectDutpCustomerQuickReplyByReplyId(replyId));
    }

    /**
     * 获取DUTP-BASE-024客服快捷回复详细信息
     */
    @RequiresPermissions("im:reply:query")
    @GetMapping(value = "/getInfoByWord")
    public AjaxResult getInfoByWord(@RequestParam String word)
    {
        return success(dutpCustomerQuickReplyService.getInfoByWord(word));
    }

    /**
     * 新增DUTP-BASE-024客服快捷回复
     */
    @RequiresPermissions("im:reply:add")
    @Log(title = "新增客服快捷回复", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        if(dutpCustomerQuickReply.getIsCustomer() != 0) {
            dutpCustomerQuickReply.setUserId(SecurityUtils.getUserId());
        } else {
            dutpCustomerQuickReply.setUserId(0L);
        }
        return toAjax(dutpCustomerQuickReplyService.insertDutpCustomerQuickReply(dutpCustomerQuickReply));
    }

    /**
     * 修改DUTP-BASE-024客服快捷回复
     */
    @RequiresPermissions("im:reply:edit")
    @Log(title = "修改客服快捷回复", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        return toAjax(dutpCustomerQuickReplyService.updateDutpCustomerQuickReply(dutpCustomerQuickReply));
    }

    /**
     * 快捷回复启用/停用
     */
    @RequiresPermissions("im:reply:edit")
    @Log(title = "修改客服快捷回复", businessType = BusinessType.UPDATE)
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody DutpCustomerQuickReply dutpCustomerQuickReply)
    {
        return toAjax(dutpCustomerQuickReplyService.editStatus(dutpCustomerQuickReply));
    }

    /**
     * 删除DUTP-BASE-024客服快捷回复
     */
    @RequiresPermissions("im:reply:remove")
    @Log(title = "删除客服快捷回复", businessType = BusinessType.DELETE)
    @DeleteMapping("/{replyIds}")
    public AjaxResult remove(@PathVariable Long[] replyIds)
    {
        return toAjax(dutpCustomerQuickReplyService.deleteDutpCustomerQuickReplyByReplyIds(Arrays.asList(replyIds)));
    }
}
