package cn.dutp.im.service.impl;

import cn.dutp.api.common.constant.NotificationConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpUserWorkOrder;
import cn.dutp.im.mapper.DutpUserWorkOrderMapper;
import cn.dutp.im.service.IDutpUserWorkOrderService;
import cn.dutp.message.api.RemoteUserMessageService;
import cn.dutp.message.api.domain.DutpUserMessage;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sun.org.apache.bcel.internal.generic.NEW;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.management.Notification;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 反馈工单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class DutpUserWorkOrderServiceImpl extends ServiceImpl<DutpUserWorkOrderMapper, DutpUserWorkOrder> implements IDutpUserWorkOrderService {
    @Autowired
    private DutpUserWorkOrderMapper dutpUserWorkOrderMapper;

    @Autowired
    private RemoteUserMessageService remoteUserMessageService;

    /**
     * 查询反馈工单
     *
     * @param ticketId 反馈工单主键
     * @return 反馈工单
     */
    @Override
    public DutpUserWorkOrder selectDutpUserWorkOrderByTicketId(Long ticketId) {
        return this.getById(ticketId);
    }

    /**
     * 查询反馈工单列表
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 反馈工单
     */
    @Override
    public List<DutpUserWorkOrder> selectDutpUserWorkOrderList(DutpUserWorkOrder dutpUserWorkOrder) {
        LambdaQueryWrapper<DutpUserWorkOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(dutpUserWorkOrder.getUserId())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getUserId
                    , dutpUserWorkOrder.getUserId());
        }
        if (ObjectUtil.isNotEmpty(dutpUserWorkOrder.getTitle())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getTitle
                    , dutpUserWorkOrder.getTitle());
        }
        if (ObjectUtil.isNotEmpty(dutpUserWorkOrder.getDescription())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getDescription
                    , dutpUserWorkOrder.getDescription());
        }
        if (ObjectUtil.isNotEmpty(dutpUserWorkOrder.getStatus())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getStatus
                    , dutpUserWorkOrder.getStatus());
        }
        if (ObjectUtil.isNotEmpty(dutpUserWorkOrder.getPriority())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getPriority
                    , dutpUserWorkOrder.getPriority());
        }
        if (ObjectUtil.isNotEmpty(dutpUserWorkOrder.getFeedbackType())) {
            lambdaQueryWrapper.eq(DutpUserWorkOrder::getFeedbackType
                    , dutpUserWorkOrder.getFeedbackType());
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增反馈工单
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 结果
     */
    @Override
    public boolean insertDutpUserWorkOrder(DutpUserWorkOrder dutpUserWorkOrder) {
        return this.save(dutpUserWorkOrder);
    }

    /**
     * 修改反馈工单
     *
     * @param dutpUserWorkOrder 反馈工单
     * @return 结果
     */
    @Override
    public Integer updateDutpUserWorkOrder(DutpUserWorkOrder dutpUserWorkOrder) {
        UpdateWrapper<DutpUserWorkOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DutpUserWorkOrder::getTicketId, dutpUserWorkOrder.getTicketId())
                .set(DutpUserWorkOrder::getUpdateBy, SecurityUtils.getUsername())
                .set(DutpUserWorkOrder::getUpdateTime, new Date())
                .set(DutpUserWorkOrder::getStatus, dutpUserWorkOrder.getStatus())
                .set(DutpUserWorkOrder::getProcessTime,new Date())
                .set(DutpUserWorkOrder::getProcessUserId,SecurityUtils.getUserId())
                .set(DutpUserWorkOrder::getProcessDescription, dutpUserWorkOrder.getProcessDescription());
        Integer res = dutpUserWorkOrderMapper.update(null,updateWrapper);
        // 给相关人员发送消息
        String title = "意见反馈提醒";
        String content = String.format(NotificationConstants.FEEDBACK, dutpUserWorkOrder.getTicketId());
        sendMessage(title,content, dutpUserWorkOrder.getUserId(),dutpUserWorkOrder.getTicketId());
        return res;
    }

    public void sendMessage(String title,String content,Long toUserId,Long ticketId) {
        boolean isResult = false;
        DutpUserMessage dutpUserMessage = new DutpUserMessage();
        dutpUserMessage.setContent(content);
        dutpUserMessage.setTitle(title);
        // 发送者
        dutpUserMessage.setFromUserId(SecurityUtils.getUserId());
        // 接收者
        dutpUserMessage.setToUserId(toUserId);
        // 1系统消息2教务消息3推送消息
        dutpUserMessage.setMessageType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setFromUserType(1);
        // 用户类型1后台用户2前台用户
        dutpUserMessage.setToUserType(2);
        dutpUserMessage.setBusinessId(ticketId);
        R<Boolean> booleanR = remoteUserMessageService.addMessage(dutpUserMessage);
        if (booleanR.getCode() == 500) {
            isResult = false;
            throw new ServiceException("Message模块未启动，无法发送消息！");
        }
    }

    /**
     * 批量删除反馈工单
     *
     * @param ticketIds 需要删除的反馈工单主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpUserWorkOrderByTicketIds(List<Long> ticketIds) {
        return this.removeByIds(ticketIds);
    }

    @Override
    public List<DutpUserWorkOrder> getCustomerlist(DutpUserWorkOrder dutpUserWorkOrder) {
        return dutpUserWorkOrderMapper.getCustomerlist(dutpUserWorkOrder);
    }

    @Override
    public DutpUserWorkOrder getCustomerInfo(Long ticketId) {
        return dutpUserWorkOrderMapper.getCustomerInfo(ticketId);
    }

}
