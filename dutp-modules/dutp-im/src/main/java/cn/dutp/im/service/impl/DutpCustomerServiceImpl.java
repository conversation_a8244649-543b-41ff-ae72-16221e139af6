package cn.dutp.im.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.domain.DutpCustomerChatDetail;
import cn.dutp.im.domain.DutpCustomer;
import cn.dutp.im.domain.DutpCustomerChat;
import cn.dutp.im.mapper.DutpCustomerChatMapper;
import cn.dutp.im.mapper.DutpCustomerMapper;
import cn.dutp.im.service.IDutpCustomerService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static cn.dutp.common.core.utils.PageUtils.startPage;

/**
 * DUTP-BASE-010客服Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Service
public class DutpCustomerServiceImpl extends ServiceImpl<DutpCustomerMapper, DutpCustomer> implements IDutpCustomerService
{
    @Autowired
    private DutpCustomerMapper dutpCustomerMapper;

    @Autowired
    private DutpCustomerChatMapper dutpCustomerChatMapper;

    /**
     * 查询DUTP-BASE-010客服
     *
     * @param repId DUTP-BASE-010客服主键
     * @return DUTP-BASE-010客服
     */
    @Override
    public DutpCustomer selectDutpCustomerByRepId(Long repId)
    {
        return this.getById(repId);
    }

    /**
     * 查询DUTP-BASE-010客服列表
     *
     * @param dutpCustomer DUTP-BASE-010客服
     * @return DUTP-BASE-010客服
     */
    @Override
    public Map<String,Object> selectDutpCustomerList(DutpCustomer dutpCustomer)
    {
//        startPage();
        Map<String,Object> resultMap = new HashMap<String,Object>();
        List<DutpCustomer> list = dutpCustomerMapper.selectDutpCustomerAllList(dutpCustomer);
        for (DutpCustomer dutp : list) {
            // 正在会话，扣除已结束服务次数
            dutp.setTalkingCount(dutp.getTalkingCount() - dutp.getTalkedCount());
        }
        resultMap.put("rows", list);
        QueryWrapper<DutpCustomerChatDetail> queryTodayWrapper = new QueryWrapper<>();
        queryTodayWrapper.lambda().eq(DutpCustomerChatDetail::getUserType,3);
        Integer onloadCount = list.stream().filter(e -> e.getStatus() == 1).collect(Collectors.toList()).size();
        resultMap.put("onloadCount", onloadCount);
        resultMap.put("unOnloadCount", list.size() - onloadCount);
        Integer talkingCount = list.stream().mapToInt(DutpCustomer::getTalkingCount).sum();
        /*正在会话*/
        resultMap.put("talkingCount", talkingCount);
        /*会话关闭*/
        Integer talkedCount = list.stream().mapToInt(DutpCustomer::getTalkedCount).sum();
        resultMap.put("talkedCount", talkedCount);

        QueryWrapper<DutpCustomerChat> chatQueryWrapper = new QueryWrapper<>();
        chatQueryWrapper.lambda().eq(DutpCustomerChat::getChatStatus,1)
                .eq(DutpCustomerChat::getDelFlag,0);
        List<DutpCustomerChat> chatList = dutpCustomerChatMapper.selectList(chatQueryWrapper);
        Integer tobeOpened = chatList.size();
        /*待开启会话*/
        resultMap.put("tobeOpened", tobeOpened);
        return resultMap;
    }

    /**
     * 新增DUTP-BASE-010客服
     *
     * @param dutpCustomer DUTP-BASE-010客服
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertDutpCustomer(DutpCustomer dutpCustomer)
    {
        Integer max = dutpCustomerMapper.getMaxSeat();
        dutpCustomer.setStatus(2);
        dutpCustomer.setSeatName("席位" + (max + 1));
        return this.save(dutpCustomer);
    }

    /**
     * 修改DUTP-BASE-010客服
     *
     * @param dutpCustomer DUTP-BASE-010客服
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateDutpCustomer(DutpCustomer dutpCustomer)
    {
        return this.updateById(dutpCustomer);
    }

    /**
     * 批量删除DUTP-BASE-010客服
     *
     * @param repIds 需要删除的DUTP-BASE-010客服主键
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteDutpCustomerByRepIds(List<Long> repIds)
    {
        return this.removeByIds(repIds);
    }

    @Override
    public List<SysUser> selectUserList(SysUser sysUser) {
        return dutpCustomerMapper.selectUserList(sysUser);
    }

    @Override
    @Transactional
    public int updateDutpCustomerStatus(DutpCustomer dutpCustomer) {
        QueryWrapper<DutpCustomer> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DutpCustomer::getUserId, SecurityUtils.getUserId())
                .eq(DutpCustomer::getDelFlag,0);
        DutpCustomer customer = dutpCustomerMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(customer)) {
            return dutpCustomerMapper.updateDutpCustomerStatus(dutpCustomer);
        }
        return 0;
    }

}
