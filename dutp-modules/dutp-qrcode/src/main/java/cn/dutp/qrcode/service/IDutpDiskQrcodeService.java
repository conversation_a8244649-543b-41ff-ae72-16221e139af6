package cn.dutp.qrcode.service;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import cn.dutp.qrcode.domain.DutpDiskQrcode;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 智典云盘资源Service接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface IDutpDiskQrcodeService extends IService<DutpDiskQrcode>
{
    /**
     * 查询智典云盘资源
     *
     * @param qrcodeId 智典云盘资源主键
     * @return 智典云盘资源
     */
    public DutpDiskQrcode selectDutpDiskQrcodeByQrcodeId(Long qrcodeId);

    /**
     * 查询智典云盘资源列表
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 智典云盘资源集合
     */
    public List<DutpDiskQrcode> selectDutpDiskQrcodeList(DutpDiskQrcode dutpDiskQrcode);

    /**
     * 新增智典云盘资源
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 结果
     */
    public DutpDiskQrcode  insertDutpDiskQrcode(DutpDiskQrcode dutpDiskQrcode);

    /**
     * 修改智典云盘资源
     *
     * @param dutpDiskQrcode 智典云盘资源
     * @return 结果
     */
    public boolean updateDutpDiskQrcode(DutpDiskQrcode dutpDiskQrcode);

    /**
     * 批量删除智典云盘资源
     *
     * @param qrcodeIds 需要删除的智典云盘资源主键集合
     * @return 结果
     */
    public boolean deleteDutpDiskQrcodeByQrcodeIds(List<Long> qrcodeIds);

    /**
     * 导入智典云盘资源信息
     *
     * @param  qrcodeList 智典云盘资源
     * @return 结果
     */
    String importQrcode(List<DutpDiskQrcode> qrcodeList, String operName);

    /**
     * 拉取二维码图片列表
     * @param qrcodeIds 具体的二维码id
     * @param bookIds  或某本书的id
     */
    List<DutpDiskQrcode> selectDutpDiskQrcodeListForDownload(List<Long> qrcodeIds,List<Long> bookIds);


    /**
     * 拉取二维码中mongo里面的资源列表
     * @param qrcodeIds 具体的二维码id
     * @param bookIds  或某本书的id
     */
    List<DutpDiskQrcode> selectDutpDiskQrcodeMongoForDownload(List<Long> qrcodeIds, List<Long> bookIds);

    /**
     * 查询智典云盘资源列表(检索窗口使用)
     */
     List<DutpDiskQrcode> selectDutpDiskQrcodeSearchList(DutpDiskQrcode dutpDiskQrcode);

    ByteArrayOutputStream addWatermarkToFile(MultipartFile file,String watermarkText);

    /**
     * 根据目录ID列表查询二维码数量
     *
     * @param catalogIds 目录ID列表
     * @return Map<目录ID, 数量>
     */
    Map<Long, Long> getQrcodeCountByCatalogIds(List<Long> catalogIds);

    /**
     * 验证二维码是否在允许扫描的时间范围内
     *
     * @param qrcode 二维码对象
     * @return true-允许扫描，false-不允许扫描
     */
    boolean isQrcodeScanTimeValid(DutpDiskQrcode qrcode);

}
