package cn.dutp.qrcode.mapper;

import cn.dutp.qrcode.domain.DutpDiskUserTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 用户的二维码模板Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Repository
public interface DutpDiskUserTemplateMapper extends BaseMapper<DutpDiskUserTemplate>
{
    @Select("SELECT count(*) from dutp_disk_user_template where template_name = #{templateName} and del_flag = 0 and user_id = #{userId}")
    int selectUserTemplateName(@Param("templateName") String templateName, @Param("userId")Long userId);

    @Update("update dutp_disk_user_template set is_default = 1 where user_template_id = #{userTemplateId}")
    boolean changeDefault(DutpDiskUserTemplate dutpDiskUserTemplate);

    @Update("update dutp_disk_user_template set is_default = 0 where user_template_id = #{userTemplateId}")
    boolean changeDefaultIsZero(DutpDiskUserTemplate dutpDiskUserTemplate);
}
