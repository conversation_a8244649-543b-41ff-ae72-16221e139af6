package cn.dutp.edu.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseClassMemberScore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 互动课堂班级成员各项汇总成绩Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseClassMemberScoreMapper extends BaseMapper<MoocSmartCourseClassMemberScore>
{

    List<MoocSmartCourseClassMemberScore> getListByClassId(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore);
}
