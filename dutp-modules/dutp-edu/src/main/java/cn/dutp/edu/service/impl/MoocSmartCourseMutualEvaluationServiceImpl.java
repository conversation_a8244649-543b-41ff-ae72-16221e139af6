package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseMutualEvaluationMapper;
import cn.dutp.edu.domain.MoocSmartCourseMutualEvaluation;
import cn.dutp.edu.service.IMoocSmartCourseMutualEvaluationService;

/**
 * 互动课堂互评Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class MoocSmartCourseMutualEvaluationServiceImpl extends ServiceImpl<MoocSmartCourseMutualEvaluationMapper, MoocSmartCourseMutualEvaluation> implements IMoocSmartCourseMutualEvaluationService
{
    @Autowired
    private MoocSmartCourseMutualEvaluationMapper moocSmartCourseMutualEvaluationMapper;

    /**
     * 查询互动课堂互评
     *
     * @param mutualEvaluationId 互动课堂互评主键
     * @return 互动课堂互评
     */
    @Override
    public MoocSmartCourseMutualEvaluation selectMoocSmartCourseMutualEvaluationByMutualEvaluationId(Long mutualEvaluationId)
    {
        return this.getById(mutualEvaluationId);
    }

    /**
     * 查询互动课堂互评列表
     *
     * @param moocSmartCourseMutualEvaluation 互动课堂互评
     * @return 互动课堂互评
     */
    @Override
    public List<MoocSmartCourseMutualEvaluation> selectMoocSmartCourseMutualEvaluationList(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        LambdaQueryWrapper<MoocSmartCourseMutualEvaluation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getAssignmentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getAssignmentId
                ,moocSmartCourseMutualEvaluation.getAssignmentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getTestPaperId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getTestPaperId
                ,moocSmartCourseMutualEvaluation.getTestPaperId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getCourseId
                ,moocSmartCourseMutualEvaluation.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getClassId
                ,moocSmartCourseMutualEvaluation.getClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getGradingMethod())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getGradingMethod
                ,moocSmartCourseMutualEvaluation.getGradingMethod());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getFromEvaluationId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getFromEvaluationId
                ,moocSmartCourseMutualEvaluation.getFromEvaluationId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getToEvaluationId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getToEvaluationId
                ,moocSmartCourseMutualEvaluation.getToEvaluationId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMutualEvaluation.getScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMutualEvaluation::getScore
                ,moocSmartCourseMutualEvaluation.getScore());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂互评
     *
     * @param moocSmartCourseMutualEvaluation 互动课堂互评
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseMutualEvaluation(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        return this.save(moocSmartCourseMutualEvaluation);
    }

    /**
     * 修改互动课堂互评
     *
     * @param moocSmartCourseMutualEvaluation 互动课堂互评
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseMutualEvaluation(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        return this.updateById(moocSmartCourseMutualEvaluation);
    }

    /**
     * 批量删除互动课堂互评
     *
     * @param mutualEvaluationIds 需要删除的互动课堂互评主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseMutualEvaluationByMutualEvaluationIds(List<Long> mutualEvaluationIds)
    {
        return this.removeByIds(mutualEvaluationIds);
    }

}
