package cn.dutp.edu.domain.vo;

import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import cn.dutp.edu.domain.MoocSmartCourseSurveyOption;
import cn.dutp.edu.domain.MoocSmartCourseSurveyQuestion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 问卷问题详情，包含选项和用户回答
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "问卷问题详情，包含选项和用户回答")
public class SurveyQuestionDetailVo extends MoocSmartCourseSurveyQuestion {

    @Schema(description = "问题对应的选项列表")
    private List<MoocSmartCourseSurveyOption> options;

    @Schema(description = "当前用户的回答")
    private MoocSmartCourseSurveyAnswerItem userAnswer;
} 