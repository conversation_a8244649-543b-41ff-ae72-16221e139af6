<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.DtbBookMapper">

    <resultMap id="BaseResultMap" type="cn.dutp.domain.DtbBook">
        <id property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="authorLabel" column="author_label"/>
        <result property="authorValue" column="author_value"/>
        <result property="cover" column="cover"/>
        <result property="topSubjectId" column="top_subject_id"/>
        <result property="secondSubjectId" column="second_subject_id"/>
        <result property="thirdSubjectId" column="third_subject_id"/>
        <result property="forthSubjectId" column="forth_subject_id"/>
        <result property="isbn" column="isbn"/>
        <result property="issn" column="issn"/>
        <result property="bookNo" column="book_no"/>
        <result property="publishDate" column="publish_date"/>
        <result property="currentVersionId" column="current_version_id"/>
        <result property="lastVersionId" column="last_version_id"/>
        <result property="shelfTime" column="shelf_time"/>
        <result property="unshelfTime" column="unshelf_time"/>
        <result property="publishOrganization" column="publish_organization"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="shelfState" column="shelf_state"/>
        <result property="schoolId" column="school_id"/>
        <result property="houseId" column="house_id"/>
        <result property="bookType" column="book_type"/>
        <result property="masterFlag" column="master_flag"/>
        <result property="masterBookId" column="master_book_id"/>
        <result property="soldQuantity" column="sold_quantity"/>
        <result property="readQuantity" column="read_quantity"/>
        <result property="priceCounter" column="price_counter"/>
        <result property="priceSale" column="price_sale"/>
        <result property="bookOrganize" column="book_organize"/>
        <result property="topicNo" column="topic_no"/>
        <result property="languageId" column="language_id"/>
        <result property="currentStepId" column="current_step_id"/>
        <result property="edition" column="edition"/>
        <result property="tableNumberType" column="table_number_type"/>
        <result property="imageNumberType" column="image_number_type"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="bookResultVo" type="cn.dutp.edu.domain.vo.BookVo">
        <id property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="authorLabel" column="author_label"/>
        <result property="authorValue" column="author_value"/>
        <result property="cover" column="cover"/>
        <result property="topSubjectId" column="top_subject_id"/>
        <result property="secondSubjectId" column="second_subject_id"/>
        <result property="thirdSubjectId" column="third_subject_id"/>
        <result property="forthSubjectId" column="forth_subject_id"/>
        <result property="isbn" column="isbn"/>
        <result property="issn" column="issn"/>
        <result property="bookNo" column="book_no"/>
        <result property="publishDate" column="publish_date"/>
        <result property="currentVersionId" column="current_version_id"/>
        <result property="lastVersionId" column="last_version_id"/>
        <result property="shelfTime" column="shelf_time"/>
        <result property="unshelfTime" column="unshelf_time"/>
        <result property="publishOrganization" column="publish_organization"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="shelfState" column="shelf_state"/>
        <result property="schoolId" column="school_id"/>
        <result property="houseId" column="house_id"/>
        <result property="bookType" column="book_type"/>
        <result property="masterFlag" column="master_flag"/>
        <result property="masterBookId" column="master_book_id"/>
        <result property="soldQuantity" column="sold_quantity"/>
        <result property="readQuantity" column="read_quantity"/>
        <result property="priceCounter" column="price_counter"/>
        <result property="priceSale" column="price_sale"/>
        <result property="bookOrganize" column="book_organize"/>
        <result property="topicNo" column="topic_no"/>
        <result property="languageId" column="language_id"/>
        <result property="currentStepId" column="current_step_id"/>
        <result property="edition" column="edition"/>
        <result property="tableNumberType" column="table_number_type"/>
        <result property="imageNumberType" column="image_number_type"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="editor" column="editor"/>
        <result property="editorKey" column="editorKey"/>
        <result property="teacherNum" column="teacherNum"/>
        <result property="studentNum" column="studentNum"/>
        <result property="childCount" column="childCount"/>
        <collection property="groupList" ofType="cn.dutp.domain.DtbBookGroup">
            <result property="groupId"    column="group_id"    />
            <result property="roleType"    column="role_type"    />
        </collection>
        <collection property="dtbUserBookList" ofType="cn.dutp.book.domain.DtbUserBook">
            <result property="userBookId"    column="user_book_id"    />
            <result property="userId"    column="user_id"    />
            <result property="bookId"    column="book_id"    />
            <result property="versionId"    column="version_id"    />
            <result property="bookTypeId"    column="book_type_id"    />
            <result property="addWay"    column="add_way"    />
            <result property="readRate"    column="read_rate"    />
            <result property="expireDate"    column="expire_date"    />
            <result property="sort"    column="sort"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="delFlag"    column="del_flag"    />
        </collection>
    </resultMap>

    <resultMap id="bookResultVoForSchool" type="cn.dutp.edu.domain.vo.BookVo">
        <id property="bookId" column="book_id"/>
        <result property="bookName" column="book_name"/>
        <result property="cover" column="cover"/>
        <result property="isbn" column="isbn"/>
        <result property="issn" column="issn"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="shelfState" column="shelf_state"/>
        <result property="bookOrganize" column="book_organize"/>
        <result property="masterFlag" column="master_flag"/>
        <result property="currentStepId" column="current_step_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="stepName" column="step_name"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap id="getOrderResult" type="cn.dutp.edu.domain.vo.DutpEduOrderVo">
        <id property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="createTime" column="create_time"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="orderStatus" column="order_status"/>
        <result property="price" column="price"/>
        <collection property="itemList" column="order_id" ofType="cn.dutp.edu.domain.vo.DutpEduOrderDetailVo">
            <id property="orderItemId" column="order_item_id"/>
            <result property="isbn" column="isbn"/>
            <result property="bookId" column="book_id"/>
            <result property="bookName" column="book_name"/>
            <result property="priceSale" column="price_sale"/>
            <result property="priceCounter" column="price_counter"/>
            <result property="priceOrderItem" column="price_order_item"/>
            <result property="bookQuantity" column="book_quantity"/>
            <result property="itemStatus" column="item_status"/>
            <result property="bindingCodeCount" column="bindingCodeCount"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        book_id
        ,book_name,author_label,author_value,cover,top_subject_id,
        second_subject_id,third_subject_id,forth_subject_id,isbn,issn,
        book_no,publish_date,current_version_id,last_version_id,shelf_time,
        unshelf_time,publish_organization,publish_status,shelf_state,school_id,
        house_id,book_type,master_flag,master_book_id,sold_quantity,
        read_quantity,price_counter,price_sale,book_organize,topic_no,
        language_id,current_step_id,edition,table_number_type,image_number_type,
        del_flag,create_by,create_time,update_by,update_time
    </sql>

    <select id="selectOpenBookList" resultMap="bookResultVo" parameterType="cn.dutp.edu.domain.dto.BookDto">
        select
            t.*
        from
            (
                select
                    dtb.book_no,
                    dtb.book_name,
                    dtb.book_id,
                    dtb.cover,
                    IF(dtb.isbn IS NULL OR dtb.isbn = '', dtb.issn, dtb.isbn) AS isbn,
                    dtb.author_label as editorKey,
                    dtb.author_value as editor,
                    (select count(0) from dtb_book where master_book_id = dtb.book_id and master_flag = 3 and del_flag = 0) as childCount
                from
                    dtb_book dtb
                <where>
                    dtb.del_flag = 0 and dtb.master_flag != 3 and dtb.shelf_state = 1 or dtb.shelf_state = 4 and dtb.book_organize = 1
                    <if test="bookName != null and bookName != ''">
                        and (
                        dtb.book_name like concat('%', #{bookName}, '%')
                        or dtb.isbn like concat('%', #{bookName}, '%')
                        or dtb.issn like concat('%', #{bookName}, '%')
                        )
                    </if>
                    <if test="topSubjectId != null">and dtb.top_subject_id = #{topSubjectId}</if>
                    <if test="secondSubjectId != null">and dtb.second_subject_id = #{secondSubjectId}</if>
                    <if test="thirdSubjectId != null">and dtb.third_subject_id = #{thirdSubjectId}</if>
                    <if test="forthSubjectId != null">and dtb.forth_subject_id = #{forthSubjectId}</if>
                    GROUP BY dtb.book_id
                </where>
            ) t
        <where>
            <if test="bookName != null and bookName != ''">
                and (
                book_name like concat('%', #{bookName}, '%')
                or isbn like concat('%', #{bookName}, '%')
                or editor like concat('%', #{bookName}, '%')
                )
            </if>
        </where>
    </select>

    <select id="selectBookList" resultMap="bookResultVo" parameterType="cn.dutp.edu.domain.dto.BookDto">
        select distinct
            do.book_id,
            do.book_name,
            do.author_label,
            do.author_value,
            do.cover,
            do.top_subject_id,
            do.second_subject_id,
            do.third_subject_id,
            do.forth_subject_id,
            IF(do.isbn IS NULL OR do.isbn = '', do.issn, do.isbn) AS isbn,
            do.book_no,
            do.publish_date,
            do.current_version_id,
            do.last_version_id,
            do.shelf_time,
            do.unshelf_time,
            do.publish_organization,
            do.publish_status,
            do.shelf_state,
            do.school_id,
            do.house_id,
            do.book_type,
            do.master_flag,
            do.master_book_id,
            do.sold_quantity,
            do.read_quantity,
            do.price_counter,
            do.price_sale,
            do.book_organize,
            do.topic_no,
            do.language_id,
            (
                select
                    count(0)
                from
                    dtb_user_book dub
                left join
                    dutp_user der on der.user_id = dub.user_id and der.del_flag = 0
                where
                    dub.del_flag = 0 and dub.book_id = do.book_id and der.user_type = 1
            ) as studentNum,
            (
                select
                    count(0)
                from
                    dtb_user_book dub
                left join
                    dutp_user der on der.user_id = dub.user_id and der.del_flag = 0
                where
                    dub.del_flag = 0 and dub.book_id = do.book_id and der.user_type = 2
            ) as teacherNum,
            do.author_value as editor
        from
            dtb_book do
        inner join
            dtb_book_order_item doi on doi.book_id = do.book_id
        where
            do.book_organize = #{dto.bookOrganize} and do.del_flag = 0
            <if test="dto.bookName != null and dto.bookName != ''">
                and (do.book_name like concat('%', #{dto.bookName}, '%') or do.book_no like concat('%', #{dto.bookName}, '%'))
            </if>
            <if test="dto.topSubjectId != null">and do.top_subject_id = #{dto.topSubjectId}</if>
            <if test="dto.secondSubjectId != null">and do.second_subject_id = #{dto.secondSubjectId}</if>
            <if test="dto.thirdSubjectId != null">and do.third_subject_id = #{dto.thirdSubjectId}</if>
            <if test="dto.forthSubjectId != null">and do.forth_subject_id = #{dto.forthSubjectId}</if>
            and doi.order_id in
            <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
                #{orderId}
            </foreach>
        group by
        do.book_id,
        do.book_name,
        do.author_label,
        do.author_value,
        do.cover,
        do.top_subject_id,
        do.second_subject_id,
        do.third_subject_id,
        do.forth_subject_id,
        do.isbn,issn,
        do.book_no,
        do.publish_date,
        do.current_version_id,
        do.last_version_id,
        do.shelf_time,
        do.unshelf_time,
        do.publish_organization,
        do.publish_status,
        do.shelf_state,
        do.school_id,
        do.house_id,
        do.book_type,
        do.master_flag,
        do.master_book_id,
        do.sold_quantity,
        do.read_quantity,
        do.price_counter,
        do.price_sale,
        do.book_organize,
        do.topic_no,
        do.language_id
    </select>

    <select id="selectEduSchoolBookList" resultType="cn.dutp.edu.domain.vo.BookVo" parameterType="cn.dutp.edu.domain.dto.BookDto">
        SELECT
            esb.school_book_id,
            esb.book_id,
            esb.day_limit,
            esb.expire_date,
            esb.push_flag,
            db.book_name,
            db.book_no,
            db.cover,
            db.shelf_time,
            db.current_version_id,
            db.read_quantity,
            db.master_flag,
            CASE WHEN db.current_version_id = db.last_version_id THEN 2 ELSE 1 END AS isLastFlag,
            esb.create_time,
            CASE WHEN NOW() > esb.expire_date THEN 1 ELSE 0 END AS isExpireFlag
        FROM
            edu_school_book esb
        INNER JOIN
            dtb_book db ON db.book_id = esb.book_id AND db.book_organize = 2 and db.master_flag != 3
        INNER JOIN
            (
                SELECT
                    book_id,
                    MAX(school_book_id) as school_book_id
                FROM
                    edu_school_book
                WHERE
                    school_id = #{schoolId}
                GROUP BY
                    book_id
            ) latest ON latest.school_book_id = esb.school_book_id
        WHERE
            db.del_flag = 0
            AND esb.school_id = #{schoolId}
            <if test="bookName != null and bookName != ''">
                AND (db.book_name LIKE CONCAT('%', #{bookName}, '%') OR db.book_no LIKE CONCAT('%', #{bookName}, '%'))
            </if>
            <if test="topSubjectId != null">AND db.top_subject_id = #{topSubjectId}</if>
            <if test="secondSubjectId != null">AND db.second_subject_id = #{secondSubjectId}</if>
            <if test="thirdSubjectId != null">AND db.third_subject_id = #{thirdSubjectId}</if>
            <if test="forthSubjectId != null">AND db.forth_subject_id = #{forthSubjectId}</if>
    </select>


    <select id="selectBookListForSchool" resultMap="bookResultVoForSchool" parameterType="cn.dutp.edu.domain.dto.BookDto">
        SELECT
            db.book_id,
            db.book_name,
            db.cover,
            db.isbn,
            db.issn,
            db.publish_status,
            db.shelf_state,
            db.book_organize,
            db.master_flag,
            db.current_step_id,
            db.school_id,
            ps.step_name
        FROM
            dtb_book db
            LEFT JOIN
            dtb_book_school bs ON db.book_id = bs.book_id
            LEFT JOIN
            dtb_book_publish_step ps ON db.current_step_id = ps.step_id
        WHERE
            db.del_flag = 0 AND bs.school_id = #{schoolId}
    </select>

    <select id="getOpenBookDetail" resultType="cn.dutp.edu.domain.vo.BookVo" parameterType="Long">
        select
            db.book_id,
            db.book_no,
            db.shelf_state,
            db.book_name,
            IF(db.isbn IS NULL OR db.isbn = '', db.issn, db.isbn) AS isbn,
            db.house_id,
            db.current_version_id,
            db.cover,
            dph.house_name,
            db.read_quantity,
            db.publish_date,
            db.publish_status,
            (
            SELECT
                GROUP_CONCAT(ds.school_name SEPARATOR ' ')
            from
                dtb_book_school dbs
            left join
                dutp_school ds on ds.school_id = dbs.school_id
            where
                dbs.book_id = db.book_id
            ) as schoolName,
            db.author_label as editorKey,
            db.author_value as editor,
            db.price_counter,
            db.price_sale,
            dba.copyright,
            dba.declaration,
            dba.recommend,
            dba.introduce,
            dba.other_attributes,
            CASE
            WHEN (
                select
                    count(di.order_item_id)
                from
                    dtb_book_order_item di
                left join
                    dtb_book_order do on do.order_id = di.order_id
                where
                    di.school_id = #{schoolId} and do.deleted = 1 and do.order_status in('settlement','completed') and di.item_status = 'normal' and di.book_id = db.book_id
            ) > 0 then 1 else 0 END as isBuyFlag
        from
            dtb_book db
        left join
            dtb_book_attribute dba on dba.book_id = db.book_id
        left join
            dutp_publishing_house dph on dph.house_id = db.house_id and dph.del_flag = 0
        where
            db.book_id = #{bookId} and db.del_flag = 0
    </select>

    <select id="getSchoolBookDetail" resultType="cn.dutp.edu.domain.vo.BookVo" parameterType="Long">
        select
            eb.school_book_id,
            db.book_id,
            db.book_no,
            db.book_name,
            IF(db.isbn IS NULL OR db.isbn = '', db.issn, db.isbn) AS isbn,
            db.house_id,
            db.current_version_id,
            db.cover,
            dph.house_name,
            eb.day_limit,
            eb.expire_date,
            db.read_quantity,
            db.publish_date,
            db.publish_status,
            (
            SELECT
                GROUP_CONCAT(ds.school_name SEPARATOR ' ')
            from
                dtb_book_school dbs
            left join
                dutp_school ds on ds.school_id = dbs.school_id
            where
                dbs.book_id = db.book_id
            ) as schoolName,
            db.author_label as editorKey,
            db.author_value as editor,
            CASE WHEN db.current_version_id = db.last_version_id THEN 2 ELSE 1 END AS isLastFlag,
            db.price_counter,
            db.price_sale,
            dba.copyright,
            dba.declaration,
            dba.recommend,
            dba.introduce,
            dba.other_attributes,
            CASE WHEN NOW() > eb.expire_date THEN 1 ELSE 0 END AS isExpireFlag
        from
            edu_school_book eb
        left join
            dtb_book db on eb.book_id = db.book_id
        left join
            dtb_book_attribute dba on dba.book_id = db.book_id
        left join
            dutp_publishing_house dph on dph.house_id = db.house_id and dph.del_flag = 0
        where
            db.book_id = #{bookId} and db.del_flag = 0
        order by eb.school_book_id desc
        limit 1
    </select>

    <!--根据学校id和订单类型，查询订单的列表-->
    <select id="selectOrderListBySchoolAndType" resultType="cn.dutp.edu.domain.vo.DutpEduOrderVo">
        SELECT
        dbo.order_id,
        dbo.order_no,
        SUM( CASE WHEN orderCode.state = 3 THEN 1 ELSE 0 END ) AS activationCount,
        SUM( CASE WHEN orderCode.state = 2 THEN 1 ELSE 0 END ) AS notActiveCount,
        dbo.create_time,
        dbo.pay_amount,
        dbo.order_status,
        dbo.price
        FROM
        dtb_book_order dbo
        LEFT JOIN dtb_book db ON db.book_id = dbo.book_id
        LEFT JOIN dtb_book_order_code orderCode ON dbo.order_id = orderCode.order_id
        LEFT JOIN dtb_book_purchase_code bookcode ON orderCode.code_id = bookcode.code_id
        WHERE
        dbo.deleted = 1
        AND dbo.school_id = #{schoolId}
        AND dbo.order_status IN ( 'settlement', 'completed' )
        <choose>
            <when test="orderType == 5">
                AND dbo.order_type = #{orderType}
            </when>
            <otherwise>
                AND dbo.order_type in(2,3)
            </otherwise>
        </choose>
        <if test="orderNo != null and orderNo != ''">and dbo.order_no like concat('%', #{orderNo}, '%')</if>
        <if test="bookName != null and bookName != ''">
            and dbo.order_id in(SELECT DISTINCT dborder.order_id
            FROM
            dtb_book_order dborder
            LEFT JOIN dtb_book_order_item item ON dborder.order_id = item.order_id
            LEFT JOIN dtb_book book ON book.book_id = item.book_id
            WHERE
            dborder.deleted = 1
            AND dborder.school_id = #{schoolId}
            AND dborder.order_status IN ( 'settlement', 'completed' )
            <choose>
                <when test="orderType == 5">
                    and dborder.order_type = #{orderType}
                </when>
                <otherwise>
                    and dborder.order_type in(2,3)
                </otherwise>
            </choose>
            and(
            book.book_name LIKE concat( '%', #{bookName}, '%' )
            OR book.isbn LIKE concat( '%', #{bookName}, '%' )
            OR book.issn LIKE concat( '%', #{bookName}, '%' )
            )
            )
        </if>
        GROUP BY
        dbo.order_id
        ORDER BY
        dbo.create_time DESC
    </select>

    <!--根据学校id和主订单id，查询子订单的列表-->
    <select id="selectItemListBySchoolAndOrder" resultType="cn.dutp.edu.domain.vo.DutpEduOrderDetailVo">
        SELECT book.book_name,book.book_id,
               (CASE WHEN book.isbn IS NOT NULL and book.isbn!= '' THEN book.isbn else book.issn END) AS isbn,
               item.book_quantity,
               item.item_status,
               item.price_order_item,
               item.price_sale,
               item.price_counter,
               SUM(CASE WHEN dbpc.state = '3' THEN 1 ELSE 0 END) AS bindingCodeCount,
                (item.book_quantity - SUM(CASE WHEN dbpc.state = '3' THEN 1 ELSE 0 END)) AS unBindingCodeCount
        FROM dtb_book_order_item item
                 LEFT JOIN dtb_book_order_code orderCode ON item.order_item_id = orderCode.order_item_id
                 LEFT JOIN dtb_book book ON book.book_id = item.book_id
                 LEFT JOIN (SELECT dbpc.book_id,
                                    dboc.state,
                                   dboc.school_id,
                                   dboc.order_id
                                FROM dtb_book_order_code dboc
                                     inner JOIN dtb_book_purchase_code dbpc ON dbpc.code_id = dboc.code_id
                            WHERE dbpc.del_flag = '0'
                              AND dboc.school_id = #{schoolId}) dbpc ON dbpc.book_id = item.book_id and dbpc.order_id = item.order_id
        WHERE item.order_id = #{orderId}
        GROUP BY item.order_item_id
        ORDER BY item.create_time DESC
    </select>

    <select id="getBookDetailByItemId" parameterType="Long" resultType="cn.dutp.edu.domain.vo.DutpEduOrderDetailVo">
        select
            doi.order_item_id,
            IF(db.isbn IS NULL OR db.isbn = '', db.issn, db.isbn) AS isbn,
            doi.order_item_id,
            doi.book_id,
            db.book_name,
            doi.price_sale,
            doi.price_counter,
            doi.price_order_item,
            doi.book_quantity,
            doi.item_status,
            dh.house_id,
            dh.house_name,
            (
                SELECT
                    GROUP_CONCAT(ser.nick_name SEPARATOR ' ')
                FROM
                    dtb_book_group dup
                left join
                    sys_user ser on ser.user_id = dup.user_id and ser.del_flag = 0
                WHERE
                    dup.del_flag = 0 and dup.book_id = db.book_id and dup.role_type = 2
            ) as editor,
            (
                SELECT
                    GROUP_CONCAT(ds.school_name SEPARATOR ' ')
                from
                    dtb_book_school dbs
                        left join
                    dutp_school ds on ds.school_id = dbs.school_id
                where
                    dbs.book_id = db.book_id
            ) as schoolName
        from
            dtb_book_order_item doi
        left join
            dtb_book db on db.book_id = doi.book_id
        left join
            dutp_publishing_house dh on dh.house_id = db.house_id
        where
            doi.order_item_id = #{orderItemId}
    </select>
    <!--根据订单id获取订单明细的书籍集合-->
    <select id="selectBookListByOrderId" parameterType="Long" resultType="cn.dutp.edu.domain.vo.BookVo">
        select book.book_id, book.book_name
        from dtb_book_order_item item
                 left join dtb_book book on book.book_id = item.book_id
        where item.order_id = #{orderId}
          and book.del_flag = 0
    </select>

    <select id="selectSchoolById" resultType="cn.dutp.edu.domain.DutpSchool" parameterType="Long">
        WITH RECURSIVE school_hierarchy AS (
            SELECT
                school_id,
                school_name,
                parent_id
            FROM
                dutp_school
            WHERE
                school_id = #{schoolId}

            UNION ALL
            SELECT
                c.school_id,
                c.school_name,
                c.parent_id
            FROM
                dutp_school c
                    INNER JOIN school_hierarchy cc ON c.parent_id = cc.school_id
        )
        SELECT * FROM school_hierarchy;
    </select>

    <select id="selectSchoolBookBySchoolBookId" resultType="cn.dutp.edu.domain.dto.DtbUserBookDto" parameterType="Long">
        select
            esb.book_id,
            db.current_version_id as versionId,
            esb.expire_date
        from
            dtb_book db
        left join
            edu_school_book esb on esb.book_id = db.book_id
        where
            esb.school_book_id = #{schoolBookId}

    </select>

</mapper>
