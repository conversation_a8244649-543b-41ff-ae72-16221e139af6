<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseMapper">
    
    <resultMap type="MoocSmartCourse" id="MoocSmartCourseResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseCode"    column="course_code"    />
        <result property="courseType"    column="course_type"    />
        <result property="categoryId"    column="category_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="description"    column="description"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="classroomQuestionWeight"    column="classroom_question_weight"    />
        <result property="homeworkWeight"    column="homework_weight"    />
        <result property="examWeight"    column="exam_weight"    />
        <result property="hour"    column="hour"    />
        <result property="status"    column="status"    />
        <result property="userId"    column="user_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseVo">
        select course_id, course_name, course_code, course_type, category_id, plan_id, book_id, description, cover_image_url, classroom_question_weight, homework_weight, exam_weight, hour, status, user_id, created_by, updated_by, del_flag, create_time, update_time from mooc_smart_course
    </sql>

    <select id="getListData" resultType="cn.dutp.edu.domain.MoocSmartCourse">
        SELECT
            msc.course_id,
            msc.course_name,
            msc.course_code,
            msc.course_type,
            msc.category_id,
            msc.plan_id,
            msc.book_id,
            msc.description,
            msc.cover_image_url,
            msc.classroom_question_weight,
            msc.homework_weight,
            msc.exam_weight,
            msc.hour,
            msc.status,
            msc.user_id,
            count(scc.class_id) as classTotal,
            du.real_name
        FROM
            mooc_smart_course msc
                left join mooc_smart_course_class scc on msc.course_id = scc.course_id and scc.del_flag = 0
                left join dutp_user du on msc.user_id = du.user_id
        where msc.del_flag = 0
        <if test="param.status != null">
            and msc.status = #{param.status}
        </if>
        <if test="param.searchVar != null and param.searchVar != ''">
            and msc.course_name like concat('%',#{param.searchVar},'%')
        </if>
        <if test="param.courseType != null">
            and msc.course_type = #{param.courseType}
        </if>
        group by msc.course_id
        order by msc.create_time desc
    </select>
</mapper>