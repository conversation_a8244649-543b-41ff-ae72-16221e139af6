<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.EduTeacherMapper">

    <select id="selectTeacherList" resultType="cn.dutp.edu.domain.vo.TeacherVo">
        SELECT
        u.user_id,
        u.user_name,
        u.real_name,
        u.phonenumber,
        u.user_no,
        u.email,
        s.school_name as academy_name,
        (SELECT school_name from dutp_school where school_id = u.speciality_id) as subject_name,
        u.speciality_Id,
        u.academy_Id,
        u.school_id,
        u.create_time
        FROM
        dutp_user AS u
        LEFT JOIN dutp_school AS s ON s.school_id = u.academy_id
        AND s.data_type = 1
        <where>
            <if test="academyId != null">AND u.academy_Id = #{academyId}</if>
            <if test="specialityId != null">AND u.speciality_Id = #{specialityId}</if>
            <if test="realName != null and realName != ''">AND u.real_name LIKE CONCAT('%', #{realName}, '%')</if>
            <if test="userNo != null and userNo != ''">AND u.user_no LIKE CONCAT('%', #{userNo}, '%')</if>
            AND u.user_type = 2
            AND u.del_flag = 0
            AND u.school_id = #{schoolId}
        </where>
    </select>
    <select id="selectTeacherById" resultType="cn.dutp.edu.domain.vo.TeacherVo">
        select
            u.user_id,
            U.speciality_id,
            U.academy_id,
            u.user_name,
            u.real_name,
            u.phonenumber,
            u.user_no,
            s.school_name,
            (SELECT school_name from dutp_school where school_id = u.speciality_id) as subject_name,
            u.remark

        FROM
            dutp_user AS u
                LEFT JOIN dutp_school AS s ON s.school_id = u.academy_id

        WHERE
            u.user_type = 2
          AND u.del_flag = 0
          AND u.user_id = #{userId}
    </select>
</mapper>