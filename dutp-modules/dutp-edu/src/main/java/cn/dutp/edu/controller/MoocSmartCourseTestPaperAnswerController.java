package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseTestPaperAnswer;
import cn.dutp.edu.service.IMoocSmartCourseTestPaperAnswerService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂学生考试/作业结果Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/answer")
public class MoocSmartCourseTestPaperAnswerController extends BaseController
{
    @Autowired
    private IMoocSmartCourseTestPaperAnswerService moocSmartCourseTestPaperAnswerService;

    /**
     * 查询互动课堂学生考试/作业结果列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        startPage();
        
        List<MoocSmartCourseTestPaperAnswer> list = moocSmartCourseTestPaperAnswerService.selectMoocSmartCourseTestPaperAnswerList(moocSmartCourseTestPaperAnswer);
        return getDataTable(list);
    }


    @Log(title = "导出互动课堂学生考试/作业结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        List<MoocSmartCourseTestPaperAnswer> list = moocSmartCourseTestPaperAnswerService.selectMoocSmartCourseTestPaperAnswerList(moocSmartCourseTestPaperAnswer);
        ExcelUtil<MoocSmartCourseTestPaperAnswer> util = new ExcelUtil<MoocSmartCourseTestPaperAnswer>(MoocSmartCourseTestPaperAnswer.class);
        util.exportExcel(response, list, "互动课堂学生考试/作业结果数据");
    }

    /**
     * 获取互动课堂学生考试/作业结果详细信息
     */

    @GetMapping(value = "/{answertId}")
    public AjaxResult getInfo(@PathVariable("answertId") Long answertId)
    {
        return success(moocSmartCourseTestPaperAnswerService.selectMoocSmartCourseTestPaperAnswerByAnswertId(answertId));
    }

    /**
     * 新增互动课堂学生考试/作业结果
     */

    @Log(title = "新增互动课堂学生考试/作业结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        moocSmartCourseTestPaperAnswer.setUserId(SecurityUtils.getUserId());
        return toAjax(moocSmartCourseTestPaperAnswerService.insertMoocSmartCourseTestPaperAnswer(moocSmartCourseTestPaperAnswer));
    }

    /**
     * 修改互动课堂学生考试/作业结果
     */

    @Log(title = "修改互动课堂学生考试/作业结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        return toAjax(moocSmartCourseTestPaperAnswerService.updateMoocSmartCourseTestPaperAnswer(moocSmartCourseTestPaperAnswer));
    }

    /**
     * 删除互动课堂学生考试/作业结果
     */

    @Log(title = "删除互动课堂学生考试/作业结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{answertIds}")
    public AjaxResult remove(@PathVariable Long[] answertIds)
    {
        return toAjax(moocSmartCourseTestPaperAnswerService.deleteMoocSmartCourseTestPaperAnswerByAnswertIds(Arrays.asList(answertIds)));
    }

    /**
     * 根据试卷ID查询当前用户的答题结果
     */
    @GetMapping("/getUserAnswer/{testPaperId}")
    public AjaxResult getUserAnswer(@PathVariable("testPaperId") Long testPaperId)
    {
        Long userId = SecurityUtils.getUserId();
        return success(moocSmartCourseTestPaperAnswerService.selectUserAnswerByTestPaperId(testPaperId, userId));
    }

    /**
     * 根据试卷ID查询班级成员的答题结果
     */
    @GetMapping("/getUserAnswerByAssignmentId")
    public TableDataInfo getUserAnswerByAssignmentId(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer)
    {
        return getDataTable(moocSmartCourseTestPaperAnswerService.getUserAnswerByAssignmentId(moocSmartCourseTestPaperAnswer));
    }
}
