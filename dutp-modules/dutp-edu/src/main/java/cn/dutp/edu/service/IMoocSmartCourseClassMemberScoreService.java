package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseClassMemberScore;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂班级成员各项汇总成绩Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseClassMemberScoreService extends IService<MoocSmartCourseClassMemberScore>
{
    /**
     * 查询互动课堂班级成员各项汇总成绩
     *
     * @param memberScoreId 互动课堂班级成员各项汇总成绩主键
     * @return 互动课堂班级成员各项汇总成绩
     */
    public MoocSmartCourseClassMemberScore selectMoocSmartCourseClassMemberScoreByMemberScoreId(Long memberScoreId);

    /**
     * 查询互动课堂班级成员各项汇总成绩列表
     *
     * @param moocSmartCourseClassMemberScore 互动课堂班级成员各项汇总成绩
     * @return 互动课堂班级成员各项汇总成绩集合
     */
    public List<MoocSmartCourseClassMemberScore> selectMoocSmartCourseClassMemberScoreList(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore);

    /**
     * 新增互动课堂班级成员各项汇总成绩
     *
     * @param moocSmartCourseClassMemberScore 互动课堂班级成员各项汇总成绩
     * @return 结果
     */
    public boolean insertMoocSmartCourseClassMemberScore(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore);

    /**
     * 修改互动课堂班级成员各项汇总成绩
     *
     * @param moocSmartCourseClassMemberScore 互动课堂班级成员各项汇总成绩
     * @return 结果
     */
    public boolean updateMoocSmartCourseClassMemberScore(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore);

    /**
     * 批量删除互动课堂班级成员各项汇总成绩
     *
     * @param memberScoreIds 需要删除的互动课堂班级成员各项汇总成绩主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseClassMemberScoreByMemberScoreIds(List<Long> memberScoreIds);

    List<MoocSmartCourseClassMemberScore> getListByClassId(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore);
}
