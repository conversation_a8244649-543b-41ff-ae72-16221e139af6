package cn.dutp.edu.service.impl;

import java.util.Collections;
import java.util.List;

import cn.dutp.common.security.utils.SecurityUtils;

import cn.dutp.edu.domain.MoocSmartCourseHomeworkOrTestWithPaperVO;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseHomeworkOrTestMapper;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkOrTest;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkOrTestService;
import cn.dutp.edu.domain.MoocSmartCourseTestPaper;
import cn.dutp.edu.service.IMoocSmartCourseTestPaperService;
import org.springframework.beans.BeanUtils;

/**
 * 互动课堂作业/考试管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseHomeworkOrTestServiceImpl extends ServiceImpl<MoocSmartCourseHomeworkOrTestMapper, MoocSmartCourseHomeworkOrTest> implements IMoocSmartCourseHomeworkOrTestService
{
    @Autowired
    private MoocSmartCourseHomeworkOrTestMapper moocSmartCourseHomeworkOrTestMapper;

    @Autowired
    private IMoocSmartCourseTestPaperService moocSmartCourseTestPaperService;

    /**
     * 查询互动课堂作业/考试管理
     *
     * @param assignmentId 互动课堂作业/考试管理主键
     * @return 互动课堂作业/考试管理
     */
    @Override
    public MoocSmartCourseHomeworkOrTest selectMoocSmartCourseHomeworkOrTestByAssignmentId(Long assignmentId)
    {
        return this.getById(assignmentId);
    }

    @Override
    public MoocSmartCourseHomeworkOrTestWithPaperVO selectHomeworkWithPaperByHomeworkId(Long assignmentId) {
        MoocSmartCourseHomeworkOrTest homework = this.getById(assignmentId);
        if (homework == null) {
            return null;
        }

        MoocSmartCourseHomeworkOrTestWithPaperVO vo = new MoocSmartCourseHomeworkOrTestWithPaperVO();
        BeanUtils.copyProperties(homework, vo);

        if (homework.getTestPaperId() != null) {
            MoocSmartCourseTestPaper paper = moocSmartCourseTestPaperService.selectMoocSmartCourseTestPaperByPaperId(homework.getTestPaperId());
            if (paper != null) {
                BeanUtils.copyProperties(paper, vo);
            }
        }

        return vo;
    }

    /**
     * 查询互动课堂作业/考试管理列表
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 互动课堂作业/考试管理
     */
    @Override
    public List<MoocSmartCourseHomeworkOrTest> selectMoocSmartCourseHomeworkOrTestList(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        LambdaQueryWrapper<MoocSmartCourseHomeworkOrTest> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getTestPaperId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getTestPaperId
                ,moocSmartCourseHomeworkOrTest.getTestPaperId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getCourseId
                ,moocSmartCourseHomeworkOrTest.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getClassId
                ,moocSmartCourseHomeworkOrTest.getClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getCreatorId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getCreatorId
                ,moocSmartCourseHomeworkOrTest.getCreatorId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getName())) {
                lambdaQueryWrapper.like(MoocSmartCourseHomeworkOrTest::getName
                ,moocSmartCourseHomeworkOrTest.getName());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getAssignmentType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getAssignmentType
                ,moocSmartCourseHomeworkOrTest.getAssignmentType());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getContentType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getContentType
                ,moocSmartCourseHomeworkOrTest.getContentType());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getSubmissionUnit())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getSubmissionUnit
                ,moocSmartCourseHomeworkOrTest.getSubmissionUnit());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getScore
                ,moocSmartCourseHomeworkOrTest.getScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getGradingMethod())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getGradingMethod
                ,moocSmartCourseHomeworkOrTest.getGradingMethod());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getSubmitStartTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getSubmitStartTime
                ,moocSmartCourseHomeworkOrTest.getSubmitStartTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getSubmitEndTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getSubmitEndTime
                ,moocSmartCourseHomeworkOrTest.getSubmitEndTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getGradingStartTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getGradingStartTime
                ,moocSmartCourseHomeworkOrTest.getGradingStartTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getGradingEndTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getGradingEndTime
                ,moocSmartCourseHomeworkOrTest.getGradingEndTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getAssignmentRequirement())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getAssignmentRequirement
                ,moocSmartCourseHomeworkOrTest.getAssignmentRequirement());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getAttachedMaterialIds())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getAttachedMaterialIds
                ,moocSmartCourseHomeworkOrTest.getAttachedMaterialIds());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getReferenceAnswerContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getReferenceAnswerContent
                ,moocSmartCourseHomeworkOrTest.getReferenceAnswerContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseHomeworkOrTest.getReferenceAnswerMaterialIds())) {
                lambdaQueryWrapper.eq(MoocSmartCourseHomeworkOrTest::getReferenceAnswerMaterialIds
                ,moocSmartCourseHomeworkOrTest.getReferenceAnswerMaterialIds());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂作业/考试管理
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseHomeworkOrTest(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        return this.save(moocSmartCourseHomeworkOrTest);
    }

    /**
     * 修改互动课堂作业/考试管理
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseHomeworkOrTest(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        return this.updateById(moocSmartCourseHomeworkOrTest);
    }

    /**
     * 批量删除互动课堂作业/考试管理
     *
     * @param assignmentIds 需要删除的互动课堂作业/考试管理主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseHomeworkOrTestByAssignmentIds(List<Long> assignmentIds)
    {
        return this.removeByIds(assignmentIds);
    }

    /**
     * 查询互动课堂作业/考试管理列表
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 互动课堂作业/考试管理
     */
    @Override
    public List<MoocSmartCourseHomeworkOrTest> getHomeworkOrTestList(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        moocSmartCourseHomeworkOrTest.setUserId(SecurityUtils.getUserId());
        return baseMapper.getHomeworkOrTestList(moocSmartCourseHomeworkOrTest);
    }

}
