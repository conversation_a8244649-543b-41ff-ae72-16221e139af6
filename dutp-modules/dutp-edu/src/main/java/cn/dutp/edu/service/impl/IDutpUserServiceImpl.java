package cn.dutp.edu.service.impl;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.edu.domain.DutpAiUserConfig;
import cn.dutp.edu.domain.dto.DutpUserDto;
import cn.dutp.edu.domain.vo.DutpUserVo;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.mapper.DutpAiUserConfigMapper;
import cn.dutp.edu.mapper.DutpUserMapper;
import cn.dutp.edu.service.DutpUserService;
import cn.dutp.system.api.domain.SysUser;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户信息业务层处理
 *
 * @author: dutp
 * @date: 2024/11/5 9:51
 */
@Service
public class IDutpUserServiceImpl extends ServiceImpl<DutpUserMapper, DutpUser> implements DutpUserService {

    @Resource
    private DutpUserMapper dutpUserMapper;

    @Value("${spring.experimentCount}")
    private Integer experimentCount;

    @Resource
    private DutpAiUserConfigMapper dutpAiUserConfigMapper;

    /**
     * 查询用户列表
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    @Override
    public List<DutpUser> selectAll(DutpUser dutpUser) {
        return dutpUserMapper.selectAll(dutpUser);
    }

    @Override
    public List<DutpUser> selectStudent(DutpUser dutpUser) {
        return dutpUserMapper.selectStudent(dutpUser);
    }

    @Override
    public List<DutpUser> selectTeacher(DutpUser dutpUser) {
        return dutpUserMapper.selectTeacher(dutpUser);
    }

    /**
     * 获取用户信息详情
     *
     * @return 结果
     */
    @Override
    public DutpUser getUserById() {
        return dutpUserMapper.getUserInfo(SecurityUtils.getUserId());
    }

    /**
     * 添加学生信息
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    @Override
    public boolean insertStudent(DutpUser dutpUser) {
        if (ObjectUtil.isNotEmpty(dutpUser.getUserName())){
            LambdaQueryWrapper<DutpUser> dutpUserWrapper = new LambdaQueryWrapper<DutpUser>();
            dutpUserWrapper.eq(DutpUser::getUserName, dutpUser.getUserName());
            List<DutpUser> list = this.list(dutpUserWrapper);
            if (ObjectUtil.isNotEmpty(list)){
                throw new ServiceException("账号不能重复");
            }
        }
        dutpUser.setPhonenumber(dutpUser.getUserName());
        dutpUser.setNickName(dutpUser.getUserName());
        dutpUser.setUserType("1");
        boolean flag = this.save(dutpUser);
        if (flag) {
            // 保存试用次数
            saveDutpAiUserConfig(dutpUser.getUserId());
        }
        return flag;
    }

    public void saveDutpAiUserConfig(Long userId){
        DutpAiUserConfig config = new DutpAiUserConfig();
        config.setUserType(0);
        config.setUserId(userId);
        config.setAiExperimentCount(experimentCount);
        dutpAiUserConfigMapper.insert(config);
    }

    /**
     * 添加教师信息
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    @Override
    public boolean insertTeacher(DutpUser dutpUser) {
        if (ObjectUtil.isNotEmpty(dutpUser.getUserName())){
            LambdaQueryWrapper<DutpUser> dutpUserWrapper = new LambdaQueryWrapper<DutpUser>();
            dutpUserWrapper.eq(DutpUser::getUserName, dutpUser.getUserName());
            List<DutpUser> list = this.list(dutpUserWrapper);
            if (ObjectUtil.isNotEmpty(list)){
                throw new ServiceException("账号不能重复");
            }
        }

        dutpUser.setPhonenumber(dutpUser.getUserName());
        dutpUser.setNickName(dutpUser.getUserName());
        dutpUser.setUserType("2");
        boolean flag = this.save(dutpUser);
        if (flag) {
            saveDutpAiUserConfig(dutpUser.getUserId());
        }
        return flag;
    }

    /**
     * 添加读者信息
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    @Override
    public boolean insertUser(DutpUser dutpUser) {
        if (ObjectUtil.isNotEmpty(dutpUser.getUserName())){
            LambdaQueryWrapper<DutpUser> dutpUserWrapper = new LambdaQueryWrapper<DutpUser>();
            dutpUserWrapper.eq(DutpUser::getUserName, dutpUser.getUserName());
            List<DutpUser> list = this.list(dutpUserWrapper);
            if (ObjectUtil.isNotEmpty(list)){
                throw new ServiceException("账号不能重复");
            }
        }
        dutpUser.setPhonenumber(dutpUser.getUserName());
        dutpUser.setNickName(dutpUser.getUserName());
        dutpUser.setUserType("0");
        boolean flag = this.save(dutpUser);
        if (flag) {
            saveDutpAiUserConfig(dutpUser.getUserId());
        }
        return flag;
    }

    /**
     * 修改用户信息
     *
     * @param dutpUser 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUser(DutpUser dutpUser) {
        if (dutpUser.getUserType().equals("0")){
            dutpUser.setSchoolId(null);
            dutpUser.setAcademyId(null);
            dutpUser.setUserNo(null);
        }
        dutpUser.setPhonenumber(dutpUser.getUserName());

        return this.updateById(dutpUser);
    }

    /**
     * 禁用状态
     *
     * @param userId 用户id
     * @return 结果
     */
    @Override
    public boolean changeStatus(Long userId) {
        return dutpUserMapper.changeStatus(userId);
    }

    /**
     * 启用状态
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean openStatus(Long userId) {
        return dutpUserMapper.openStatus(userId);
    }

    /**
     * 导入读者
     *
     * @param dutpUserDto@return 结果
     */
    @Override
    public String importUser(DutpUserDto dutpUserDto) {
        List<DutpUser> list = dutpUserDto.getList();
        if (ObjectUtil.isEmpty(dutpUserDto) || list.size() == 0) {
            throw new ServiceException("导入读者信息不能为空");
        }

        if(dutpUserDto.getFileName().contains("教师") || dutpUserDto.getFileName().contains("学生")){
            throw new ServiceException("当前选择导入的对象是读者，请正确的选择导入对象");
        }


        int successNum = 0;
        int failNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failMsg = new StringBuilder();

        try {
            for (int i = 0; i < list.size(); i++) {
                DutpUser user = list.get(i);

                // 检查账号是否为空
                if (StringUtils.isBlank(user.getUserName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 账号不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查姓名是否为空
                if (StringUtils.isBlank(user.getRealName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 姓名不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查账号是否已存在
                LambdaQueryWrapper<DutpUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DutpUser::getUserName, user.getUserName());
                List<DutpUser> existingUser = this.list(queryWrapper);
                if (existingUser.size() > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 账号已存在!";
                    failMsg.append(msg);
                    continue;
                }

                try {
                    // 设置创建时间并保存用户
                    user.setCreateTime(new Date());
                    user.setPassword(SecurityUtils.encryptPassword("123456"));
                    user.setNickName(user.getUserName());
                    user.setPhonenumber(user.getUserName());
                    this.save(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                } catch (Exception e) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败";
                    failMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }
        } catch (NullPointerException e) {
            failMsg.insert(0, "很抱歉, 导入失败! 请确定所有字段都已填写!");
            return failMsg.toString();
        }

        // 返回导入结果
        if (failNum > 0) {
            failMsg.insert(0, "共" + list.size() + "条数据, 其中" + successNum + "条导入成功, " + failNum + "条导入失败，错误如下：");
            return failMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    /**
     * 导入学生
     *
     * @param dutpUserDto
     * @return
     */
    @Override
    public String importStudent(DutpUserDto dutpUserDto) {
        List<DutpUser> list = dutpUserDto.getList();
        if (ObjectUtil.isNull(dutpUserDto) || list.size() == 0) {
            throw new ServiceException("导入学生信息不能为空");
        }
        if(dutpUserDto.getFileName().contains("教师") || dutpUserDto.getFileName().contains("用户")){
            throw new ServiceException("当前选择导入的对象是学生，请正确的选择导入对象");
        }

        int successNum = 0;
        int failNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failMsg = new StringBuilder();

        // 设置用户类型为学生
        for (DutpUser dutpUser : list) {
            dutpUser.setUserType("1");

            // 检查学校名称是否存在
            Long schoolId = dutpUserMapper.selectIdBySchool(dutpUser.getSchoolName());
            if (ObjectUtil.isEmpty(schoolId)) {
                throw new ServiceException("学校名称不存在: " + dutpUser.getSchoolName());
            }
            dutpUser.setSchoolId(schoolId);

            // 检查学院名称是否存在
            List<String> academyNameList = dutpUserMapper.selectIdByacademyName(schoolId);
            if (ObjectUtil.isEmpty(academyNameList) || !academyNameList.contains(dutpUser.getAcademyName())) {
                if (!academyNameList.contains(dutpUser.getAcademyName())) {
                    throw new ServiceException("该学校不存在学院名称为: " + dutpUser.getAcademyName());
                }
            }
            //查询学院id
            Long academyId = dutpUserMapper.selectAcademyId(dutpUser.getAcademyName(),schoolId);
            dutpUser.setAcademyId(academyId);

            // 查询当前学校下的学院是否有该专业
                List<String> subjectNameList = dutpUserMapper.selectSubjectNameById(academyId);
                if(ObjectUtil.isEmpty(subjectNameList) && StringUtils.isNotEmpty(dutpUser.getSubjectName()) || !subjectNameList.contains(dutpUser.getSubjectName()) && StringUtils.isNotEmpty(dutpUser.getSubjectName())){
                    throw new ServiceException("该学院不存在专业名称为: " + dutpUser.getAcademyName());
                }

            Long subjectId = dutpUserMapper.selectSubjectId(dutpUser.getSubjectName(), academyId);
            dutpUser.setSpecialityId(subjectId);
        }

        try {
            for (int i = 0; i < list.size(); i++) {
                DutpUser user = list.get(i);

                // 检查账号是否为空
                if (StringUtils.isBlank(user.getUserName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 账号不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查姓名是否为空
                if (StringUtils.isBlank(user.getRealName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 真实姓名不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查学号是否为空
                if (StringUtils.isBlank(user.getUserNo())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 学号不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查学校名称是否为空
                if (StringUtils.isBlank(user.getSchoolName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 学校名称不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查学院名称是否为空
                if (StringUtils.isBlank(user.getAcademyName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 学院名称不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查账号是否已存在
                LambdaQueryWrapper<DutpUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DutpUser::getUserName, user.getUserName());
                List<DutpUser> existingUser = this.list(queryWrapper);
                if (existingUser.size() > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 账号已存在!";
                    failMsg.append(msg);
                    continue;
                }

                try {
                    // 设置创建时间并保存用户
                    user.setCreateTime(new Date());
                    user.setUserType("1");
                    user.setPassword(SecurityUtils.encryptPassword("123456"));
                    user.setNickName(user.getUserName());
                    user.setPhonenumber(user.getUserName());
                    this.save(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                } catch (Exception e) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败";
                    failMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }
        } catch (NullPointerException e) {
            failMsg.insert(0, "很抱歉, 导入失败! 请确定所有字段都已填写!");
            return failMsg.toString();
        }

        // 返回导入结果
        if (failNum > 0) {
            failMsg.insert(0, "共" + list.size() + "条数据, 其中" + successNum + "条导入成功, " + failNum + "条导入失败，错误如下：");
            return failMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    @Override
    public String importTeacher(DutpUserDto dutpUserDto) {
        List<DutpUser> list = dutpUserDto.getList();
        if (StringUtils.isNull(dutpUserDto) || list.size() == 0) {
            throw new ServiceException("导入读者信息不能为空");
        }
        if(dutpUserDto.getFileName().contains("用户") || dutpUserDto.getFileName().contains("学生")){
            throw new ServiceException("当前选择导入的对象是教师，请正确的选择导入对象");
        }

        int successNum = 0;
        int failNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failMsg = new StringBuilder();

        // 设置用户类型为教师
        for (DutpUser dutpUser : list) {
            dutpUser.setUserType("2");

            // 检查学校名称是否存在
            Long schoolId = dutpUserMapper.selectIdBySchool(dutpUser.getSchoolName());
            if (ObjectUtil.isEmpty(schoolId)) {
                throw new ServiceException("学校名称不存在: " + dutpUser.getSchoolName());
            }
            dutpUser.setSchoolId(schoolId);

            // 检查学院名称是否存在
            List<String> academyNameList = dutpUserMapper.selectIdByacademyName(schoolId);
            if (ObjectUtil.isEmpty(academyNameList) || !academyNameList.contains(dutpUser.getAcademyName())) {
                if (!academyNameList.contains(dutpUser.getAcademyName())) {}
                throw new ServiceException("该学校不存在学院名称为: " + dutpUser.getAcademyName());
            }
            //查询学院id
            Long academyId = dutpUserMapper.selectAcademyId(dutpUser.getAcademyName(),schoolId);
            dutpUser.setAcademyId(academyId);

            // 查询当前学校下的学院是否有该专业
            List<String> subjectNameList = dutpUserMapper.selectSubjectNameById(academyId);
            if(ObjectUtil.isEmpty(subjectNameList) && StringUtils.isNotEmpty(dutpUser.getSubjectName()) || !subjectNameList.contains(dutpUser.getSubjectName()) && StringUtils.isNotEmpty(dutpUser.getSubjectName())){
                throw new ServiceException("该学院不存在专业名称为: " + dutpUser.getAcademyName());
            }

            Long subjectId = dutpUserMapper.selectSubjectId(dutpUser.getSubjectName(), academyId);
            dutpUser.setSpecialityId(subjectId);

        }

        try {
            for (int i = 0; i < list.size(); i++) {
                DutpUser user = list.get(i);

                // 检查账号是否为空
                if (StringUtils.isBlank(user.getUserName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 账号不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查姓名是否为空
                if (StringUtils.isBlank(user.getRealName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 真实姓名不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查工号是否为空
                if (StringUtils.isBlank(user.getUserNo())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 工号不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查学校名称是否为空
                if (StringUtils.isBlank(user.getSchoolName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 学校名称不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查学院名称是否为空
                if (StringUtils.isBlank(user.getAcademyName())) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 学院名称不能为空!";
                    failMsg.append(msg);
                    continue;
                }

                // 检查账号是否已存在
                LambdaQueryWrapper<DutpUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DutpUser::getUserName, user.getUserName());
                List<DutpUser> existingUser = this.list(queryWrapper);
                if (existingUser.size() > 0) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败, 账号已存在!";
                    failMsg.append(msg);
                    continue;
                }

                try {
                    // 设置创建时间并保存用户
                    user.setCreateTime(new Date());
                    user.setPassword(SecurityUtils.encryptPassword("123456"));
                    user.setNickName(user.getUserName());
                    user.setPhonenumber(user.getUserName());
                    this.save(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、第" + (i + 1) + "行 导入成功");
                } catch (Exception e) {
                    failNum++;
                    String msg = "<br/>" + failNum + "、第" + (i + 1) + "行 导入失败";
                    failMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }
        } catch (NullPointerException e) {
            failMsg.insert(0, "很抱歉, 导入失败! 请确定所有字段都已填写!");
            return failMsg.toString();
        }

        // 返回导入结果
        if (failNum > 0) {
            failMsg.insert(0, "共" + list.size() + "条数据, 其中" + successNum + "条导入成功, " + failNum + "条导入失败，错误如下：");
            return failMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            return successMsg.toString();
        }
    }

    /**
     * 注销账号
     *
     * @param Ids 需要删除的教师认证主键集合
     * @return 结果
     */
    @Override
    public boolean cancelAccount(List<Long> Ids) {
        return this.removeByIds(Ids);
    }

    /**
     * 根据登陆用户的学校id获取 学生数量 教师数量
     *
     * @param schoolId 学校id
     * @return DutpUserVo
     */
    @Override
    public DutpUserVo countTeacherAndStudentBySchoolId(long schoolId) {
        return dutpUserMapper.countTeacherAndStudentBySchoolId(schoolId);
    }

    @Override
    public DutpUser getDetail(DutpUser dutpUser) {
        if (ObjectUtil.isEmpty(dutpUser)) {
            dutpUser.setAcademyIdStr(dutpUser.getAcademyId().toString());
        }
        return this.getById(dutpUser);
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(DutpUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            DutpUser user = new DutpUser();
            user.setUserId(userId);
            List<DutpUser> users = SpringUtils.getAopProxy(this).selectAll(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 重置密码
     *
     *
     * @return
     */
    @Override
    public boolean resetPwd(DutpUser user) {
        return this.updateById(user);
    }

    @Override
    @Transactional
    public Boolean cancelDutpUser(DutpUser user) {
        Integer checkCode = AliyunSmsUtil.checkCode(user.getPhonenumber(), user.getCode(), "SMS_314725755");
        if (checkCode != 200) {
            throw new ServiceException("验证码输入错误！", 7001);

        }
        return this.removeById(SecurityUtils.getUserId());
    }

    /**
     * 导出用户信息
     * @param response
     * @param dutpUser
     * @return
     */
    @Override
    public void exportUser(HttpServletResponse response, DutpUser dutpUser) {
        // 参数校验
        if (ObjectUtil.isEmpty(dutpUser.getIds())) {
            throw new ServiceException("请选择要导出的数据");
        }

        // 去重处理
        List<Long> ids = dutpUser.getIds().stream().distinct().collect(Collectors.toList());

        // 创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();

        try {
            // 创建工作表
            XSSFSheet sheet = workbook.createSheet("用户信息");

            // 创建表头
            String[] titles = {"账号","真实姓名","用户类型","学号/工号","用户邮箱",
                    "学校名称","学院名称","专业名称","购买教材"};
            XSSFRow headerRow = sheet.createRow(0);
            for (int i = 0; i < titles.length; i++) {
                headerRow.createCell(i).setCellValue(titles[i]);
            }

            // 填充数据
            int rowNum = 1;
            for (Long userId : ids) {
                List<DutpUser> users = dutpUserMapper.selectExportList(userId);
                if (ObjectUtil.isEmpty(users)) {
                    continue;
                }

                for (DutpUser user : users) {
                    XSSFRow dataRow = sheet.createRow(rowNum++);

                    // 安全设置单元格值
                    dataRow.createCell(0).setCellValue(ObjectUtil.defaultIfNull(user.getUserName(), ""));
                    dataRow.createCell(1).setCellValue(ObjectUtil.defaultIfNull(user.getRealName(), ""));
                    dataRow.createCell(2).setCellValue(convertUserType(user.getUserType())); // 类型转换
                    dataRow.createCell(3).setCellValue(ObjectUtil.defaultIfNull(user.getUserNo(), ""));
                    dataRow.createCell(4).setCellValue(ObjectUtil.defaultIfNull(user.getEmail(), ""));
                    dataRow.createCell(5).setCellValue(ObjectUtil.defaultIfNull(user.getSchoolName(), ""));
                    dataRow.createCell(6).setCellValue(ObjectUtil.defaultIfNull(user.getAcademyName(), ""));
                    dataRow.createCell(7).setCellValue(ObjectUtil.defaultIfNull(user.getSubjectName(), ""));
                     dataRow.createCell(8).setCellValue(user.getSchoolAdd() + user.getPersonAdd());
                }
            }

            // 设置响应头
            String fileName = "用户信息_" + System.currentTimeMillis() + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 写入数据
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();

        } catch (Exception e) {
            throw new ServiceException("导出失败: " + e.getMessage());
        } finally {
            try {
                if (workbook != null) {
                    workbook.close();
                }
            } catch (IOException e) {
                log.error("关闭工作簿失败", e);
            }
        }
    }

    // 用户类型转换示例方法
    private String convertUserType(String type) {
        if (type == null) return "";
        switch (type) {
            case "1": return "学生";
            case "2": return "教师";
            case "0": return "读者";
                default:return type;
        }
    }
}
