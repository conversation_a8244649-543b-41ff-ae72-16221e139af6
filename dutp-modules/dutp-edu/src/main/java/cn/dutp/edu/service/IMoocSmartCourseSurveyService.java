package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseSurvey;
import cn.dutp.edu.domain.MoocSmartCourseSurveyQuestion;
import cn.dutp.edu.domain.vo.SurveyQuestionDetailVo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的问卷调查Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseSurveyService extends IService<MoocSmartCourseSurvey>
{
    /**
     * 查询互动课堂的问卷调查
     *
     * @param surveyId 互动课堂的问卷调查主键
     * @return 互动课堂的问卷调查
     */
    public MoocSmartCourseSurvey selectMoocSmartCourseSurveyBySurveyId(Long surveyId);

    /**
     * 查询互动课堂的问卷调查列表
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 互动课堂的问卷调查集合
     */
    public List<MoocSmartCourseSurvey> selectMoocSmartCourseSurveyList(MoocSmartCourseSurvey moocSmartCourseSurvey);

    /**
     * 新增互动课堂的问卷调查
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 结果
     */
    public boolean insertMoocSmartCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey);

    /**
     * 修改互动课堂的问卷调查
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 结果
     */
    public boolean updateMoocSmartCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey);

    /**
     * 批量删除互动课堂的问卷调查
     *
     * @param surveyIds 需要删除的互动课堂的问卷调查主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseSurveyBySurveyIds(List<Long> surveyIds);

    /**
     * 查询互动课堂的问卷调查列表
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 互动课堂的问卷调查集合
     */
    public List<MoocSmartCourseSurvey> getCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey);

    /**
     * 获取问卷问题详情列表（包含选项和用户回答）
     *
     * @param surveyId 问卷ID
     * @return 问卷问题详情列表
     */
    public List<SurveyQuestionDetailVo> getSurveyQuestionDetails(Long surveyId);

}
