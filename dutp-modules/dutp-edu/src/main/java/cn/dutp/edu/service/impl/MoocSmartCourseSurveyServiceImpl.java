package cn.dutp.edu.service.impl;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.Collections;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.MoocSmartCourseClass;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswer;
import cn.dutp.edu.domain.MoocSmartCourseSurveyQuestion;
import cn.dutp.edu.domain.MoocSmartCourseClassMember;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import cn.dutp.edu.domain.MoocSmartCourseSurveyOption;
import cn.dutp.edu.domain.vo.SurveyQuestionDetailVo;
import cn.dutp.edu.mapper.MoocSmartCourseClassMapper;
import cn.dutp.edu.mapper.MoocSmartCourseClassMemberMapper;
import cn.dutp.edu.mapper.MoocSmartCourseSurveyAnswerMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseSurveyMapper;
import cn.dutp.edu.domain.MoocSmartCourseSurvey;
import cn.dutp.edu.service.IMoocSmartCourseSurveyService;
import cn.dutp.edu.service.IMoocSmartCourseSurveyQuestionService;
import cn.dutp.edu.service.IMoocSmartCourseSurveyOptionService;
import cn.dutp.edu.service.IMoocSmartCourseSurveyAnswerItemService;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.collection.CollUtil;

/**
 * 互动课堂的问卷调查Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseSurveyServiceImpl extends ServiceImpl<MoocSmartCourseSurveyMapper, MoocSmartCourseSurvey> implements IMoocSmartCourseSurveyService
{
    @Autowired
    private MoocSmartCourseSurveyMapper moocSmartCourseSurveyMapper;

    @Autowired
    private MoocSmartCourseSurveyAnswerMapper moocSmartCourseSurveyAnswerMapper;

    @Autowired
    private MoocSmartCourseClassMemberMapper moocSmartCourseClassMemberMapper;

    @Autowired
    private MoocSmartCourseClassMapper moocSmartCourseClassMapper;

    @Autowired
    private IMoocSmartCourseSurveyQuestionService moocSmartCourseSurveyQuestionService;

    @Autowired
    private IMoocSmartCourseSurveyOptionService moocSmartCourseSurveyOptionService;

    @Autowired
    private IMoocSmartCourseSurveyAnswerItemService moocSmartCourseSurveyAnswerItemService;

    /**
     * 查询互动课堂的问卷调查
     *
     * @param surveyId 互动课堂的问卷调查主键
     * @return 互动课堂的问卷调查
     */
    @Override
    public MoocSmartCourseSurvey selectMoocSmartCourseSurveyBySurveyId(Long surveyId)
    {
        return this.getById(surveyId);
    }

    /**
     * 查询互动课堂的问卷调查列表
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 互动课堂的问卷调查
     */
    @Override
    public List<MoocSmartCourseSurvey> selectMoocSmartCourseSurveyList(MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        LambdaQueryWrapper<MoocSmartCourseSurvey> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurvey.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurvey::getCourseId
                ,moocSmartCourseSurvey.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurvey.getCreatorId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurvey::getCreatorId
                ,moocSmartCourseSurvey.getCreatorId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurvey.getParticipationRate())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurvey::getParticipationRate
                ,moocSmartCourseSurvey.getParticipationRate());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的问卷调查
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        return this.save(moocSmartCourseSurvey);
    }

    /**
     * 修改互动课堂的问卷调查
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        return this.updateById(moocSmartCourseSurvey);
    }

    /**
     * 批量删除互动课堂的问卷调查
     *
     * @param surveyIds 需要删除的互动课堂的问卷调查主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseSurveyBySurveyIds(List<Long> surveyIds)
    {
        return this.removeByIds(surveyIds);
    }

    /**
     * 查询互动课堂的问卷调查列表
     *
     * @param moocSmartCourseSurvey 互动课堂的问卷调查
     * @return 互动课堂的问卷调查
     */
    @Override
    public List<MoocSmartCourseSurvey> getCourseSurvey(MoocSmartCourseSurvey moocSmartCourseSurvey)
    {
        // 通过classCode查询班级信息
        MoocSmartCourseClass moocSmartCourseClass = new MoocSmartCourseClass();
        moocSmartCourseClass.setUserId(SecurityUtils.getUserId());
        List<MoocSmartCourseClass> listClass = moocSmartCourseClassMemberMapper.getLearnClass(moocSmartCourseClass);
        List<Long> courseIds = listClass.stream()
                .map(MoocSmartCourseClass::getCourseId)
                .collect(Collectors.toList());
        moocSmartCourseSurvey.setCourseIds(courseIds);
        moocSmartCourseSurvey.setUserId(SecurityUtils.getUserId());
        return baseMapper.getCourseSurvey(moocSmartCourseSurvey);
    }

    @Override
    public List<SurveyQuestionDetailVo> getSurveyQuestionDetails(Long surveyId) {
        Long userId = SecurityUtils.getUserId();

        // 1. 根据surveyId查询所有问题
        MoocSmartCourseSurveyQuestion questionQuery = new MoocSmartCourseSurveyQuestion();
        questionQuery.setSurveyId(surveyId);
        List<MoocSmartCourseSurveyQuestion> questions = moocSmartCourseSurveyQuestionService.selectMoocSmartCourseSurveyQuestionList(questionQuery);

        if (CollUtil.isEmpty(questions)) {
            return Collections.emptyList();
        }

        // 2. 一次性查询所有问题的选项
        List<Long> questionIds = questions.stream().map(MoocSmartCourseSurveyQuestion::getQuestionId).collect(Collectors.toList());
        LambdaQueryWrapper<MoocSmartCourseSurveyOption> optionWrapper = new LambdaQueryWrapper<>();
        optionWrapper.in(MoocSmartCourseSurveyOption::getQuestionId, questionIds);
        List<MoocSmartCourseSurveyOption> options = moocSmartCourseSurveyOptionService.list(optionWrapper);

        // 新增：计算每个选项的选择百分比
        calculateOptionPercentages(surveyId, questions, options);

        Map<Long, List<MoocSmartCourseSurveyOption>> optionsMap = options.stream().collect(Collectors.groupingBy(MoocSmartCourseSurveyOption::getQuestionId));

        // 3. 一次性查询用户的所有回答项
        LambdaQueryWrapper<MoocSmartCourseSurveyAnswerItem> answerItemWrapper = new LambdaQueryWrapper<>();
        answerItemWrapper.eq(MoocSmartCourseSurveyAnswerItem::getSurveyId, surveyId);
        answerItemWrapper.eq(MoocSmartCourseSurveyAnswerItem::getUserId, userId);
        List<MoocSmartCourseSurveyAnswerItem> answerItems = moocSmartCourseSurveyAnswerItemService.list(answerItemWrapper);
        Map<Long, MoocSmartCourseSurveyAnswerItem> answerItemMap = answerItems.stream().collect(Collectors.toMap(MoocSmartCourseSurveyAnswerItem::getQuestionId, item -> item, (a, b) -> a));

        // 4. 组装VO
        return questions.stream().map(question -> {
            SurveyQuestionDetailVo vo = new SurveyQuestionDetailVo();
            BeanUtils.copyProperties(question, vo);
            vo.setOptions(optionsMap.getOrDefault(question.getQuestionId(), Collections.emptyList()));
            vo.setUserAnswer(answerItemMap.get(question.getQuestionId()));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 计算问卷调查中每个选项的选择百分比，并直接设置到Option对象中
     * @param surveyId 问卷ID
     * @param questions 问题列表
     * @param options 选项列表
     */
    private void calculateOptionPercentages(Long surveyId, List<MoocSmartCourseSurveyQuestion> questions, List<MoocSmartCourseSurveyOption> options) {
        // 1. 获取该问卷的所有回答记录
        LambdaQueryWrapper<MoocSmartCourseSurveyAnswerItem> allAnswerItemsWrapper = new LambdaQueryWrapper<>();
        allAnswerItemsWrapper.eq(MoocSmartCourseSurveyAnswerItem::getSurveyId, surveyId);
        List<MoocSmartCourseSurveyAnswerItem> allAnswerItems = moocSmartCourseSurveyAnswerItemService.list(allAnswerItemsWrapper);

        if (CollUtil.isEmpty(allAnswerItems)) {
            options.forEach(option -> option.setSelectionRate(0));
            return;
        }

        // 2. 按问题ID分组，统计每个问题的回答人数
        Map<Long, Long> participantsPerQuestion = allAnswerItems.stream()
                .collect(Collectors.groupingBy(MoocSmartCourseSurveyAnswerItem::getQuestionId, Collectors.counting()));

        // 3. 统计每个选项被选择的次数
        Map<Long, Long> selectionsPerOption = new java.util.HashMap<>();
        Map<Long, MoocSmartCourseSurveyQuestion> questionMap = questions.stream()
                .collect(Collectors.toMap(MoocSmartCourseSurveyQuestion::getQuestionId, q -> q));

        for (MoocSmartCourseSurveyAnswerItem item : allAnswerItems) {
            MoocSmartCourseSurveyQuestion question = questionMap.get(item.getQuestionId());
            if (question == null || ObjectUtil.isEmpty(item.getAnswerValue())) continue;

            // 根据问题类型处理: 0为单选, 1为多选.
            if (Integer.valueOf(0).equals(question.getOptionType())) { // 单选
                try {
                    Long optionId = Long.parseLong(item.getAnswerValue());
                    selectionsPerOption.merge(optionId, 1L, Long::sum);
                } catch (NumberFormatException e) {
                    // 记录日志或处理格式错误的脏数据
                }
            } else if (Integer.valueOf(1).equals(question.getOptionType())) { // 多选
                try {
                    String[] optionIds = item.getAnswerValue().split(",");
                    for (String optionIdStr : optionIds) {
                        Long optionId = Long.parseLong(optionIdStr.trim());
                        selectionsPerOption.merge(optionId, 1L, Long::sum);
                    }
                } catch (Exception e) {
                    // 记录日志或处理异常
                }
            }
        }

        // 4. 计算百分比并设置到每个Option对象中
        options.forEach(option -> {
            Long questionId = option.getQuestionId();
            Long totalParticipants = participantsPerQuestion.getOrDefault(questionId, 0L);
            Long selectionCount = selectionsPerOption.getOrDefault(option.getOptionId(), 0L);

            if (totalParticipants > 0) {
                double percentage = (double) selectionCount / totalParticipants * 100;
                option.setSelectionRate((int) Math.round(percentage));
            } else {
                option.setSelectionRate(0);
            }
        });
    }

}
