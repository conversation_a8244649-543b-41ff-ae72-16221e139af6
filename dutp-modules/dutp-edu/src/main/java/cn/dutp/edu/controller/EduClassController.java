package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.EduClass;
import cn.dutp.edu.service.IEduClassService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教务班级Controller
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@RestController
@RequestMapping("/eduClass")
public class EduClassController extends BaseController
{
    @Autowired
    private IEduClassService eduClassService;

    /**
     * 查询教务班级列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EduClass eduClass)
    {
        startPage();
        List<EduClass> list = eduClassService.selectEduClassList(eduClass);
        return getDataTable(list);
    }

    /**
     * 导出教务班级列表
     */
    @RequiresPermissions("edu:eduClass:export")
    @Log(title = "导出教务班级", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EduClass eduClass)
    {
        List<EduClass> list = eduClassService.selectEduClassList(eduClass);
        ExcelUtil<EduClass> util = new ExcelUtil<EduClass>(EduClass.class);
        util.exportExcel(response, list, "教务班级数据");
    }

    /**
     * 获取教务班级详细信息
     */
    @RequiresPermissions("edu:eduClass:query")
    @GetMapping(value = "/{eduClassId}")
    public AjaxResult getInfo(@PathVariable("eduClassId") Long eduClassId)
    {
        return success(eduClassService.selectEduClassByEduClassId(eduClassId));
    }

    /**
     * 新增教务班级
     */
    @RequiresPermissions("edu:eduClass:add")
    @Log(title = "新增教务班级", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EduClass eduClass)
    {
        return toAjax(eduClassService.insertEduClass(eduClass));
    }

    /**
     * 修改教务班级
     */
    @RequiresPermissions("edu:eduClass:edit")
    @Log(title = "修改教务班级", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EduClass eduClass)
    {
        return toAjax(eduClassService.updateEduClass(eduClass));
    }

    /**
     * 删除教务班级
     */
    @RequiresPermissions("edu:eduClass:remove")
    @Log(title = "删除教务班级", businessType = BusinessType.DELETE)
    @DeleteMapping("/{eduClassIds}")
    public AjaxResult remove(@PathVariable Long[] eduClassIds)
    {
        return toAjax(eduClassService.deleteEduClassByEduClassIds(Arrays.asList(eduClassIds)));
    }

    /**
     * 查询教务班列表(无分页 下拉框)
     */
    @GetMapping("/getEduClassList")
    public AjaxResult getEduClassList(EduClass eduClass)
    {
        return eduClassService.getEduClassList(eduClass);
    }
}
