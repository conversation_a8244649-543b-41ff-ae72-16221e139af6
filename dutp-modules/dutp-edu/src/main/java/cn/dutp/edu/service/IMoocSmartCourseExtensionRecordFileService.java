package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecordFile;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 拓展内容提交文件Service接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IMoocSmartCourseExtensionRecordFileService extends IService<MoocSmartCourseExtensionRecordFile>
{
    /**
     * 查询拓展内容提交文件
     *
     * @param fileId 拓展内容提交文件主键
     * @return 拓展内容提交文件
     */
    public MoocSmartCourseExtensionRecordFile selectMoocSmartCourseExtensionRecordFileByFileId(Long fileId);

    /**
     * 查询拓展内容提交文件列表
     *
     * @param moocSmartCourseExtensionRecordFile 拓展内容提交文件
     * @return 拓展内容提交文件集合
     */
    public List<MoocSmartCourseExtensionRecordFile> selectMoocSmartCourseExtensionRecordFileList(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile);

    /**
     * 新增拓展内容提交文件
     *
     * @param moocSmartCourseExtensionRecordFile 拓展内容提交文件
     * @return 结果
     */
    public boolean insertMoocSmartCourseExtensionRecordFile(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile);

    /**
     * 修改拓展内容提交文件
     *
     * @param moocSmartCourseExtensionRecordFile 拓展内容提交文件
     * @return 结果
     */
    public boolean updateMoocSmartCourseExtensionRecordFile(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile);

    /**
     * 批量删除拓展内容提交文件
     *
     * @param fileIds 需要删除的拓展内容提交文件主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseExtensionRecordFileByFileIds(List<Long> fileIds);

}
