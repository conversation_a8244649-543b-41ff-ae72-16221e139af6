package cn.dutp.edu.service.impl;

import java.util.List;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.EduClassMapper;
import cn.dutp.edu.domain.EduClass;
import cn.dutp.edu.service.IEduClassService;

/**
 * 教务班级Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class EduClassServiceImpl extends ServiceImpl<EduClassMapper, EduClass> implements IEduClassService
{
    @Autowired
    private EduClassMapper eduClassMapper;

    /**
     * 查询教务班级
     *
     * @param eduClassId 教务班级主键
     * @return 教务班级
     */
    @Override
    public EduClass selectEduClassByEduClassId(Long eduClassId)
    {
        return this.getById(eduClassId);
    }

    /**
     * 查询教务班级列表
     *
     * @param eduClass 教务班级
     * @return 教务班级
     */
    @Override
    public List<EduClass> selectEduClassList(EduClass eduClass)
    {
        LambdaQueryWrapper<EduClass> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(eduClass.getAcademicYearId())) {
                lambdaQueryWrapper.eq(EduClass::getAcademicYearId
                ,eduClass.getAcademicYearId());
            }

                if(ObjectUtil.isNotEmpty(eduClass.getOrganizationId())) {
                lambdaQueryWrapper.eq(EduClass::getOrganizationId
                ,eduClass.getOrganizationId());
            }
                if(ObjectUtil.isNotEmpty(eduClass.getMajorId())) {
                lambdaQueryWrapper.eq(EduClass::getMajorId
                ,eduClass.getMajorId());
            }
                if(ObjectUtil.isNotEmpty(eduClass.getClassCode())) {
                lambdaQueryWrapper.eq(EduClass::getClassCode
                ,eduClass.getClassCode());
            }
                if(ObjectUtil.isNotEmpty(eduClass.getClassName())) {
                lambdaQueryWrapper.like(EduClass::getClassName
                ,eduClass.getClassName());
            }
                if(ObjectUtil.isNotEmpty(eduClass.getRemarks())) {
                lambdaQueryWrapper.eq(EduClass::getRemarks
                ,eduClass.getRemarks());
            }
                if(ObjectUtil.isNotEmpty(eduClass.getCreatedBy())) {
                lambdaQueryWrapper.eq(EduClass::getCreatedBy
                ,eduClass.getCreatedBy());
            }
                if(ObjectUtil.isNotEmpty(eduClass.getUpdatedBy())) {
                lambdaQueryWrapper.eq(EduClass::getUpdatedBy
                ,eduClass.getUpdatedBy());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教务班级
     *
     * @param eduClass 教务班级
     * @return 结果
     */
    @Override
    public boolean insertEduClass(EduClass eduClass)
    {
        return this.save(eduClass);
    }

    /**
     * 修改教务班级
     *
     * @param eduClass 教务班级
     * @return 结果
     */
    @Override
    public boolean updateEduClass(EduClass eduClass)
    {
        return this.updateById(eduClass);
    }

    /**
     * 批量删除教务班级
     *
     * @param eduClassIds 需要删除的教务班级主键
     * @return 结果
     */
    @Override
    public boolean deleteEduClassByEduClassIds(List<Long> eduClassIds)
    {
        return this.removeByIds(eduClassIds);
    }

    /**
     * 查询教务班列表(无分页 下拉框)
     */
    @Override
    public AjaxResult getEduClassList(EduClass eduClass) {
        LambdaQueryWrapper<EduClass> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        return AjaxResult.success(this.baseMapper.selectList(lambdaQueryWrapper));
    }
}
