package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.edu.domain.MoocSmartCourseClass;
import cn.dutp.edu.domain.dto.MoocSmartCourseClassMemberScoreDto;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseClassMemberScore;
import cn.dutp.edu.service.IMoocSmartCourseClassMemberScoreService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂班级成员各项汇总成绩Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/score")
public class MoocSmartCourseClassMemberScoreController extends BaseController
{
    @Autowired
    private IMoocSmartCourseClassMemberScoreService moocSmartCourseClassMemberScoreService;

    /**
     * 查询互动课堂班级成员各项汇总成绩列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        startPage();
        List<MoocSmartCourseClassMemberScore> list = moocSmartCourseClassMemberScoreService.selectMoocSmartCourseClassMemberScoreList(moocSmartCourseClassMemberScore);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂班级成员各项汇总成绩列表
     */
    @RequiresPermissions("edu:score:export")
    @Log(title = "导出互动课堂班级成员各项汇总成绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        List<MoocSmartCourseClassMemberScore> list = moocSmartCourseClassMemberScoreService.selectMoocSmartCourseClassMemberScoreList(moocSmartCourseClassMemberScore);
        ExcelUtil<MoocSmartCourseClassMemberScore> util = new ExcelUtil<MoocSmartCourseClassMemberScore>(MoocSmartCourseClassMemberScore.class);
        util.exportExcel(response, list, "互动课堂班级成员各项汇总成绩数据");
    }

    /**
     * 获取互动课堂班级成员各项汇总成绩详细信息
     */
    @RequiresPermissions("edu:score:query")
    @GetMapping(value = "/{memberScoreId}")
    public AjaxResult getInfo(@PathVariable("memberScoreId") Long memberScoreId)
    {
        return success(moocSmartCourseClassMemberScoreService.selectMoocSmartCourseClassMemberScoreByMemberScoreId(memberScoreId));
    }

    /**
     * 新增互动课堂班级成员各项汇总成绩
     */
    @RequiresPermissions("edu:score:add")
    @Log(title = "新增互动课堂班级成员各项汇总成绩", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        return toAjax(moocSmartCourseClassMemberScoreService.insertMoocSmartCourseClassMemberScore(moocSmartCourseClassMemberScore));
    }

    /**
     * 修改互动课堂班级成员各项汇总成绩
     */
    @RequiresPermissions("edu:score:edit")
    @Log(title = "修改互动课堂班级成员各项汇总成绩", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        return toAjax(moocSmartCourseClassMemberScoreService.updateMoocSmartCourseClassMemberScore(moocSmartCourseClassMemberScore));
    }

    /**
     * 删除互动课堂班级成员各项汇总成绩
     */
    @RequiresPermissions("edu:score:remove")
    @Log(title = "删除互动课堂班级成员各项汇总成绩", businessType = BusinessType.DELETE)
    @DeleteMapping("/{memberScoreIds}")
    public AjaxResult remove(@PathVariable Long[] memberScoreIds)
    {
        return toAjax(moocSmartCourseClassMemberScoreService.deleteMoocSmartCourseClassMemberScoreByMemberScoreIds(Arrays.asList(memberScoreIds)));
    }

    /**
     * 查询班级成员的各项汇总成绩
     */
    @GetMapping("/getListByClassId")
    public TableDataInfo getListByClassId(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        startPage();
        List<MoocSmartCourseClassMemberScore> list = moocSmartCourseClassMemberScoreService.getListByClassId(moocSmartCourseClassMemberScore);
        return getDataTable(list);
    }

    /**
     * 导出查询班级成员的各项汇总成绩
     */
    @Log(title = "导出查询班级成员的各项汇总成绩", businessType = BusinessType.EXPORT)
    @PostMapping("/exportScore")
    public void exportScore(HttpServletResponse response, MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore) {
        startPage();
        List<MoocSmartCourseClassMemberScore> list = moocSmartCourseClassMemberScoreService.getListByClassId(moocSmartCourseClassMemberScore);
        // 使用 Stream API 进行转换
        List<MoocSmartCourseClassMemberScoreDto> exportList = list.stream()
                .map(source -> {
                    MoocSmartCourseClassMemberScoreDto target = new MoocSmartCourseClassMemberScoreDto();
                    BeanUtils.copyProperties(source, target);
                    return target;
                })
                .collect(Collectors.toList());
        ExcelUtil<MoocSmartCourseClassMemberScoreDto> util = new ExcelUtil<MoocSmartCourseClassMemberScoreDto>(MoocSmartCourseClassMemberScoreDto.class);
        util.exportExcel(response, exportList, "成绩管理信息");
    }
}
