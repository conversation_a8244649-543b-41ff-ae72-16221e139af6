<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseBrainstormMapper">
    
    <resultMap type="MoocSmartCourseBrainstorm" id="MoocSmartCourseBrainstormResult">
        <result property="brainstormId"    column="brainstorm_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="brainstormTitle"    column="brainstorm_title"    />
        <result property="brainstormContent"    column="brainstorm_content"    />
        <result property="brainstormCreatorId"    column="brainstorm_creator_id"    />
        <result property="brainstormRemark"    column="brainstorm_remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseBrainstormVo">
        select brainstorm_id, course_id, brainstorm_title, brainstorm_content, brainstorm_creator_id, brainstorm_remark, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_brainstorm
    </sql>

    <select id="selectMoocSmartCourseBrainstormList" parameterType="MoocSmartCourseBrainstorm" resultMap="MoocSmartCourseBrainstormResult">
        <include refid="selectMoocSmartCourseBrainstormVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="brainstormTitle != null  and brainstormTitle != ''"> and brainstorm_title = #{brainstormTitle}</if>
            <if test="brainstormContent != null  and brainstormContent != ''"> and brainstorm_content = #{brainstormContent}</if>
            <if test="brainstormCreatorId != null "> and brainstorm_creator_id = #{brainstormCreatorId}</if>
            <if test="brainstormRemark != null  and brainstormRemark != ''"> and brainstorm_remark = #{brainstormRemark}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseBrainstormByBrainstormId" parameterType="Long" resultMap="MoocSmartCourseBrainstormResult">
        <include refid="selectMoocSmartCourseBrainstormVo"/>
        where brainstorm_id = #{brainstormId}
    </select>

    <insert id="insertMoocSmartCourseBrainstorm" parameterType="MoocSmartCourseBrainstorm">
        insert into mooc_smart_course_brainstorm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="brainstormId != null">brainstorm_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="brainstormTitle != null">brainstorm_title,</if>
            <if test="brainstormContent != null">brainstorm_content,</if>
            <if test="brainstormCreatorId != null">brainstorm_creator_id,</if>
            <if test="brainstormRemark != null">brainstorm_remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="brainstormId != null">#{brainstormId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="brainstormTitle != null">#{brainstormTitle},</if>
            <if test="brainstormContent != null">#{brainstormContent},</if>
            <if test="brainstormCreatorId != null">#{brainstormCreatorId},</if>
            <if test="brainstormRemark != null">#{brainstormRemark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseBrainstorm" parameterType="MoocSmartCourseBrainstorm">
        update mooc_smart_course_brainstorm
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="brainstormTitle != null">brainstorm_title = #{brainstormTitle},</if>
            <if test="brainstormContent != null">brainstorm_content = #{brainstormContent},</if>
            <if test="brainstormCreatorId != null">brainstorm_creator_id = #{brainstormCreatorId},</if>
            <if test="brainstormRemark != null">brainstorm_remark = #{brainstormRemark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where brainstorm_id = #{brainstormId}
    </update>

    <delete id="deleteMoocSmartCourseBrainstormByBrainstormId" parameterType="Long">
        delete from mooc_smart_course_brainstorm where brainstorm_id = #{brainstormId}
    </delete>

    <delete id="deleteMoocSmartCourseBrainstormByBrainstormIds" parameterType="String">
        delete from mooc_smart_course_brainstorm where brainstorm_id in 
        <foreach item="brainstormId" collection="array" open="(" separator="," close=")">
            #{brainstormId}
        </foreach>
    </delete>
</mapper>