package cn.dutp.edu.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseUserMindmap;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
/**
 * 用户书籍思维导图数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Repository
public interface MoocSmartCourseUserMindmapMapper extends BaseMapper<MoocSmartCourseUserMindmap>
{
    /**
     * 根据用户ID和书籍ID查询用户书籍思维导图数据
     *
     * @param userId 用户ID
     * @param bookId 书籍ID
     * @return 用户书籍思维导图数据
     */
    public MoocSmartCourseUserMindmap selectByUserIdAndBookId(@Param("userId") Long userId, @Param("bookId") Long bookId);

    /**
     * 查询用户书籍思维导图数据列表
     * 
     */
}
