package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseMutualEvaluation;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂互评Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IMoocSmartCourseMutualEvaluationService extends IService<MoocSmartCourseMutualEvaluation>
{
    /**
     * 查询互动课堂互评
     *
     * @param mutualEvaluationId 互动课堂互评主键
     * @return 互动课堂互评
     */
    public MoocSmartCourseMutualEvaluation selectMoocSmartCourseMutualEvaluationByMutualEvaluationId(Long mutualEvaluationId);

    /**
     * 查询互动课堂互评列表
     *
     * @param moocSmartCourseMutualEvaluation 互动课堂互评
     * @return 互动课堂互评集合
     */
    public List<MoocSmartCourseMutualEvaluation> selectMoocSmartCourseMutualEvaluationList(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation);

    /**
     * 新增互动课堂互评
     *
     * @param moocSmartCourseMutualEvaluation 互动课堂互评
     * @return 结果
     */
    public boolean insertMoocSmartCourseMutualEvaluation(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation);

    /**
     * 修改互动课堂互评
     *
     * @param moocSmartCourseMutualEvaluation 互动课堂互评
     * @return 结果
     */
    public boolean updateMoocSmartCourseMutualEvaluation(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation);

    /**
     * 批量删除互动课堂互评
     *
     * @param mutualEvaluationIds 需要删除的互动课堂互评主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseMutualEvaluationByMutualEvaluationIds(List<Long> mutualEvaluationIds);

}
