package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.EduClassMember;
import cn.dutp.edu.service.IEduClassMemberService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 教务班级成员Controller
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@RestController
@RequestMapping("/edu/eduMember")
public class EduClassMemberController extends BaseController
{
    @Autowired
    private IEduClassMemberService eduClassMemberService;

    /**
     * 查询教务班级成员列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EduClassMember eduClassMember)
    {
        startPage();
        List<EduClassMember> list = eduClassMemberService.selectEduClassMemberList(eduClassMember);
        return getDataTable(list);
    }

    /**
     * 导出教务班级成员列表
     */
    @RequiresPermissions("edu:eduMember:export")
    @Log(title = "导出教务班级成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EduClassMember eduClassMember)
    {
        List<EduClassMember> list = eduClassMemberService.selectEduClassMemberList(eduClassMember);
        ExcelUtil<EduClassMember> util = new ExcelUtil<EduClassMember>(EduClassMember.class);
        util.exportExcel(response, list, "教务班级成员数据");
    }

    /**
     * 获取教务班级成员详细信息
     */
    @RequiresPermissions("edu:eduMember:query")
    @GetMapping(value = "/{eduClassMemberId}")
    public AjaxResult getInfo(@PathVariable("eduClassMemberId") Long eduClassMemberId)
    {
        return success(eduClassMemberService.selectEduClassMemberByEduClassMemberId(eduClassMemberId));
    }

    /**
     * 新增教务班级成员
     */
    @RequiresPermissions("edu:eduMember:add")
    @Log(title = "新增教务班级成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EduClassMember eduClassMember)
    {
        return toAjax(eduClassMemberService.insertEduClassMember(eduClassMember));
    }

    /**
     * 修改教务班级成员
     */
    @RequiresPermissions("edu:eduMember:edit")
    @Log(title = "修改教务班级成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EduClassMember eduClassMember)
    {
        return toAjax(eduClassMemberService.updateEduClassMember(eduClassMember));
    }

    /**
     * 删除教务班级成员
     */
    @RequiresPermissions("edu:eduMember:remove")
    @Log(title = "删除教务班级成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{eduClassMemberIds}")
    public AjaxResult remove(@PathVariable Long[] eduClassMemberIds)
    {
        return toAjax(eduClassMemberService.deleteEduClassMemberByEduClassMemberIds(Arrays.asList(eduClassMemberIds)));
    }
}
