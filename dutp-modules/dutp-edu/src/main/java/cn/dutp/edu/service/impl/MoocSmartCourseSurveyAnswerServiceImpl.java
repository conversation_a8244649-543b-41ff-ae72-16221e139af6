package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseSurveyAnswerMapper;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswer;
import cn.dutp.edu.service.IMoocSmartCourseSurveyAnswerService;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import cn.dutp.edu.service.IMoocSmartCourseSurveyAnswerItemService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 互动课堂的问卷调查回答Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class MoocSmartCourseSurveyAnswerServiceImpl extends ServiceImpl<MoocSmartCourseSurveyAnswerMapper, MoocSmartCourseSurveyAnswer> implements IMoocSmartCourseSurveyAnswerService
{
    @Autowired
    private MoocSmartCourseSurveyAnswerMapper moocSmartCourseSurveyAnswerMapper;

    @Autowired
    private IMoocSmartCourseSurveyAnswerItemService moocSmartCourseSurveyAnswerItemService;

    /**
     * 查询互动课堂的问卷调查回答
     *
     * @param answerId 互动课堂的问卷调查回答主键
     * @return 互动课堂的问卷调查回答
     */
    @Override
    public MoocSmartCourseSurveyAnswer selectMoocSmartCourseSurveyAnswerByAnswerId(Long answerId)
    {
        return this.getById(answerId);
    }

    /**
     * 查询互动课堂的问卷调查回答列表
     *
     * @param moocSmartCourseSurveyAnswer 互动课堂的问卷调查回答
     * @return 互动课堂的问卷调查回答
     */
    @Override
    public List<MoocSmartCourseSurveyAnswer> selectMoocSmartCourseSurveyAnswerList(MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        LambdaQueryWrapper<MoocSmartCourseSurveyAnswer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswer.getSurveyId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswer::getSurveyId
                ,moocSmartCourseSurveyAnswer.getSurveyId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswer.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswer::getCourseId
                ,moocSmartCourseSurveyAnswer.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswer.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswer::getUserId
                ,moocSmartCourseSurveyAnswer.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswer.getAnswerContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswer::getAnswerContent
                ,moocSmartCourseSurveyAnswer.getAnswerContent());
            }
        List<MoocSmartCourseSurveyAnswer> list = this.list(lambdaQueryWrapper);
        list.forEach(answer -> {
            List<MoocSmartCourseSurveyAnswerItem> answerItems = moocSmartCourseSurveyAnswerItemService.list(new LambdaQueryWrapper<MoocSmartCourseSurveyAnswerItem>()
                    .eq(MoocSmartCourseSurveyAnswerItem::getAnswerId, answer.getAnswerId()));
            answer.setAnswerItems(answerItems);
        });
        return list;
    }

    /**
     * 新增互动课堂的问卷调查回答
     *
     * @param moocSmartCourseSurveyAnswer 互动课堂的问卷调查回答
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertMoocSmartCourseSurveyAnswer(MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        boolean result = this.save(moocSmartCourseSurveyAnswer);
        if (result) {
            List<MoocSmartCourseSurveyAnswerItem> answerItems = moocSmartCourseSurveyAnswer.getAnswerItems();
            if (ObjectUtil.isNotEmpty(answerItems)) {
                Long answerId = moocSmartCourseSurveyAnswer.getAnswerId();
                answerItems.forEach(item -> {
                    item.setAnswerId(answerId);
                });
                moocSmartCourseSurveyAnswerItemService.saveBatch(answerItems);
            }
        }
        return result;
    }

    /**
     * 修改互动课堂的问卷调查回答
     *
     * @param moocSmartCourseSurveyAnswer 互动课堂的问卷调查回答
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseSurveyAnswer(MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        return this.updateById(moocSmartCourseSurveyAnswer);
    }

    /**
     * 批量删除互动课堂的问卷调查回答
     *
     * @param answerIds 需要删除的互动课堂的问卷调查回答主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseSurveyAnswerByAnswerIds(List<Long> answerIds)
    {
        return this.removeByIds(answerIds);
    }

}
