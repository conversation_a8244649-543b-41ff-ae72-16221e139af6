package cn.dutp.edu.service.impl;

import java.util.Collections;
import java.util.List;

import cn.dutp.edu.domain.MoocSmartCourseClass;
import cn.dutp.edu.mapper.MoocSmartCourseClassMapper;
import cn.dutp.edu.service.IMoocSmartCourseClassService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 互动课堂班级Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseClassServiceImpl extends ServiceImpl<MoocSmartCourseClassMapper, MoocSmartCourseClass> implements IMoocSmartCourseClassService
{
    @Autowired
    private MoocSmartCourseClassMapper moocSmartCourseClassMapper;

    /**
     * 查询互动课堂班级
     *
     * @param classId 互动课堂班级主键
     * @return 互动课堂班级
     */
    @Override
    public MoocSmartCourseClass selectMoocSmartCourseClassByClassId(Long classId)
    {
        return this.getById(classId);
    }

    /**
     * 查询互动课堂班级列表
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 互动课堂班级
     */
    @Override
    public List<MoocSmartCourseClass> selectMoocSmartCourseClassList(MoocSmartCourseClass moocSmartCourseClass)
    {
        LambdaQueryWrapper<MoocSmartCourseClass> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getCourseId
                ,moocSmartCourseClass.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassName())) {
                lambdaQueryWrapper.like(MoocSmartCourseClass::getClassName
                ,moocSmartCourseClass.getClassName());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassCode())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getClassCode
                ,moocSmartCourseClass.getClassCode());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getClassType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getClassType
                ,moocSmartCourseClass.getClassType());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getUserId
                ,moocSmartCourseClass.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getBookId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getBookId
                ,moocSmartCourseClass.getBookId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getDescription())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getDescription
                ,moocSmartCourseClass.getDescription());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCoverImageUrl())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getCoverImageUrl
                ,moocSmartCourseClass.getCoverImageUrl());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getStatus())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getStatus
                ,moocSmartCourseClass.getStatus());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getEduClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getEduClassId
                ,moocSmartCourseClass.getEduClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getCreateBy())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getCreateBy
                ,moocSmartCourseClass.getCreateBy());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClass.getUpdateBy())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClass::getUpdateBy
                ,moocSmartCourseClass.getUpdateBy());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseClass(MoocSmartCourseClass moocSmartCourseClass)
    {
        return this.save(moocSmartCourseClass);
    }

    /**
     * 修改互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseClass(MoocSmartCourseClass moocSmartCourseClass)
    {
        return this.updateById(moocSmartCourseClass);
    }

    /**
     * 批量删除互动课堂班级
     *
     * @param classIds 需要删除的互动课堂班级主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseClassByClassIds(List<Long> classIds)
    {
        return this.removeByIds(classIds);
    }

    /**
     * 查询互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 互动课堂班级
     */
    @Override
    public MoocSmartCourseClass getInfo(MoocSmartCourseClass moocSmartCourseClass)
    {
        return baseMapper.getInfo(moocSmartCourseClass);
    }

    @Override
    public List<MoocSmartCourseClass> getListBySchool(MoocSmartCourseClass moocSmartCourseClass) {
        return moocSmartCourseClassMapper.getListBySchool(moocSmartCourseClass);
    }

    @Override
    public MoocSmartCourseClass getInfoByClassId(MoocSmartCourseClass moocSmartCourseClass) {
        return moocSmartCourseClassMapper.getInfoByClassId(moocSmartCourseClass);
    }
}
