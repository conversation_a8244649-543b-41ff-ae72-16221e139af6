package cn.dutp.edu.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.vo.EduStudentVo;
import cn.dutp.edu.domian.DutpUser;
import cn.dutp.edu.service.IEduStudentService;
import cn.dutp.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @author: dutp
 * @date: 2025/7/21 9:48
 */
@RestController
@RequestMapping("eduStudent")
public class EduStudentController extends BaseController {

    @Autowired
    private IEduStudentService eduStudentService;


    /**
     * 查询当前学校下的学生
     * @param student
     * @return
     */
    @GetMapping("list")
    public TableDataInfo selStudentListInSchool(EduStudentVo student) {
        startPage();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long schoolId = loginUser.getSysUser().getSchoolId();
        student.setSchoolId(schoolId);
        return getDataTable(eduStudentService.studentListInSchool(student));
    }


    /**
     * 查询当前班级下的学生
     * @param student
     * @return
     */
    @GetMapping("listClass")
    public TableDataInfo selStudentListInClass(EduStudentVo student) {
        startPage();
        return getDataTable(eduStudentService.studentListInClass(student));
    }

    /**
     * 班级加入学生
     * @param student
     * @return
     */
    @PostMapping("/addStudent")
    @Log(title = "添加学生", businessType = BusinessType.INSERT)
    public AjaxResult addStudent(@RequestBody DutpUser student) {
        return toAjax(eduStudentService.addStudentInfo(student));
    }
}
