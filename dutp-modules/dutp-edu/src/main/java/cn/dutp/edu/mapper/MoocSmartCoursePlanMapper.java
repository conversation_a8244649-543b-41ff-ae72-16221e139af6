package cn.dutp.edu.mapper;

import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.MoocSmartCoursePlan;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 教务课程Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Repository
public interface MoocSmartCoursePlanMapper extends BaseMapper<MoocSmartCoursePlan>
{

    List<MoocSmartCoursePlan> selectMoocSmartCoursePlanList(MoocSmartCoursePlan moocSmartCoursePlan);
    List<MoocSmartCoursePlan> selectPlanListNoPage();

    List<DutpSchool> getSchoolByCoursePlan(Long schoolId);
}
