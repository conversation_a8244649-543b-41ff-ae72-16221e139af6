package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkOrTest;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkOrTestWithPaperVO;
/**
 * 互动课堂作业/考试管理Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseHomeworkOrTestService extends IService<MoocSmartCourseHomeworkOrTest>
{
    /**
     * 查询互动课堂作业/考试管理
     *
     * @param assignmentId 互动课堂作业/考试管理主键
     * @return 互动课堂作业/考试管理
     */
    public MoocSmartCourseHomeworkOrTest selectMoocSmartCourseHomeworkOrTestByAssignmentId(Long assignmentId);

    /**
     * 根据作业/考试ID查询作业/考试及试卷信息
     *
     * @param assignmentId 互动课堂作业/考试管理主键
     * @return 互动课堂作业/考试管理及试卷信息
     */
    public MoocSmartCourseHomeworkOrTestWithPaperVO selectHomeworkWithPaperByHomeworkId(Long assignmentId);

    /**
     * 查询互动课堂作业/考试管理列表
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 互动课堂作业/考试管理集合
     */
    public List<MoocSmartCourseHomeworkOrTest> selectMoocSmartCourseHomeworkOrTestList(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest);

    /**
     * 新增互动课堂作业/考试管理
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 结果
     */
    public boolean insertMoocSmartCourseHomeworkOrTest(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest);

    /**
     * 修改互动课堂作业/考试管理
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 结果
     */
    public boolean updateMoocSmartCourseHomeworkOrTest(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest);

    /**
     * 批量删除互动课堂作业/考试管理
     *
     * @param assignmentIds 需要删除的互动课堂作业/考试管理主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseHomeworkOrTestByAssignmentIds(List<Long> assignmentIds);

    /**
     * 查询互动课堂作业/考试管理列表
     *
     * @param moocSmartCourseHomeworkOrTest 互动课堂作业/考试管理
     * @return 互动课堂作业/考试管理集合
     */
    public List<MoocSmartCourseHomeworkOrTest> getHomeworkOrTestList(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest);
}
