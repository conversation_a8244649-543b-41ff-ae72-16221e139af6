package cn.dutp.edu.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseTestPaperAnswer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 互动课堂学生考试/作业结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseTestPaperAnswerMapper extends BaseMapper<MoocSmartCourseTestPaperAnswer>
{

    List<MoocSmartCourseTestPaperAnswer> getUserAnswerByAssignmentId(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer);
}
