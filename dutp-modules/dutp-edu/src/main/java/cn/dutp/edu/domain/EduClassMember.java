package cn.dutp.edu.domain;

    import java.util.Date;

    import com.baomidou.mybatisplus.annotation.TableField;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教务班级成员对象 edu_class_member
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@TableName("edu_class_member")
public class EduClassMember extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 班级成员ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long eduClassMemberId;

    /** 教务班级ID (外键, 关联 edu_class) */
        @Excel(name = "教务班级ID (外键, 关联 edu_class)")
    private Long eduClassId;

    /** 用户ID (外键, 关联统一用户表, 包含学生和教师信息) */
        @Excel(name = "用户ID (外键, 关联统一用户表, 包含学生和教师信息)")
    private Long userId;

    /** 负责教师 */
        @Excel(name = "负责教师")
    private Long teacherUserId;

    /** 加入班级日期 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "加入班级日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date joinTime;

    /** 创建者用户标识 */
        @Excel(name = "创建者用户标识")
    private String createdBy;

    /** 最后更新者用户标识 */
        @Excel(name = "最后更新者用户标识")
    private String updatedBy;

    /** 删除标志 (0: 存在, 2: 删除) */
    private String delFlag;

    @TableField(exist = false)
    private String createBy;

    @TableField(exist = false)
    private String updateBy;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("eduClassMemberId", getEduClassMemberId())
            .append("eduClassId", getEduClassId())
            .append("userId", getUserId())
            .append("teacherUserId", getTeacherUserId())
            .append("joinTime", getJoinTime())
            .append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
        }
