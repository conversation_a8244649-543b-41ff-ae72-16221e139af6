package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseBrainstormReply;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂的头脑风暴讨论成员Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseBrainstormReplyService extends IService<MoocSmartCourseBrainstormReply>
{
    /**
     * 查询互动课堂的头脑风暴讨论成员
     *
     * @param replyId 互动课堂的头脑风暴讨论成员主键
     * @return 互动课堂的头脑风暴讨论成员
     */
    public MoocSmartCourseBrainstormReply selectMoocSmartCourseBrainstormReplyByReplyId(Long replyId);

    /**
     * 查询互动课堂的头脑风暴讨论成员列表
     *
     * @param moocSmartCourseBrainstormReply 互动课堂的头脑风暴讨论成员
     * @return 互动课堂的头脑风暴讨论成员集合
     */
    public List<MoocSmartCourseBrainstormReply> selectMoocSmartCourseBrainstormReplyList(MoocSmartCourseBrainstormReply moocSmartCourseBrainstormReply);

    /**
     * 新增互动课堂的头脑风暴讨论成员
     *
     * @param moocSmartCourseBrainstormReply 互动课堂的头脑风暴讨论成员
     * @return 结果
     */
    public boolean insertMoocSmartCourseBrainstormReply(MoocSmartCourseBrainstormReply moocSmartCourseBrainstormReply);

    /**
     * 修改互动课堂的头脑风暴讨论成员
     *
     * @param moocSmartCourseBrainstormReply 互动课堂的头脑风暴讨论成员
     * @return 结果
     */
    public boolean updateMoocSmartCourseBrainstormReply(MoocSmartCourseBrainstormReply moocSmartCourseBrainstormReply);

    /**
     * 批量删除互动课堂的头脑风暴讨论成员
     *
     * @param replyIds 需要删除的互动课堂的头脑风暴讨论成员主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseBrainstormReplyByReplyIds(List<Long> replyIds);

}
