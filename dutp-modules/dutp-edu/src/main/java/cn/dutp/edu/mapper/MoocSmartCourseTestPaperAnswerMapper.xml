<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseTestPaperAnswerMapper">
    
    <resultMap type="MoocSmartCourseTestPaperAnswer" id="MoocSmartCourseTestPaperAnswerResult">
        <result property="answertId"    column="answert_id"    />
        <result property="assignmentId"    column="assignment_id"    />
        <result property="homeworkShowId"    column="homework_show_id"    />
        <result property="userId"    column="user_id"    />
        <result property="answerContent"    column="answer_content"    />
        <result property="score"    column="score"    />
        <result property="answerType"    column="answer_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseTestPaperAnswerVo">
        select answert_id, assignment_id, homework_show_id, user_id, answer_content, score, answer_type, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_test_paper_answer
    </sql>

    <select id="selectMoocSmartCourseTestPaperAnswerList" parameterType="MoocSmartCourseTestPaperAnswer" resultMap="MoocSmartCourseTestPaperAnswerResult">
        <include refid="selectMoocSmartCourseTestPaperAnswerVo"/>
        <where>  
            <if test="assignmentId != null "> and assignment_id = #{assignmentId}</if>
            <if test="homeworkShowId != null "> and homework_show_id = #{homeworkShowId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="answerContent != null  and answerContent != ''"> and answer_content = #{answerContent}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="answerType != null  and answerType != ''"> and answer_type = #{answerType}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseTestPaperAnswerByAnswertId" parameterType="Long" resultMap="MoocSmartCourseTestPaperAnswerResult">
        <include refid="selectMoocSmartCourseTestPaperAnswerVo"/>
        where answert_id = #{answertId}
    </select>

    <insert id="insertMoocSmartCourseTestPaperAnswer" parameterType="MoocSmartCourseTestPaperAnswer">
        insert into mooc_smart_course_test_paper_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="answertId != null">answert_id,</if>
            <if test="assignmentId != null">assignment_id,</if>
            <if test="homeworkShowId != null">homework_show_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="answerContent != null">answer_content,</if>
            <if test="score != null">score,</if>
            <if test="answerType != null">answer_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="answertId != null">#{answertId},</if>
            <if test="assignmentId != null">#{assignmentId},</if>
            <if test="homeworkShowId != null">#{homeworkShowId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="answerContent != null">#{answerContent},</if>
            <if test="score != null">#{score},</if>
            <if test="answerType != null">#{answerType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseTestPaperAnswer" parameterType="MoocSmartCourseTestPaperAnswer">
        update mooc_smart_course_test_paper_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="assignmentId != null">assignment_id = #{assignmentId},</if>
            <if test="homeworkShowId != null">homework_show_id = #{homeworkShowId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="answerContent != null">answer_content = #{answerContent},</if>
            <if test="score != null">score = #{score},</if>
            <if test="answerType != null">answer_type = #{answerType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where answert_id = #{answertId}
    </update>

    <delete id="deleteMoocSmartCourseTestPaperAnswerByAnswertId" parameterType="Long">
        delete from mooc_smart_course_test_paper_answer where answert_id = #{answertId}
    </delete>

    <delete id="deleteMoocSmartCourseTestPaperAnswerByAnswertIds" parameterType="String">
        delete from mooc_smart_course_test_paper_answer where answert_id in 
        <foreach item="answertId" collection="array" open="(" separator="," close=")">
            #{answertId}
        </foreach>
    </delete>

    <select id="getUserAnswerByAssignmentId" parameterType="cn.dutp.edu.domain.MoocSmartCourseTestPaperAnswer" resultType="cn.dutp.edu.domain.MoocSmartCourseTestPaperAnswer">
        select
            du.real_name,
            du.user_no,
            pa.score
        from
            mooc_smart_course_class_member cm
        left join
            mooc_smart_course_test_paper_answer pa on pa.user_id = cm.user_id
        left join
            dutp_user du on du.user_id = cm.user_id and du.del_flag = 0 and du.user_type = 1
        where
            cm.del_flag = 0 and pa.assignment_id = #{assignmentId}
            <if test="realName != null and realName != ''">
                AND du.real_name LIKE CONCAT('%', #{realName}, '%') or du.user_no LIKE CONCAT('%', #{realName}, '%')
            </if>
    </select>
</mapper>