<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseMutualEvaluationMapper">
    
    <resultMap type="MoocSmartCourseMutualEvaluation" id="MoocSmartCourseMutualEvaluationResult">
        <result property="mutualEvaluationId"    column="mutual_evaluation_id"    />
        <result property="assignmentId"    column="assignment_id"    />
        <result property="testPaperId"    column="test_paper_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="classId"    column="class_id"    />
        <result property="gradingMethod"    column="grading_method"    />
        <result property="fromEvaluationId"    column="from_evaluation_id"    />
        <result property="toEvaluationId"    column="to_evaluation_id"    />
        <result property="score"    column="score"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseMutualEvaluationVo">
        select mutual_evaluation_id, assignment_id, test_paper_id, course_id, class_id, grading_method, from_evaluation_id, to_evaluation_id, score, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_mutual_evaluation
    </sql>

    <select id="selectMoocSmartCourseMutualEvaluationList" parameterType="MoocSmartCourseMutualEvaluation" resultMap="MoocSmartCourseMutualEvaluationResult">
        <include refid="selectMoocSmartCourseMutualEvaluationVo"/>
        <where>  
            <if test="assignmentId != null "> and assignment_id = #{assignmentId}</if>
            <if test="testPaperId != null "> and test_paper_id = #{testPaperId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="gradingMethod != null  and gradingMethod != ''"> and grading_method = #{gradingMethod}</if>
            <if test="fromEvaluationId != null "> and from_evaluation_id = #{fromEvaluationId}</if>
            <if test="toEvaluationId != null "> and to_evaluation_id = #{toEvaluationId}</if>
            <if test="score != null "> and score = #{score}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseMutualEvaluationByMutualEvaluationId" parameterType="Long" resultMap="MoocSmartCourseMutualEvaluationResult">
        <include refid="selectMoocSmartCourseMutualEvaluationVo"/>
        where mutual_evaluation_id = #{mutualEvaluationId}
    </select>

    <insert id="insertMoocSmartCourseMutualEvaluation" parameterType="MoocSmartCourseMutualEvaluation">
        insert into mooc_smart_course_mutual_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mutualEvaluationId != null">mutual_evaluation_id,</if>
            <if test="assignmentId != null">assignment_id,</if>
            <if test="testPaperId != null">test_paper_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="gradingMethod != null">grading_method,</if>
            <if test="fromEvaluationId != null">from_evaluation_id,</if>
            <if test="toEvaluationId != null">to_evaluation_id,</if>
            <if test="score != null">score,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mutualEvaluationId != null">#{mutualEvaluationId},</if>
            <if test="assignmentId != null">#{assignmentId},</if>
            <if test="testPaperId != null">#{testPaperId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="gradingMethod != null">#{gradingMethod},</if>
            <if test="fromEvaluationId != null">#{fromEvaluationId},</if>
            <if test="toEvaluationId != null">#{toEvaluationId},</if>
            <if test="score != null">#{score},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseMutualEvaluation" parameterType="MoocSmartCourseMutualEvaluation">
        update mooc_smart_course_mutual_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="assignmentId != null">assignment_id = #{assignmentId},</if>
            <if test="testPaperId != null">test_paper_id = #{testPaperId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="gradingMethod != null">grading_method = #{gradingMethod},</if>
            <if test="fromEvaluationId != null">from_evaluation_id = #{fromEvaluationId},</if>
            <if test="toEvaluationId != null">to_evaluation_id = #{toEvaluationId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where mutual_evaluation_id = #{mutualEvaluationId}
    </update>

    <delete id="deleteMoocSmartCourseMutualEvaluationByMutualEvaluationId" parameterType="Long">
        delete from mooc_smart_course_mutual_evaluation where mutual_evaluation_id = #{mutualEvaluationId}
    </delete>

    <delete id="deleteMoocSmartCourseMutualEvaluationByMutualEvaluationIds" parameterType="String">
        delete from mooc_smart_course_mutual_evaluation where mutual_evaluation_id in 
        <foreach item="mutualEvaluationId" collection="array" open="(" separator="," close=")">
            #{mutualEvaluationId}
        </foreach>
    </delete>
</mapper>