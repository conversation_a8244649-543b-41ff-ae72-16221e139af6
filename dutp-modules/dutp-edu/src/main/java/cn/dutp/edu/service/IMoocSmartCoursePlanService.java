package cn.dutp.edu.service;

import java.util.List;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.MoocSmartCoursePlan;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教务课程Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IMoocSmartCoursePlanService extends IService<MoocSmartCoursePlan>
{

    /**
     * 查询教务课程列表
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 教务课程集合
     */
    public List<MoocSmartCoursePlan> selectMoocSmartCoursePlanList(MoocSmartCoursePlan moocSmartCoursePlan);

    /**
     * 新增教务课程
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 结果
     */
    public boolean insertMoocSmartCoursePlan(MoocSmartCoursePlan moocSmartCoursePlan);

    /**
     * 修改教务课程
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 结果
     */
    public boolean updateMoocSmartCoursePlan(MoocSmartCoursePlan moocSmartCoursePlan);

    /**
     * 批量删除教务课程
     *
     * @param courseIds 需要删除的教务课程主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCoursePlanByCourseIds(List<Long> courseIds);

    /**
     * 查询教务课程
     *
     * @param courseId 教务课程主键
     * @return 教务课程
     */
    public MoocSmartCoursePlan selectMoocSmartCoursePlanByCourseId(Long courseId);

    public List<DutpSchool> getSchoolByCoursePlan();

    String importCoursePlan(List<MoocSmartCoursePlan> moocSmartCoursePlanList);

    /**
     * 查询教务课程列表
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 教务课程集合
     */
    public AjaxResult selectPlanListNoPage();
}
