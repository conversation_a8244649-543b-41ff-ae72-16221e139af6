package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseExtensionRecordMapper;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecord;
import cn.dutp.edu.service.IMoocSmartCourseExtensionRecordService;
import cn.dutp.edu.service.IMoocSmartCourseExtensionRecordFileService;
import org.springframework.transaction.annotation.Transactional;
import cn.dutp.common.security.utils.SecurityUtils;

/**
 * 互动课堂的拓展内容学生记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseExtensionRecordServiceImpl extends ServiceImpl<MoocSmartCourseExtensionRecordMapper, MoocSmartCourseExtensionRecord> implements IMoocSmartCourseExtensionRecordService
{
    @Autowired
    private MoocSmartCourseExtensionRecordMapper moocSmartCourseExtensionRecordMapper;

    @Autowired
    private IMoocSmartCourseExtensionRecordFileService moocSmartCourseExtensionRecordFileService;

    /**
     * 查询互动课堂的拓展内容学生记录
     *
     * @param recordId 互动课堂的拓展内容学生记录主键
     * @return 互动课堂的拓展内容学生记录
     */
    @Override
    public MoocSmartCourseExtensionRecord selectMoocSmartCourseExtensionRecordByRecordId(Long recordId)
    {
        return this.getById(recordId);
    }

    /**
     * 查询互动课堂的拓展内容学生记录列表
     *
     * @param moocSmartCourseExtensionRecord 互动课堂的拓展内容学生记录
     * @return 互动课堂的拓展内容学生记录
     */
    @Override
    public List<MoocSmartCourseExtensionRecord> selectMoocSmartCourseExtensionRecordList(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
    {
        LambdaQueryWrapper<MoocSmartCourseExtensionRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecord.getStudentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecord::getStudentId
                ,moocSmartCourseExtensionRecord.getStudentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecord.getQuestionContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecord::getQuestionContent
                ,moocSmartCourseExtensionRecord.getQuestionContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecord.getSubmitTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecord::getSubmitTime
                ,moocSmartCourseExtensionRecord.getSubmitTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public MoocSmartCourseExtensionRecord getRecordByExtensionId(Long extensionId, Long userId) {
        LambdaQueryWrapper<MoocSmartCourseExtensionRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecord::getExtensionId, extensionId);
        lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecord::getStudentId, userId);
        MoocSmartCourseExtensionRecord record = this.getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotNull(record)) {
            record.setFiles(moocSmartCourseExtensionRecordFileService.list(new LambdaQueryWrapper<cn.dutp.edu.domain.MoocSmartCourseExtensionRecordFile>().eq(cn.dutp.edu.domain.MoocSmartCourseExtensionRecordFile::getRecordId, record.getRecordId())));
        }
        return record;
    }

    /**
     * 新增互动课堂的拓展内容学生记录
     *
     * @param moocSmartCourseExtensionRecord 互动课堂的拓展内容学生记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertMoocSmartCourseExtensionRecord(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
    {
        boolean result = this.save(moocSmartCourseExtensionRecord);
        if (result && ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecord.getFiles())) {
            moocSmartCourseExtensionRecord.getFiles().forEach(file -> file.setRecordId(moocSmartCourseExtensionRecord.getRecordId()));
            moocSmartCourseExtensionRecordFileService.saveBatch(moocSmartCourseExtensionRecord.getFiles());
        }
        return result;
    }

    /**
     * 修改互动课堂的拓展内容学生记录
     *
     * @param moocSmartCourseExtensionRecord 互动课堂的拓展内容学生记录
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseExtensionRecord(MoocSmartCourseExtensionRecord moocSmartCourseExtensionRecord)
    {
        return this.updateById(moocSmartCourseExtensionRecord);
    }

    /**
     * 批量删除互动课堂的拓展内容学生记录
     *
     * @param recordIds 需要删除的互动课堂的拓展内容学生记录主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseExtensionRecordByRecordIds(List<Long> recordIds)
    {
        return this.removeByIds(recordIds);
    }

}
