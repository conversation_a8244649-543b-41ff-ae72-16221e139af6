package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation;
import cn.dutp.edu.service.IMoocSmartCourseLessonEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation;
import cn.dutp.edu.service.IMoocSmartCourseLessonEvaluationService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 课程评价Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/lessonEvaluation")
public class MoocSmartCourseLessonEvaluationController extends BaseController
{
    @Autowired
    private IMoocSmartCourseLessonEvaluationService moocSmartCourseLessonEvaluationService;

    /**
     * 查询课程评价列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        startPage();
        List<MoocSmartCourseLessonEvaluation> list = moocSmartCourseLessonEvaluationService.selectMoocSmartCourseLessonEvaluationList(moocSmartCourseLessonEvaluation);
        return getDataTable(list);
    }


    /**
     * 查询课程评价列表
     */
    @GetMapping("/getMine")
    public MoocSmartCourseLessonEvaluation getStudentLessonEvaluation(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {

        moocSmartCourseLessonEvaluation.setStudentId(SecurityUtils.getUserId());
        List<MoocSmartCourseLessonEvaluation> list = moocSmartCourseLessonEvaluationService.selectMoocSmartCourseLessonEvaluationList(moocSmartCourseLessonEvaluation);
        return list.isEmpty() ?null:list.get(0);
    }

    /**
     * 导出课程评价列表
     */
    @Log(title = "导出课程评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        List<MoocSmartCourseLessonEvaluation> list = moocSmartCourseLessonEvaluationService.selectMoocSmartCourseLessonEvaluationList(moocSmartCourseLessonEvaluation);
        ExcelUtil<MoocSmartCourseLessonEvaluation> util = new ExcelUtil<MoocSmartCourseLessonEvaluation>(MoocSmartCourseLessonEvaluation.class);
        util.exportExcel(response, list, "课程评价数据");
    }

    /**
     * 获取课程评价详细信息
     */
    @GetMapping(value = "/{evaluationId}")
    public AjaxResult getInfo(@PathVariable("evaluationId") Long evaluationId)
    {
        return success(moocSmartCourseLessonEvaluationService.selectMoocSmartCourseLessonEvaluationByEvaluationId(evaluationId));
    }

    /**
     * 新增课程评价
     */
    @Log(title = "新增课程评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        moocSmartCourseLessonEvaluation.setStudentId(SecurityUtils.getUserId());
        return toAjax(moocSmartCourseLessonEvaluationService.insertMoocSmartCourseLessonEvaluation(moocSmartCourseLessonEvaluation));
    }

    /**
     * 修改课程评价
     */
    @Log(title = "修改课程评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        return toAjax(moocSmartCourseLessonEvaluationService.updateMoocSmartCourseLessonEvaluation(moocSmartCourseLessonEvaluation));
    }

    /**
     * 删除课程评价
     */
    @Log(title = "删除课程评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{evaluationIds}")
    public AjaxResult remove(@PathVariable Long[] evaluationIds)
    {
        return toAjax(moocSmartCourseLessonEvaluationService.deleteMoocSmartCourseLessonEvaluationByEvaluationIds(Arrays.asList(evaluationIds)));
    }

    /**
     * 查询课程评价列表
     */
    @GetMapping("/getByClassId")
    public TableDataInfo getByClassId(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation)
    {
        startPage();
        List<MoocSmartCourseLessonEvaluation> list = moocSmartCourseLessonEvaluationService.getByClassId(moocSmartCourseLessonEvaluation);
        return getDataTable(list);
    }
}
