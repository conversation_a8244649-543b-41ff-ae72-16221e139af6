package cn.dutp.edu.service.impl;

import java.util.Collections;
import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseClassMemberScoreMapper;
import cn.dutp.edu.domain.MoocSmartCourseClassMemberScore;
import cn.dutp.edu.service.IMoocSmartCourseClassMemberScoreService;

/**
 * 互动课堂班级成员各项汇总成绩Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseClassMemberScoreServiceImpl extends ServiceImpl<MoocSmartCourseClassMemberScoreMapper, MoocSmartCourseClassMemberScore> implements IMoocSmartCourseClassMemberScoreService
{
    @Autowired
    private MoocSmartCourseClassMemberScoreMapper moocSmartCourseClassMemberScoreMapper;

    /**
     * 查询互动课堂班级成员各项汇总成绩
     *
     * @param memberScoreId 互动课堂班级成员各项汇总成绩主键
     * @return 互动课堂班级成员各项汇总成绩
     */
    @Override
    public MoocSmartCourseClassMemberScore selectMoocSmartCourseClassMemberScoreByMemberScoreId(Long memberScoreId)
    {
        return this.getById(memberScoreId);
    }

    /**
     * 查询互动课堂班级成员各项汇总成绩列表
     *
     * @param moocSmartCourseClassMemberScore 互动课堂班级成员各项汇总成绩
     * @return 互动课堂班级成员各项汇总成绩
     */
    @Override
    public List<MoocSmartCourseClassMemberScore> selectMoocSmartCourseClassMemberScoreList(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        LambdaQueryWrapper<MoocSmartCourseClassMemberScore> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getClassMemberId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getClassMemberId
                ,moocSmartCourseClassMemberScore.getClassMemberId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getTotalScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getTotalScore
                ,moocSmartCourseClassMemberScore.getTotalScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getCoursewareScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getCoursewareScore
                ,moocSmartCourseClassMemberScore.getCoursewareScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getClassroomQuestionScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getClassroomQuestionScore
                ,moocSmartCourseClassMemberScore.getClassroomQuestionScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getHomeworkScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getHomeworkScore
                ,moocSmartCourseClassMemberScore.getHomeworkScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getExamScore())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getExamScore
                ,moocSmartCourseClassMemberScore.getExamScore());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getNotes())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getNotes
                ,moocSmartCourseClassMemberScore.getNotes());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getClassId
                ,moocSmartCourseClassMemberScore.getClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getUserId
                ,moocSmartCourseClassMemberScore.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getCreatedBy())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getCreatedBy
                ,moocSmartCourseClassMemberScore.getCreatedBy());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseClassMemberScore.getUpdatedBy())) {
                lambdaQueryWrapper.eq(MoocSmartCourseClassMemberScore::getUpdatedBy
                ,moocSmartCourseClassMemberScore.getUpdatedBy());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂班级成员各项汇总成绩
     *
     * @param moocSmartCourseClassMemberScore 互动课堂班级成员各项汇总成绩
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseClassMemberScore(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        return this.save(moocSmartCourseClassMemberScore);
    }

    /**
     * 修改互动课堂班级成员各项汇总成绩
     *
     * @param moocSmartCourseClassMemberScore 互动课堂班级成员各项汇总成绩
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseClassMemberScore(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore)
    {
        return this.updateById(moocSmartCourseClassMemberScore);
    }

    /**
     * 批量删除互动课堂班级成员各项汇总成绩
     *
     * @param memberScoreIds 需要删除的互动课堂班级成员各项汇总成绩主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseClassMemberScoreByMemberScoreIds(List<Long> memberScoreIds)
    {
        return this.removeByIds(memberScoreIds);
    }

    @Override
    public List<MoocSmartCourseClassMemberScore> getListByClassId(MoocSmartCourseClassMemberScore moocSmartCourseClassMemberScore) {
        return moocSmartCourseClassMemberScoreMapper.getListByClassId(moocSmartCourseClassMemberScore);
    }

}
