package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseMutualEvaluation;
import cn.dutp.edu.service.IMoocSmartCourseMutualEvaluationService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂互评Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/mutualEvaluation")
public class MoocSmartCourseMutualEvaluationController extends BaseController
{
    @Autowired
    private IMoocSmartCourseMutualEvaluationService moocSmartCourseMutualEvaluationService;

    /**
     * 查询互动课堂互评列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        startPage();
        List<MoocSmartCourseMutualEvaluation> list = moocSmartCourseMutualEvaluationService.selectMoocSmartCourseMutualEvaluationList(moocSmartCourseMutualEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂互评列表
     */

    @Log(title = "导出互动课堂互评", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        List<MoocSmartCourseMutualEvaluation> list = moocSmartCourseMutualEvaluationService.selectMoocSmartCourseMutualEvaluationList(moocSmartCourseMutualEvaluation);
        ExcelUtil<MoocSmartCourseMutualEvaluation> util = new ExcelUtil<MoocSmartCourseMutualEvaluation>(MoocSmartCourseMutualEvaluation.class);
        util.exportExcel(response, list, "互动课堂互评数据");
    }

    /**
     * 获取互动课堂互评详细信息
     */

    @GetMapping(value = "/{mutualEvaluationId}")
    public AjaxResult getInfo(@PathVariable("mutualEvaluationId") Long mutualEvaluationId)
    {
        return success(moocSmartCourseMutualEvaluationService.selectMoocSmartCourseMutualEvaluationByMutualEvaluationId(mutualEvaluationId));
    }

    /**
     * 新增互动课堂互评
     */

    @Log(title = "新增互动课堂互评", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        return toAjax(moocSmartCourseMutualEvaluationService.insertMoocSmartCourseMutualEvaluation(moocSmartCourseMutualEvaluation));
    }

    /**
     * 修改互动课堂互评
     */

    @Log(title = "修改互动课堂互评", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseMutualEvaluation moocSmartCourseMutualEvaluation)
    {
        return toAjax(moocSmartCourseMutualEvaluationService.updateMoocSmartCourseMutualEvaluation(moocSmartCourseMutualEvaluation));
    }

    /**
     * 删除互动课堂互评
     */

    @Log(title = "删除互动课堂互评", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mutualEvaluationIds}")
    public AjaxResult remove(@PathVariable Long[] mutualEvaluationIds)
    {
        return toAjax(moocSmartCourseMutualEvaluationService.deleteMoocSmartCourseMutualEvaluationByMutualEvaluationIds(Arrays.asList(mutualEvaluationIds)));
    }
}
