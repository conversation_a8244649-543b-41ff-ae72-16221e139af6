<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseSurveyQuestionMapper">
    
    <resultMap type="MoocSmartCourseSurveyQuestion" id="MoocSmartCourseSurveyQuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="surveyId"    column="survey_id"    />
        <result property="questionTitle"    column="question_title"    />
        <result property="questionContent"    column="question_content"    />
        <result property="sort"    column="sort"    />
        <result property="optionType"    column="option_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseSurveyQuestionVo">
        select question_id, survey_id, question_title, question_content, sort, option_type, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_survey_question
    </sql>

    <select id="selectMoocSmartCourseSurveyQuestionList" parameterType="MoocSmartCourseSurveyQuestion" resultMap="MoocSmartCourseSurveyQuestionResult">
        <include refid="selectMoocSmartCourseSurveyQuestionVo"/>
        <where>  
            <if test="surveyId != null "> and survey_id = #{surveyId}</if>
            <if test="questionTitle != null  and questionTitle != ''"> and question_title = #{questionTitle}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content = #{questionContent}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="optionType != null "> and option_type = #{optionType}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseSurveyQuestionByQuestionId" parameterType="Long" resultMap="MoocSmartCourseSurveyQuestionResult">
        <include refid="selectMoocSmartCourseSurveyQuestionVo"/>
        where question_id = #{questionId}
    </select>

    <insert id="insertMoocSmartCourseSurveyQuestion" parameterType="MoocSmartCourseSurveyQuestion">
        insert into mooc_smart_course_survey_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="questionId != null">question_id,</if>
            <if test="surveyId != null">survey_id,</if>
            <if test="questionTitle != null">question_title,</if>
            <if test="questionContent != null">question_content,</if>
            <if test="sort != null">sort,</if>
            <if test="optionType != null">option_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="questionId != null">#{questionId},</if>
            <if test="surveyId != null">#{surveyId},</if>
            <if test="questionTitle != null">#{questionTitle},</if>
            <if test="questionContent != null">#{questionContent},</if>
            <if test="sort != null">#{sort},</if>
            <if test="optionType != null">#{optionType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseSurveyQuestion" parameterType="MoocSmartCourseSurveyQuestion">
        update mooc_smart_course_survey_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="surveyId != null">survey_id = #{surveyId},</if>
            <if test="questionTitle != null">question_title = #{questionTitle},</if>
            <if test="questionContent != null">question_content = #{questionContent},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="optionType != null">option_type = #{optionType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteMoocSmartCourseSurveyQuestionByQuestionId" parameterType="Long">
        delete from mooc_smart_course_survey_question where question_id = #{questionId}
    </delete>

    <delete id="deleteMoocSmartCourseSurveyQuestionByQuestionIds" parameterType="String">
        delete from mooc_smart_course_survey_question where question_id in 
        <foreach item="questionId" collection="array" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>
</mapper>