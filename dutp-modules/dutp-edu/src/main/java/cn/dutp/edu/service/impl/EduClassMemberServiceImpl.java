package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.EduClassMemberMapper;
import cn.dutp.edu.domain.EduClassMember;
import cn.dutp.edu.service.IEduClassMemberService;

/**
 * 教务班级成员Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class EduClassMemberServiceImpl extends ServiceImpl<EduClassMemberMapper, EduClassMember> implements IEduClassMemberService
{
    @Autowired
    private EduClassMemberMapper eduClassMemberMapper;

    /**
     * 查询教务班级成员
     *
     * @param eduClassMemberId 教务班级成员主键
     * @return 教务班级成员
     */
    @Override
    public EduClassMember selectEduClassMemberByEduClassMemberId(Long eduClassMemberId)
    {
        return this.getById(eduClassMemberId);
    }

    /**
     * 查询教务班级成员列表
     *
     * @param eduClassMember 教务班级成员
     * @return 教务班级成员
     */
    @Override
    public List<EduClassMember> selectEduClassMemberList(EduClassMember eduClassMember)
    {
        LambdaQueryWrapper<EduClassMember> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(eduClassMember.getEduClassId())) {
                lambdaQueryWrapper.eq(EduClassMember::getEduClassId
                ,eduClassMember.getEduClassId());
            }
                if(ObjectUtil.isNotEmpty(eduClassMember.getUserId())) {
                lambdaQueryWrapper.eq(EduClassMember::getUserId
                ,eduClassMember.getUserId());
            }
                if(ObjectUtil.isNotEmpty(eduClassMember.getTeacherUserId())) {
                lambdaQueryWrapper.eq(EduClassMember::getTeacherUserId
                ,eduClassMember.getTeacherUserId());
            }
                if(ObjectUtil.isNotEmpty(eduClassMember.getJoinTime())) {
                lambdaQueryWrapper.eq(EduClassMember::getJoinTime
                ,eduClassMember.getJoinTime());
            }
                if(ObjectUtil.isNotEmpty(eduClassMember.getCreatedBy())) {
                lambdaQueryWrapper.eq(EduClassMember::getCreatedBy
                ,eduClassMember.getCreatedBy());
            }
                if(ObjectUtil.isNotEmpty(eduClassMember.getUpdatedBy())) {
                lambdaQueryWrapper.eq(EduClassMember::getUpdatedBy
                ,eduClassMember.getUpdatedBy());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增教务班级成员
     *
     * @param eduClassMember 教务班级成员
     * @return 结果
     */
    @Override
    public boolean insertEduClassMember(EduClassMember eduClassMember)
    {
        return this.save(eduClassMember);
    }

    /**
     * 修改教务班级成员
     *
     * @param eduClassMember 教务班级成员
     * @return 结果
     */
    @Override
    public boolean updateEduClassMember(EduClassMember eduClassMember)
    {
        return this.updateById(eduClassMember);
    }

    /**
     * 批量删除教务班级成员
     *
     * @param eduClassMemberIds 需要删除的教务班级成员主键
     * @return 结果
     */
    @Override
    public boolean deleteEduClassMemberByEduClassMemberIds(List<Long> eduClassMemberIds)
    {
        return this.removeByIds(eduClassMemberIds);
    }

}
