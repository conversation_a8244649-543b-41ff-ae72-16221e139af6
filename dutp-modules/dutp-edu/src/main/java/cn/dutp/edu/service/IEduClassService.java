package cn.dutp.edu.service;

import java.util.List;

import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.edu.domain.EduClass;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教务班级Service接口
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface IEduClassService extends IService<EduClass>
{
    /**
     * 查询教务班级
     *
     * @param eduClassId 教务班级主键
     * @return 教务班级
     */
    public EduClass selectEduClassByEduClassId(Long eduClassId);

    /**
     * 查询教务班级列表
     *
     * @param eduClass 教务班级
     * @return 教务班级集合
     */
    public List<EduClass> selectEduClassList(EduClass eduClass);

    /**
     * 新增教务班级
     *
     * @param eduClass 教务班级
     * @return 结果
     */
    public boolean insertEduClass(EduClass eduClass);

    /**
     * 修改教务班级
     *
     * @param eduClass 教务班级
     * @return 结果
     */
    public boolean updateEduClass(EduClass eduClass);

    /**
     * 批量删除教务班级
     *
     * @param eduClassIds 需要删除的教务班级主键集合
     * @return 结果
     */
    public boolean deleteEduClassByEduClassIds(List<Long> eduClassIds);

    /**
     * 查询教务班列表(无分页 下拉框)
     */
    AjaxResult getEduClassList(EduClass eduClass);
}
