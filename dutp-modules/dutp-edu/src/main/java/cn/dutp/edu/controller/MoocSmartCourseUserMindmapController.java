package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.MoocSmartCourseUserMindmap;
import cn.dutp.edu.service.IMoocSmartCourseUserMindmapService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 用户书籍思维导图数据Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/mindmap")
public class MoocSmartCourseUserMindmapController extends BaseController
{
    @Autowired
    private IMoocSmartCourseUserMindmapService moocSmartCourseUserMindmapService;

    /**
     * 查询用户书籍思维导图数据列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        startPage();
        List<MoocSmartCourseUserMindmap> list = moocSmartCourseUserMindmapService.selectMoocSmartCourseUserMindmapList(moocSmartCourseUserMindmap);
        return getDataTable(list);
    }

    /**
     * 导出用户书籍思维导图数据列表
     */

    @Log(title = "导出用户书籍思维导图数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        List<MoocSmartCourseUserMindmap> list = moocSmartCourseUserMindmapService.selectMoocSmartCourseUserMindmapList(moocSmartCourseUserMindmap);
        ExcelUtil<MoocSmartCourseUserMindmap> util = new ExcelUtil<MoocSmartCourseUserMindmap>(MoocSmartCourseUserMindmap.class);
        util.exportExcel(response, list, "用户书籍思维导图数据数据");
    }

    /**
     * 获取用户书籍思维导图数据详细信息
     */

    @GetMapping(value = "/{mindmapId}")
    public AjaxResult getInfo(@PathVariable("mindmapId") Long mindmapId)
    {
        return success(moocSmartCourseUserMindmapService.selectMoocSmartCourseUserMindmapByMindmapId(mindmapId));
    }

    /**
     * 根据书籍ID获取当前用户的思维导图数据
     */
    @GetMapping("/book/{bookId}")
    public AjaxResult getByBookId(@PathVariable("bookId") Long bookId) {
        Long userId = SecurityUtils.getUserId();
        MoocSmartCourseUserMindmap mindMap = moocSmartCourseUserMindmapService.selectByUserIdAndBookId(userId, bookId);
        return AjaxResult.success(mindMap);
    }

    /**
     * 新增用户书籍思维导图数据
     */

    @Log(title = "新增用户书籍思维导图数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        moocSmartCourseUserMindmap.setUserId(SecurityUtils.getUserId());
        moocSmartCourseUserMindmap.setCreateBy(SecurityUtils.getUsername());
        return toAjax(moocSmartCourseUserMindmapService.insertMoocSmartCourseUserMindmap(moocSmartCourseUserMindmap));
    }

    /**
     * 修改用户书籍思维导图数据
     */

    @Log(title = "修改用户书籍思维导图数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseUserMindmap moocSmartCourseUserMindmap)
    {
        MoocSmartCourseUserMindmap existingMap = moocSmartCourseUserMindmapService.selectMoocSmartCourseUserMindmapByMindmapId(moocSmartCourseUserMindmap.getMindmapId());
        if (existingMap == null || !existingMap.getUserId().equals(SecurityUtils.getUserId())) {
            return AjaxResult.error("无权修改他人的思维导图");
        }
        moocSmartCourseUserMindmap.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(moocSmartCourseUserMindmapService.updateMoocSmartCourseUserMindmap(moocSmartCourseUserMindmap));
    }

    /**
     * 删除用户书籍思维导图数据
     */

    @Log(title = "删除用户书籍思维导图数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mindmapIds}")
    public AjaxResult remove(@PathVariable Long[] mindmapIds)
    {
        return toAjax(moocSmartCourseUserMindmapService.deleteMoocSmartCourseUserMindmapByMindmapIds(Arrays.asList(mindmapIds)));
    }
}
