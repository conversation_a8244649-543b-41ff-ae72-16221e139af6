package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocUserResourceFolder;
import cn.dutp.edu.domain.vo.ResourceVO;
import cn.dutp.edu.service.IMoocUserResourceFolderService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.security.utils.SecurityUtils;

/**
 * 教师个人资源文件夹Controller
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/moocResourceFolder")
public class MoocUserResourceFolderController extends BaseController
{
    @Autowired
    private IMoocUserResourceFolderService moocUserResourceFolderService;

    /**
     * 查询教师个人资源文件夹列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocUserResourceFolder moocUserResourceFolder)
    {
        startPage();
        List<MoocUserResourceFolder> list = moocUserResourceFolderService.selectMoocUserResourceFolderList(moocUserResourceFolder);
        return getDataTable(list);
    }

    /**
     * 查询个人资源列表（包含文件夹和文件）
     */
    @GetMapping("/resourceList")
    public TableDataInfo getResourceList(MoocUserResourceFolder moocUserResourceFolder)
    {
        startPage();
        Long userId = SecurityUtils.getUserId();
        moocUserResourceFolder.setUserId(userId);
        List<ResourceVO> list = moocUserResourceFolderService.selectCombinedResources(moocUserResourceFolder);
        return getDataTable(list);
    }

    /**
     * 导出教师个人资源文件夹列表
     */

    @Log(title = "导出教师个人资源文件夹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocUserResourceFolder moocUserResourceFolder)
    {
        List<MoocUserResourceFolder> list = moocUserResourceFolderService.selectMoocUserResourceFolderList(moocUserResourceFolder);
        ExcelUtil<MoocUserResourceFolder> util = new ExcelUtil<MoocUserResourceFolder>(MoocUserResourceFolder.class);
        util.exportExcel(response, list, "教师个人资源文件夹数据");
    }

    /**
     * 获取教师个人资源文件夹详细信息
     */

    @GetMapping(value = "/{userFolderId}")
    public AjaxResult getInfo(@PathVariable("userFolderId") Long userFolderId)
    {
        return success(moocUserResourceFolderService.selectMoocUserResourceFolderByUserFolderId(userFolderId));
    }

    /**
     * 新增教师个人资源文件夹
     */

    @Log(title = "新增教师个人资源文件夹", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocUserResourceFolder moocUserResourceFolder)
    {
        return toAjax(moocUserResourceFolderService.insertMoocUserResourceFolder(moocUserResourceFolder));
    }

    /**
     * 修改教师个人资源文件夹
     */

    @Log(title = "修改教师个人资源文件夹", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocUserResourceFolder moocUserResourceFolder)
    {
        return toAjax(moocUserResourceFolderService.updateMoocUserResourceFolder(moocUserResourceFolder));
    }

    /**
     * 删除教师个人资源文件夹
     */
    @Log(title = "删除教师个人资源文件夹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userFolderIds}")
    public AjaxResult remove(@PathVariable Long[] userFolderIds)
    {
        return toAjax(moocUserResourceFolderService.deleteMoocUserResourceFolderByUserFolderIds(Arrays.asList(userFolderIds)));
    }

    /**
     * 查询个人资源文件夹列表(不分页)
     */
    @GetMapping("/listAll")
    public TableDataInfo listAll(MoocUserResourceFolder dtbUserResourceFolder)
    {
        dtbUserResourceFolder.setUserId(SecurityUtils.getUserId());
        List<MoocUserResourceFolder> list = moocUserResourceFolderService.selectMoocUserResourceFolderList(dtbUserResourceFolder);
        return getDataTable(list);
    }
}
