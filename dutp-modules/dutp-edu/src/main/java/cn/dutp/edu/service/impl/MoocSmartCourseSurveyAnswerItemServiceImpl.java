package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseSurveyAnswerItemMapper;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import cn.dutp.edu.service.IMoocSmartCourseSurveyAnswerItemService;

/**
 * 问卷调查答题明细（按题目存储）Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class MoocSmartCourseSurveyAnswerItemServiceImpl extends ServiceImpl<MoocSmartCourseSurveyAnswerItemMapper, MoocSmartCourseSurveyAnswerItem> implements IMoocSmartCourseSurveyAnswerItemService
{
    @Autowired
    private MoocSmartCourseSurveyAnswerItemMapper moocSmartCourseSurveyAnswerItemMapper;

    /**
     * 查询问卷调查答题明细（按题目存储）
     *
     * @param answerItemId 问卷调查答题明细（按题目存储）主键
     * @return 问卷调查答题明细（按题目存储）
     */
    @Override
    public MoocSmartCourseSurveyAnswerItem selectMoocSmartCourseSurveyAnswerItemByAnswerItemId(Long answerItemId)
    {
        return this.getById(answerItemId);
    }

    /**
     * 查询问卷调查答题明细（按题目存储）列表
     *
     * @param moocSmartCourseSurveyAnswerItem 问卷调查答题明细（按题目存储）
     * @return 问卷调查答题明细（按题目存储）
     */
    @Override
    public List<MoocSmartCourseSurveyAnswerItem> selectMoocSmartCourseSurveyAnswerItemList(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        LambdaQueryWrapper<MoocSmartCourseSurveyAnswerItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getAnswerId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getAnswerId
                ,moocSmartCourseSurveyAnswerItem.getAnswerId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getSurveyId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getSurveyId
                ,moocSmartCourseSurveyAnswerItem.getSurveyId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getCourseId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getCourseId
                ,moocSmartCourseSurveyAnswerItem.getCourseId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getUserId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getUserId
                ,moocSmartCourseSurveyAnswerItem.getUserId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getQuestionId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getQuestionId
                ,moocSmartCourseSurveyAnswerItem.getQuestionId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getQuestionType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getQuestionType
                ,moocSmartCourseSurveyAnswerItem.getQuestionType());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseSurveyAnswerItem.getAnswerValue())) {
                lambdaQueryWrapper.eq(MoocSmartCourseSurveyAnswerItem::getAnswerValue
                ,moocSmartCourseSurveyAnswerItem.getAnswerValue());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增问卷调查答题明细（按题目存储）
     *
     * @param moocSmartCourseSurveyAnswerItem 问卷调查答题明细（按题目存储）
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseSurveyAnswerItem(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        return this.save(moocSmartCourseSurveyAnswerItem);
    }

    /**
     * 修改问卷调查答题明细（按题目存储）
     *
     * @param moocSmartCourseSurveyAnswerItem 问卷调查答题明细（按题目存储）
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseSurveyAnswerItem(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        return this.updateById(moocSmartCourseSurveyAnswerItem);
    }

    /**
     * 批量删除问卷调查答题明细（按题目存储）
     *
     * @param answerItemIds 需要删除的问卷调查答题明细（按题目存储）主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseSurveyAnswerItemByAnswerItemIds(List<Long> answerItemIds)
    {
        return this.removeByIds(answerItemIds);
    }

}
