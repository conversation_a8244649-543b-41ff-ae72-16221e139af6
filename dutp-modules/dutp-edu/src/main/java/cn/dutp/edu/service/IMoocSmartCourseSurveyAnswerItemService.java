package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 问卷调查答题明细（按题目存储）Service接口
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface IMoocSmartCourseSurveyAnswerItemService extends IService<MoocSmartCourseSurveyAnswerItem>
{
    /**
     * 查询问卷调查答题明细（按题目存储）
     *
     * @param answerItemId 问卷调查答题明细（按题目存储）主键
     * @return 问卷调查答题明细（按题目存储）
     */
    public MoocSmartCourseSurveyAnswerItem selectMoocSmartCourseSurveyAnswerItemByAnswerItemId(Long answerItemId);

    /**
     * 查询问卷调查答题明细（按题目存储）列表
     *
     * @param moocSmartCourseSurveyAnswerItem 问卷调查答题明细（按题目存储）
     * @return 问卷调查答题明细（按题目存储）集合
     */
    public List<MoocSmartCourseSurveyAnswerItem> selectMoocSmartCourseSurveyAnswerItemList(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem);

    /**
     * 新增问卷调查答题明细（按题目存储）
     *
     * @param moocSmartCourseSurveyAnswerItem 问卷调查答题明细（按题目存储）
     * @return 结果
     */
    public boolean insertMoocSmartCourseSurveyAnswerItem(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem);

    /**
     * 修改问卷调查答题明细（按题目存储）
     *
     * @param moocSmartCourseSurveyAnswerItem 问卷调查答题明细（按题目存储）
     * @return 结果
     */
    public boolean updateMoocSmartCourseSurveyAnswerItem(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem);

    /**
     * 批量删除问卷调查答题明细（按题目存储）
     *
     * @param answerItemIds 需要删除的问卷调查答题明细（按题目存储）主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseSurveyAnswerItemByAnswerItemIds(List<Long> answerItemIds);

}
