package cn.dutp.edu.mapper;

import java.util.List;
import org.springframework.stereotype.Repository;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkOrTest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 互动课堂作业/考试管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseHomeworkOrTestMapper extends BaseMapper<MoocSmartCourseHomeworkOrTest>
{
    public List<MoocSmartCourseHomeworkOrTest> getHomeworkOrTestList(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest);
}
