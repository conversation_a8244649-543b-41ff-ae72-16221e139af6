package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseExtensionRecordFileMapper;
import cn.dutp.edu.domain.MoocSmartCourseExtensionRecordFile;
import cn.dutp.edu.service.IMoocSmartCourseExtensionRecordFileService;

/**
 * 拓展内容提交文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class MoocSmartCourseExtensionRecordFileServiceImpl extends ServiceImpl<MoocSmartCourseExtensionRecordFileMapper, MoocSmartCourseExtensionRecordFile> implements IMoocSmartCourseExtensionRecordFileService
{
    @Autowired
    private MoocSmartCourseExtensionRecordFileMapper moocSmartCourseExtensionRecordFileMapper;

    /**
     * 查询拓展内容提交文件
     *
     * @param fileId 拓展内容提交文件主键
     * @return 拓展内容提交文件
     */
    @Override
    public MoocSmartCourseExtensionRecordFile selectMoocSmartCourseExtensionRecordFileByFileId(Long fileId)
    {
        return this.getById(fileId);
    }

    /**
     * 查询拓展内容提交文件列表
     *
     * @param moocSmartCourseExtensionRecordFile 拓展内容提交文件
     * @return 拓展内容提交文件
     */
    @Override
    public List<MoocSmartCourseExtensionRecordFile> selectMoocSmartCourseExtensionRecordFileList(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
        LambdaQueryWrapper<MoocSmartCourseExtensionRecordFile> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecordFile.getRecordId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecordFile::getRecordId
                ,moocSmartCourseExtensionRecordFile.getRecordId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecordFile.getFileName())) {
                lambdaQueryWrapper.like(MoocSmartCourseExtensionRecordFile::getFileName
                ,moocSmartCourseExtensionRecordFile.getFileName());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecordFile.getFileUrl())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecordFile::getFileUrl
                ,moocSmartCourseExtensionRecordFile.getFileUrl());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecordFile.getFileType())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecordFile::getFileType
                ,moocSmartCourseExtensionRecordFile.getFileType());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseExtensionRecordFile.getFileSize())) {
                lambdaQueryWrapper.eq(MoocSmartCourseExtensionRecordFile::getFileSize
                ,moocSmartCourseExtensionRecordFile.getFileSize());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增拓展内容提交文件
     *
     * @param moocSmartCourseExtensionRecordFile 拓展内容提交文件
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseExtensionRecordFile(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
        return this.save(moocSmartCourseExtensionRecordFile);
    }

    /**
     * 修改拓展内容提交文件
     *
     * @param moocSmartCourseExtensionRecordFile 拓展内容提交文件
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseExtensionRecordFile(MoocSmartCourseExtensionRecordFile moocSmartCourseExtensionRecordFile)
    {
        return this.updateById(moocSmartCourseExtensionRecordFile);
    }

    /**
     * 批量删除拓展内容提交文件
     *
     * @param fileIds 需要删除的拓展内容提交文件主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseExtensionRecordFileByFileIds(List<Long> fileIds)
    {
        return this.removeByIds(fileIds);
    }

}
