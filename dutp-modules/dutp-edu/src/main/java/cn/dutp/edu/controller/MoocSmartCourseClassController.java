package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.MoocSmartCourseClass;
import cn.dutp.edu.service.IMoocSmartCourseClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂班级Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/class")
public class MoocSmartCourseClassController extends BaseController
{
    @Autowired
    private IMoocSmartCourseClassService moocSmartCourseClassService;

    /**
     * 查询互动课堂班级列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseClass moocSmartCourseClass)
    {
        startPage();
        List<MoocSmartCourseClass> list = moocSmartCourseClassService.selectMoocSmartCourseClassList(moocSmartCourseClass);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂班级列表
     */
    @RequiresPermissions("system:class:export")
    @Log(title = "导出互动课堂班级", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseClass moocSmartCourseClass)
    {
        List<MoocSmartCourseClass> list = moocSmartCourseClassService.selectMoocSmartCourseClassList(moocSmartCourseClass);
        ExcelUtil<MoocSmartCourseClass> util = new ExcelUtil<MoocSmartCourseClass>(MoocSmartCourseClass.class);
        util.exportExcel(response, list, "互动课堂班级数据");
    }

    /**
     * 获取互动课堂班级详细信息
     */
    @RequiresPermissions("system:class:query")
    @GetMapping(value = "/{classId}")
    public AjaxResult getInfo(@PathVariable("classId") Long classId)
    {
        return success(moocSmartCourseClassService.selectMoocSmartCourseClassByClassId(classId));
    }

    /**
     * 新增互动课堂班级
     */
    @RequiresPermissions("system:class:add")
    @Log(title = "新增互动课堂班级", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseClass moocSmartCourseClass)
    {
        return toAjax(moocSmartCourseClassService.insertMoocSmartCourseClass(moocSmartCourseClass));
    }

    /**
     * 修改互动课堂班级
     */
    @RequiresPermissions("system:class:edit")
    @Log(title = "修改互动课堂班级", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseClass moocSmartCourseClass)
    {
        return toAjax(moocSmartCourseClassService.updateMoocSmartCourseClass(moocSmartCourseClass));
    }

    /**
     * 删除互动课堂班级
     */
    @RequiresPermissions("system:class:remove")
    @Log(title = "删除互动课堂班级", businessType = BusinessType.DELETE)
    @DeleteMapping("/{classIds}")
    public AjaxResult remove(@PathVariable Long[] classIds)
    {
        return toAjax(moocSmartCourseClassService.deleteMoocSmartCourseClassByClassIds(Arrays.asList(classIds)));
    }

    /**
     * 获取互动课堂班级详细信息
     */
    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo(MoocSmartCourseClass moocSmartCourseClass)
    {
        return success(moocSmartCourseClassService.getInfo(moocSmartCourseClass));
    }

    /**
     * 教务获取班级列表
     */
    @GetMapping("/getListBySchool")
    public TableDataInfo getListBySchool(MoocSmartCourseClass moocSmartCourseClass)
    {
        startPage();
        moocSmartCourseClass.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<MoocSmartCourseClass> list = moocSmartCourseClassService.getListBySchool(moocSmartCourseClass);
        return getDataTable(list);
    }


    /**
     * 教务查看课表详情
     */
    @GetMapping(value = "/getInfoByClassId")
    public AjaxResult getInfoByClassId(MoocSmartCourseClass moocSmartCourseClass)
    {
        return success(moocSmartCourseClassService.getInfoByClassId(moocSmartCourseClass));
    }

/*    *//**
     * 教务考试/成绩获取班级列表
     *//*
    @GetMapping("/getListForTest")
    public TableDataInfo getListForTest(MoocSmartCourseClass moocSmartCourseClass)
    {
        startPage();
        moocSmartCourseClass.setSchoolId(SecurityUtils.getLoginUser().getSchoolId());
        List<MoocSmartCourseClass> list = moocSmartCourseClassService.getListForTest(moocSmartCourseClass);
        return getDataTable(list);
    }*/

}
