<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseExtensionRecordFileMapper">
    
    <resultMap type="MoocSmartCourseExtensionRecordFile" id="MoocSmartCourseExtensionRecordFileResult">
        <result property="fileId"    column="file_id"    />
        <result property="recordId"    column="record_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseExtensionRecordFileVo">
        select file_id, record_id, file_name, file_url, file_type, file_size, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_extension_record_file
    </sql>

    <select id="selectMoocSmartCourseExtensionRecordFileList" parameterType="MoocSmartCourseExtensionRecordFile" resultMap="MoocSmartCourseExtensionRecordFileResult">
        <include refid="selectMoocSmartCourseExtensionRecordFileVo"/>
        <where>  
            <if test="recordId != null "> and record_id = #{recordId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseExtensionRecordFileByFileId" parameterType="Long" resultMap="MoocSmartCourseExtensionRecordFileResult">
        <include refid="selectMoocSmartCourseExtensionRecordFileVo"/>
        where file_id = #{fileId}
    </select>

    <insert id="insertMoocSmartCourseExtensionRecordFile" parameterType="MoocSmartCourseExtensionRecordFile" useGeneratedKeys="true" keyProperty="fileId">
        insert into mooc_smart_course_extension_record_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">record_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">#{recordId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseExtensionRecordFile" parameterType="MoocSmartCourseExtensionRecordFile">
        update mooc_smart_course_extension_record_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteMoocSmartCourseExtensionRecordFileByFileId" parameterType="Long">
        delete from mooc_smart_course_extension_record_file where file_id = #{fileId}
    </delete>

    <delete id="deleteMoocSmartCourseExtensionRecordFileByFileIds" parameterType="String">
        delete from mooc_smart_course_extension_record_file where file_id in 
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>
</mapper>