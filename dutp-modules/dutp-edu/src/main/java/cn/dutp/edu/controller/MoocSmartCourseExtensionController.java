package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseExtension;
import cn.dutp.edu.service.IMoocSmartCourseExtensionService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂的拓展内容Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/extension")
public class MoocSmartCourseExtensionController extends BaseController
{
    @Autowired
    private IMoocSmartCourseExtensionService moocSmartCourseExtensionService;

    /**
     * 查询互动课堂的拓展内容列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseExtension moocSmartCourseExtension)
    {
        startPage();
        List<MoocSmartCourseExtension> list = moocSmartCourseExtensionService.selectMoocSmartCourseExtensionList(moocSmartCourseExtension);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂的拓展内容列表
     */

    @Log(title = "导出互动课堂的拓展内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseExtension moocSmartCourseExtension)
    {
        List<MoocSmartCourseExtension> list = moocSmartCourseExtensionService.selectMoocSmartCourseExtensionList(moocSmartCourseExtension);
        ExcelUtil<MoocSmartCourseExtension> util = new ExcelUtil<MoocSmartCourseExtension>(MoocSmartCourseExtension.class);
        util.exportExcel(response, list, "互动课堂的拓展内容数据");
    }

    /**
     * 获取互动课堂的拓展内容详细信息
     */

    @GetMapping(value = "/{extensionId}")
    public AjaxResult getInfo(@PathVariable("extensionId") Long extensionId)
    {
        return success(moocSmartCourseExtensionService.selectMoocSmartCourseExtensionByExtensionId(extensionId));
    }

    /**
     * 新增互动课堂的拓展内容
     */

    @Log(title = "新增互动课堂的拓展内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseExtension moocSmartCourseExtension)
    {
        return toAjax(moocSmartCourseExtensionService.insertMoocSmartCourseExtension(moocSmartCourseExtension));
    }

    /**
     * 修改互动课堂的拓展内容
     */

    @Log(title = "修改互动课堂的拓展内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseExtension moocSmartCourseExtension)
    {
        return toAjax(moocSmartCourseExtensionService.updateMoocSmartCourseExtension(moocSmartCourseExtension));
    }

    /**
     * 删除互动课堂的拓展内容
     */

    @Log(title = "删除互动课堂的拓展内容", businessType = BusinessType.DELETE)
    @DeleteMapping("/{extensionIds}")
    public AjaxResult remove(@PathVariable Long[] extensionIds)
    {
        return toAjax(moocSmartCourseExtensionService.deleteMoocSmartCourseExtensionByExtensionIds(Arrays.asList(extensionIds)));
    }
}
