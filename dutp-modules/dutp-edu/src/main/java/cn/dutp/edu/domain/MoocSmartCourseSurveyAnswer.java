package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 互动课堂的问卷调查回答对象 mooc_smart_course_survey_answer
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@TableName("mooc_smart_course_survey_answer")
public class MoocSmartCourseSurveyAnswer extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerId;

    /** 问卷ID */
        @Excel(name = "问卷ID")
    private Long surveyId;

    /** 课程ID */
        @Excel(name = "课程ID")
    private Long courseId;

    /** 参与者ID */
        @Excel(name = "参与者ID")
    private Long userId;

    /** 问卷回答内容 */
        @Excel(name = "问卷回答内容")
    private String answerContent;

    /** 是否删除（0：存在，2：已删除） */
    private String delFlag;

    /**
     * 答题卡
     */
    @TableField(exist = false)
    private List<MoocSmartCourseSurveyAnswerItem> answerItems;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("answerId", getAnswerId())
            .append("surveyId", getSurveyId())
            .append("courseId", getCourseId())
            .append("userId", getUserId())
            .append("answerContent", getAnswerContent())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("answerItems", getAnswerItems())
        .toString();
        }
        }
