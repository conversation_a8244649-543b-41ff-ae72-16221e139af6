package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.EduClassMember;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 教务班级成员Service接口
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface IEduClassMemberService extends IService<EduClassMember>
{
    /**
     * 查询教务班级成员
     *
     * @param eduClassMemberId 教务班级成员主键
     * @return 教务班级成员
     */
    public EduClassMember selectEduClassMemberByEduClassMemberId(Long eduClassMemberId);

    /**
     * 查询教务班级成员列表
     *
     * @param eduClassMember 教务班级成员
     * @return 教务班级成员集合
     */
    public List<EduClassMember> selectEduClassMemberList(EduClassMember eduClassMember);

    /**
     * 新增教务班级成员
     *
     * @param eduClassMember 教务班级成员
     * @return 结果
     */
    public boolean insertEduClassMember(EduClassMember eduClassMember);

    /**
     * 修改教务班级成员
     *
     * @param eduClassMember 教务班级成员
     * @return 结果
     */
    public boolean updateEduClassMember(EduClassMember eduClassMember);

    /**
     * 批量删除教务班级成员
     *
     * @param eduClassMemberIds 需要删除的教务班级成员主键集合
     * @return 结果
     */
    public boolean deleteEduClassMemberByEduClassMemberIds(List<Long> eduClassMemberIds);

}
