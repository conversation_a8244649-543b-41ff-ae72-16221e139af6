package cn.dutp.edu.config;

import cn.dutp.common.core.constant.Constants;
import cn.dutp.domain.DtbBookOrder;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkOrTestService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.aggregations.pipeline.EwmaModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Configuration
@EnableCaching
@AutoConfigureBefore(RedisAutoConfiguration.class)
public class EduRedisConfig {
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Bean
    RedisMessageListenerContainer container() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        container.addMessageListener(new MessageListener() {
            @Override
            public void onMessage(Message message, byte[] pattern) {
                String expiredKey = message.toString();
                log.info("过期key:{}", expiredKey);
                if (expiredKey.startsWith(Constants.REDIS_EXPIRE_KEY_PREFIX_ASSIGNMENT_ID_KEY)) {
                    // todo 待处理
                }
            }
        }, new PatternTopic("__keyevent@*__:expired"));
        return container;
    }


}
