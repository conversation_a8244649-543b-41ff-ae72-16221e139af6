package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseLessonEvaluation;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 课程评价Service接口
 *
 * <AUTHOR>
 */
public interface IMoocSmartCourseLessonEvaluationService extends IService<MoocSmartCourseLessonEvaluation>
{
    /**
     * 查询课程评价
     *
     * @param evaluationId 课程评价主键
     * @return 课程评价
     */
    public MoocSmartCourseLessonEvaluation selectMoocSmartCourseLessonEvaluationByEvaluationId(Long evaluationId);

    /**
     * 查询课程评价列表
     *
     * @param moocSmartCourseLessonEvaluation 课程评价
     * @return 课程评价集合
     */
    public List<MoocSmartCourseLessonEvaluation> selectMoocSmartCourseLessonEvaluationList(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation);

    /**
     * 新增课程评价
     *
     * @param moocSmartCourseLessonEvaluation 课程评价
     * @return 结果
     */
    public boolean insertMoocSmartCourseLessonEvaluation(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation);

    /**
     * 修改课程评价
     *
     * @param moocSmartCourseLessonEvaluation 课程评价
     * @return 结果
     */
    public boolean updateMoocSmartCourseLessonEvaluation(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation);

    /**
     * 批量删除课程评价
     *
     * @param evaluationIds 需要删除的课程评价主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseLessonEvaluationByEvaluationIds(List<Long> evaluationIds);

    List<MoocSmartCourseLessonEvaluation> getByClassId(MoocSmartCourseLessonEvaluation moocSmartCourseLessonEvaluation);
}
