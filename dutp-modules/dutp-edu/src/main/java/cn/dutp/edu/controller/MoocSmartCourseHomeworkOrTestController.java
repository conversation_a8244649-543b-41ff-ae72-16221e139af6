package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseHomeworkOrTest;
import cn.dutp.edu.service.IMoocSmartCourseHomeworkOrTestService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 互动课堂作业/考试管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/test")
public class MoocSmartCourseHomeworkOrTestController extends BaseController
{
    @Autowired
    private IMoocSmartCourseHomeworkOrTestService moocSmartCourseHomeworkOrTestService;

    /**
     * 查询互动课堂作业/考试管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        startPage();
        List<MoocSmartCourseHomeworkOrTest> list = moocSmartCourseHomeworkOrTestService.selectMoocSmartCourseHomeworkOrTestList(moocSmartCourseHomeworkOrTest);
        return getDataTable(list);
    }

    /**
     * 导出互动课堂作业/考试管理列表
     */
    @RequiresPermissions("edu:test:export")
    @Log(title = "导出互动课堂作业/考试管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        List<MoocSmartCourseHomeworkOrTest> list = moocSmartCourseHomeworkOrTestService.selectMoocSmartCourseHomeworkOrTestList(moocSmartCourseHomeworkOrTest);
        ExcelUtil<MoocSmartCourseHomeworkOrTest> util = new ExcelUtil<MoocSmartCourseHomeworkOrTest>(MoocSmartCourseHomeworkOrTest.class);
        util.exportExcel(response, list, "互动课堂作业/考试管理数据");
    }

    /**
     * 获取互动课堂作业/考试管理详细信息
     */
    @RequiresPermissions("edu:test:query")
    @GetMapping(value = "/{assignmentId}")
    public AjaxResult getInfo(@PathVariable("assignmentId") Long assignmentId)
    {
        return success(moocSmartCourseHomeworkOrTestService.selectMoocSmartCourseHomeworkOrTestByAssignmentId(assignmentId));
    }

    /**
     * 获取互动课堂作业/考试管理及试卷详细信息
     */
    @GetMapping(value = "/paper/{assignmentId}")
    public AjaxResult getInfoWithPaper(@PathVariable("assignmentId") Long assignmentId)
    {
        return success(moocSmartCourseHomeworkOrTestService.selectHomeworkWithPaperByHomeworkId(assignmentId));
    }

    /**
     * 新增互动课堂作业/考试管理
     */
    @RequiresPermissions("edu:test:add")
    @Log(title = "新增互动课堂作业/考试管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        return toAjax(moocSmartCourseHomeworkOrTestService.insertMoocSmartCourseHomeworkOrTest(moocSmartCourseHomeworkOrTest));
    }

    /**
     * 修改互动课堂作业/考试管理
     */
    @RequiresPermissions("edu:test:edit")
    @Log(title = "修改互动课堂作业/考试管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        return toAjax(moocSmartCourseHomeworkOrTestService.updateMoocSmartCourseHomeworkOrTest(moocSmartCourseHomeworkOrTest));
    }

    /**
     * 删除互动课堂作业/考试管理
     */
    @RequiresPermissions("edu:test:remove")
    @Log(title = "删除互动课堂作业/考试管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{assignmentIds}")
    public AjaxResult remove(@PathVariable Long[] assignmentIds)
    {
        return toAjax(moocSmartCourseHomeworkOrTestService.deleteMoocSmartCourseHomeworkOrTestByAssignmentIds(Arrays.asList(assignmentIds)));
    }

    /**
     * 查询互动课堂作业/考试管理列表
     */
    @GetMapping("/getHomeworkOrTestList")
    public AjaxResult getHomeworkOrTestList(MoocSmartCourseHomeworkOrTest moocSmartCourseHomeworkOrTest)
    {
        return success(moocSmartCourseHomeworkOrTestService.getHomeworkOrTestList(moocSmartCourseHomeworkOrTest));
    }
}
