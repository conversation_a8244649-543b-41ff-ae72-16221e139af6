package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseTestPaperAnswer;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂学生考试/作业结果Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseTestPaperAnswerService extends IService<MoocSmartCourseTestPaperAnswer>
{
    /**
     * 查询互动课堂学生考试/作业结果
     *
     * @param answertId 互动课堂学生考试/作业结果主键
     * @return 互动课堂学生考试/作业结果
     */
    public MoocSmartCourseTestPaperAnswer selectMoocSmartCourseTestPaperAnswerByAnswertId(Long answertId);

    /**
     * 查询互动课堂学生考试/作业结果列表
     *
     * @param moocSmartCourseTestPaperAnswer 互动课堂学生考试/作业结果
     * @return 互动课堂学生考试/作业结果集合
     */
    public List<MoocSmartCourseTestPaperAnswer> selectMoocSmartCourseTestPaperAnswerList(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer);

    /**
     * 新增互动课堂学生考试/作业结果
     *
     * @param moocSmartCourseTestPaperAnswer 互动课堂学生考试/作业结果
     * @return 结果
     */
    public boolean insertMoocSmartCourseTestPaperAnswer(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer);

    /**
     * 修改互动课堂学生考试/作业结果
     *
     * @param moocSmartCourseTestPaperAnswer 互动课堂学生考试/作业结果
     * @return 结果
     */
    public boolean updateMoocSmartCourseTestPaperAnswer(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer);

    /**
     * 批量删除互动课堂学生考试/作业结果
     *
     * @param answertIds 需要删除的互动课堂学生考试/作业结果主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseTestPaperAnswerByAnswertIds(List<Long> answertIds);

    /**
     * 根据试卷ID查询用户答题结果
     * @param testPaperId
     * @param userId
     * @return
     */
    MoocSmartCourseTestPaperAnswer selectUserAnswerByTestPaperId(Long testPaperId, Long userId);

    List<MoocSmartCourseTestPaperAnswer> getUserAnswerByAssignmentId(MoocSmartCourseTestPaperAnswer moocSmartCourseTestPaperAnswer);
}
