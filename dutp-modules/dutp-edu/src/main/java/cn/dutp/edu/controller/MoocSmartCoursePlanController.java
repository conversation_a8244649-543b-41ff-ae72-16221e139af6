package cn.dutp.edu.controller;

import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.edu.domain.MoocSmartCoursePlan;
import cn.dutp.edu.service.IMoocSmartCoursePlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 教学计划管理课程管理Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/moocSmartCoursePlan")
public class MoocSmartCoursePlanController extends BaseController {

    @Autowired
    private IMoocSmartCoursePlanService moocSmartCoursePlanService;

    /**
     * 导入教务课程
     */
    @RequiresPermissions("edu:moocSmartCoursePlan:import")
    @Log(title = "导入教务课程", businessType = BusinessType.EXPORT)
    @PostMapping("/importCoursePlan")
    public AjaxResult importCoursePlan(@RequestBody List<MoocSmartCoursePlan> moocSmartCoursePlanList) {
        return success(moocSmartCoursePlanService.importCoursePlan(moocSmartCoursePlanList));
    }

    /**
     * 查询教务课程列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCoursePlan moocSmartCoursePlan)
    {
        startPage();
        List<MoocSmartCoursePlan> list = moocSmartCoursePlanService.selectMoocSmartCoursePlanList(moocSmartCoursePlan);
        return getDataTable(list);
    }

    /**
     * 新增教务课程
     */
    @RequiresPermissions("edu:moocSmartCoursePlan:add")
    @Log(title = "新增教务课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCoursePlan moocSmartCoursePlan)
    {
        return toAjax(moocSmartCoursePlanService.insertMoocSmartCoursePlan(moocSmartCoursePlan));
    }

    /**
     * 修改教务课程
     */
    @RequiresPermissions("edu:moocSmartCoursePlan:edit")
    @Log(title = "修改教务课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCoursePlan moocSmartCoursePlan)
    {
        return toAjax(moocSmartCoursePlanService.updateMoocSmartCoursePlan(moocSmartCoursePlan));
    }

    /**
     * 删除教务课程
     */
    @RequiresPermissions("edu:moocSmartCoursePlan:remove")
    @Log(title = "删除教务课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Long[] courseIds)
    {
        return toAjax(moocSmartCoursePlanService.deleteMoocSmartCoursePlanByCourseIds(Arrays.asList(courseIds)));
    }

    /**
     * 获取教务课程详细信息
     */
    @RequiresPermissions("edu:moocSmartCoursePlan:query")
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Long courseId)
    {
        return success(moocSmartCoursePlanService.selectMoocSmartCoursePlanByCourseId(courseId));
    }

    /**
     * 获取拥有教务课程的学校
     */
    @GetMapping(value = "/getSchoolByCoursePlan")
    public AjaxResult getSchoolByCoursePlan(){
        return success(moocSmartCoursePlanService.getSchoolByCoursePlan());
    }

    /**
     * 获取教学计划列表 (下拉)
     */
    @GetMapping("/listNoPage")
    public AjaxResult listNoPage(MoocSmartCoursePlan moocSmartCoursePlan)
    {
        AjaxResult result = moocSmartCoursePlanService.selectPlanListNoPage();
        return result;
    }

}
