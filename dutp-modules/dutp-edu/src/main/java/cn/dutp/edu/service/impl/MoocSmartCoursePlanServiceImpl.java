package cn.dutp.edu.service.impl;

import java.rmi.ServerException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.edu.domain.DutpSchool;
import cn.dutp.edu.domain.MoocSmartCoursePlan;

import cn.dutp.edu.mapper.DtpSchoolMapper;
import cn.dutp.edu.mapper.MoocSmartCoursePlanMapper;
import cn.dutp.edu.service.IMoocSmartCoursePlanService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 教务课程Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
public class MoocSmartCoursePlanServiceImpl extends ServiceImpl<MoocSmartCoursePlanMapper, MoocSmartCoursePlan> implements IMoocSmartCoursePlanService
{
    @Autowired
    private MoocSmartCoursePlanMapper moocSmartCoursePlanMapper;

    @Autowired
    private DtpSchoolMapper dutpSchoolMapper;

    /**
     * 查询教务课程列表
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 教务课程
     */
    @Override
    public List<MoocSmartCoursePlan> selectMoocSmartCoursePlanList(MoocSmartCoursePlan moocSmartCoursePlan)
    {
        List<MoocSmartCoursePlan> list = moocSmartCoursePlanMapper.selectMoocSmartCoursePlanList(moocSmartCoursePlan);
        return list;
    }

    /**
     * 新增教务课程
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCoursePlan(MoocSmartCoursePlan moocSmartCoursePlan)
    {

        List<MoocSmartCoursePlan> moocSmartCourseList = getBySmartCode(moocSmartCoursePlan.getCourseCode());
        if(ObjectUtil.isNotEmpty(moocSmartCourseList)){
            throw new ServiceException("课程编码已存在!");
        }
        moocSmartCoursePlan.setCreatedBy(SecurityUtils.getUserId());
        moocSmartCoursePlan.setCreateBy(SecurityUtils.getUsername());
        return this.save(moocSmartCoursePlan);
    }

    private List<MoocSmartCoursePlan> getBySmartCode(String courseCode) {
        QueryWrapper<MoocSmartCoursePlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MoocSmartCoursePlan::getCourseCode,courseCode).eq(MoocSmartCoursePlan::getDelFlag,0);
        return moocSmartCoursePlanMapper.selectList(queryWrapper);
    }

    /**
     * 修改教务课程
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCoursePlan(MoocSmartCoursePlan moocSmartCoursePlan)
    {
        List<MoocSmartCoursePlan> moocSmartCourseList = getBySmartCode(moocSmartCoursePlan.getCourseCode());
        boolean exists = moocSmartCourseList.stream()
                .anyMatch(obj -> !obj.getCourseId().equals(moocSmartCoursePlan.getCourseId()));
        if(moocSmartCourseList.size()>1 || exists){
            throw new ServiceException("课程编码已存在!");
        }
        return this.updateById(moocSmartCoursePlan);
    }

    /**
     * 批量删除教务课程
     *
     * @param courseIds 需要删除的教务课程主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCoursePlanByCourseIds(List<Long> courseIds)
    {
        return this.removeByIds(courseIds);
    }

    /**
     * 查询教务课程
     *
     * @param courseId 教务课程主键
     * @return 教务课程
     */
    @Override
    public MoocSmartCoursePlan selectMoocSmartCoursePlanByCourseId(Long courseId)
    {
        MoocSmartCoursePlan moocSmartCoursePlan = this.getById(courseId);
        if(ObjectUtil.isNull(moocSmartCoursePlan)){
            throw new ServiceException("课程不存在!");
        }
        if (StringUtils.isNotBlank(moocSmartCoursePlan.getUserIds())) {
            List<Long> longList = Arrays.stream(moocSmartCoursePlan.getUserIds().split(",")) // 将字符串按逗号分割为数组，并转换为流
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            moocSmartCoursePlan.setUserIdList(longList);
        }
        return moocSmartCoursePlan;
    }

    @Override
    public List<DutpSchool> getSchoolByCoursePlan() {
        return moocSmartCoursePlanMapper.getSchoolByCoursePlan(SecurityUtils.getLoginUser().getSchoolId());
    }

    @Override
    public String importCoursePlan(List<MoocSmartCoursePlan> moocSmartCoursePlanList) {
        if (CollectionUtils.isEmpty(moocSmartCoursePlanList)) {
            throw new ServiceException("导入教务课程数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        try {
            for (int i = 0; i < moocSmartCoursePlanList.size(); i++) {
                if (StringUtils.isBlank(moocSmartCoursePlanList.get(i).getCourseName())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：课程名称不能为空！";
                    failureMsg.append(msg);
                    continue;
                }

                if (StringUtils.isBlank(moocSmartCoursePlanList.get(i).getCourseCode())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：课程编码不能为空！";
                    failureMsg.append(msg);
                    continue;
                } else {
                    List<MoocSmartCoursePlan> moocSmartCourseList = getBySmartCode(moocSmartCoursePlanList.get(i).getCourseCode());
                    if(ObjectUtil.isNotEmpty(moocSmartCourseList)){
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：课程编码已存在！";
                        failureMsg.append(msg);
                        continue;
                    }
                }
                if (StringUtils.isBlank(moocSmartCoursePlanList.get(i).getSchoolName())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：所属学院不能为空！";
                    failureMsg.append(msg);
                    continue;
                }

                if (StringUtils.isBlank(moocSmartCoursePlanList.get(i).getStatusStr())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：状态不能为空！";
                    failureMsg.append(msg);
                    continue;
                }

                QueryWrapper<DutpSchool> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(DutpSchool::getSchoolName,moocSmartCoursePlanList.get(i).getSchoolName())
                        .eq(DutpSchool::getDataType,1).eq(DutpSchool::getParentId,SecurityUtils.getLoginUser().getSchoolId())
                        .eq(DutpSchool::getDelFlag,0);
                DutpSchool school = dutpSchoolMapper.selectOne(queryWrapper);
                if (ObjectUtil.isEmpty(school)) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、第 " + (i + 1) + "行 导入失败：所属学院不存在！";
                    failureMsg.append(msg);
                    continue;
                } else {
                    moocSmartCoursePlanList.get(i).setCategoryId(school.getSchoolId());
                    moocSmartCoursePlanList.get(i).setStatus(moocSmartCoursePlanList.get(i).equals("启用") ? 0 : 1);
                    this.save(moocSmartCoursePlanList.get(i));
                    successNum++;
                }
            }
        } catch (NullPointerException e) {
            e.printStackTrace();
            failureMsg.insert(0, "很抱歉，导入失败！请确保所有字段都已填写！");
            return failureMsg.toString();
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "共 " + moocSmartCoursePlanList.size() + " 条数据,其中" + successNum + "条导入成功," + failureNum + "条导入失败，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + moocSmartCoursePlanList.size() + " 条");
            return successMsg.toString();
        }
    }


    /**
     * 查询教务课程列表
     *
     * @param moocSmartCoursePlan 教务课程
     * @return 教务课程集合
     */
    @Override
    public AjaxResult selectPlanListNoPage() {
        Long userId = SecurityUtils.getUserId();
        return AjaxResult.success(moocSmartCoursePlanMapper.selectPlanListNoPage());
    }
}
