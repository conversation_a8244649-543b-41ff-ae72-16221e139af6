package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 用户书籍思维导图数据对象 mooc_smart_course_user_mindmap
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@TableName("mooc_smart_course_user_mindmap")
public class MoocSmartCourseUserMindmap extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long mindmapId;

    /** 用户ID */
        @Excel(name = "用户ID")
    private Long userId;

    /** 书籍ID */
        @Excel(name = "书籍ID")
    private Long bookId;

    /** 思维导图的完整数据，以JSON格式字符串存储 */
        @Excel(name = "思维导图的完整数据，以JSON格式字符串存储")
    private String mindMapData;

    /** 是否删除（0：存在，2：已删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("mindmapId", getMindmapId())
            .append("userId", getUserId())
            .append("bookId", getBookId())
            .append("mindMapData", getMindMapData())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
