package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseMissionMapper;
import cn.dutp.edu.domain.MoocSmartCourseMission;
import cn.dutp.edu.service.IMoocSmartCourseMissionService;

/**
 * 互动课堂的教材学习任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseMissionServiceImpl extends ServiceImpl<MoocSmartCourseMissionMapper, MoocSmartCourseMission> implements IMoocSmartCourseMissionService
{
    @Autowired
    private MoocSmartCourseMissionMapper moocSmartCourseMissionMapper;

    /**
     * 查询互动课堂的教材学习任务
     *
     * @param missionId 互动课堂的教材学习任务主键
     * @return 互动课堂的教材学习任务
     */
    @Override
    public MoocSmartCourseMission selectMoocSmartCourseMissionByMissionId(Long missionId)
    {
        return this.getById(missionId);
    }

    /**
     * 查询互动课堂的教材学习任务列表
     *
     * @param moocSmartCourseMission 互动课堂的教材学习任务
     * @return 互动课堂的教材学习任务
     */
    @Override
    public List<MoocSmartCourseMission> selectMoocSmartCourseMissionList(MoocSmartCourseMission moocSmartCourseMission)
    {
        LambdaQueryWrapper<MoocSmartCourseMission> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getClassId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getClassId
                ,moocSmartCourseMission.getClassId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getCreatorId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getCreatorId
                ,moocSmartCourseMission.getCreatorId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getMissionTitle())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getMissionTitle
                ,moocSmartCourseMission.getMissionTitle());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getMissionChapterId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getMissionChapterId
                ,moocSmartCourseMission.getMissionChapterId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getMissionDescribe())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getMissionDescribe
                ,moocSmartCourseMission.getMissionDescribe());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getStartTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getStartTime
                ,moocSmartCourseMission.getStartTime());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseMission.getEndTime())) {
                lambdaQueryWrapper.eq(MoocSmartCourseMission::getEndTime
                ,moocSmartCourseMission.getEndTime());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的教材学习任务
     *
     * @param moocSmartCourseMission 互动课堂的教材学习任务
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseMission(MoocSmartCourseMission moocSmartCourseMission)
    {
        return this.save(moocSmartCourseMission);
    }

    /**
     * 修改互动课堂的教材学习任务
     *
     * @param moocSmartCourseMission 互动课堂的教材学习任务
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseMission(MoocSmartCourseMission moocSmartCourseMission)
    {
        return this.updateById(moocSmartCourseMission);
    }

    /**
     * 批量删除互动课堂的教材学习任务
     *
     * @param missionIds 需要删除的互动课堂的教材学习任务主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseMissionByMissionIds(List<Long> missionIds)
    {
        return this.removeByIds(missionIds);
    }

}
