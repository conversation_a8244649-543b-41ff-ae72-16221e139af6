package cn.dutp.edu.service;

import java.util.List;

import cn.dutp.edu.domain.MoocSmartCourseClass;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂班级Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseClassService extends IService<MoocSmartCourseClass>
{
    /**
     * 查询互动课堂班级
     *
     * @param classId 互动课堂班级主键
     * @return 互动课堂班级
     */
    public MoocSmartCourseClass selectMoocSmartCourseClassByClassId(Long classId);

    /**
     * 查询互动课堂班级列表
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 互动课堂班级集合
     */
    public List<MoocSmartCourseClass> selectMoocSmartCourseClassList(MoocSmartCourseClass moocSmartCourseClass);

    /**
     * 新增互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 结果
     */
    public boolean insertMoocSmartCourseClass(MoocSmartCourseClass moocSmartCourseClass);

    /**
     * 修改互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 结果
     */
    public boolean updateMoocSmartCourseClass(MoocSmartCourseClass moocSmartCourseClass);

    /**
     * 批量删除互动课堂班级
     *
     * @param classIds 需要删除的互动课堂班级主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseClassByClassIds(List<Long> classIds);

    /**
     * 查询互动课堂班级
     *
     * @param moocSmartCourseClass 互动课堂班级
     * @return 互动课堂班级
     */
    public MoocSmartCourseClass getInfo(MoocSmartCourseClass moocSmartCourseClass);

    List<MoocSmartCourseClass> getListBySchool(MoocSmartCourseClass moocSmartCourseClass);

    MoocSmartCourseClass getInfoByClassId(MoocSmartCourseClass moocSmartCourseClass);
}
