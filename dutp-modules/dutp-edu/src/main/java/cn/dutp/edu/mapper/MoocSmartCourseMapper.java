package cn.dutp.edu.mapper;

import cn.dutp.edu.domain.MoocSmartCourse;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 智能课程Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Repository
public interface MoocSmartCourseMapper extends BaseMapper<MoocSmartCourse>
{

    List<MoocSmartCourse> getListData(@Param("param") MoocSmartCourse moocSmartCourse);
}
