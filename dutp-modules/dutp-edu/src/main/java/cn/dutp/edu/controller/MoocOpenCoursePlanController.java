package cn.dutp.edu.controller;

import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.domain.MoocOpenCoursePlan;
import cn.dutp.edu.service.IMoocOpenCoursePlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 公开课开课计划Controller
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/openCoursePlan")
public class MoocOpenCoursePlanController extends BaseController {
    @Autowired
    private IMoocOpenCoursePlanService moocOpenCoursePlanService;

    /**
     * 查询公开课开课计划列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocOpenCoursePlan moocOpenCoursePlan) {
        startPage();
        List<MoocOpenCoursePlan> list = moocOpenCoursePlanService.selectMoocOpenCoursePlanList(moocOpenCoursePlan);
        return getDataTable(list);
    }

    /**
     * 导出公开课开课计划列表
     */
    @Log(title = "导出公开课开课计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocOpenCoursePlan moocOpenCoursePlan) {
        List<MoocOpenCoursePlan> list = moocOpenCoursePlanService.selectMoocOpenCoursePlanList(moocOpenCoursePlan);
        ExcelUtil<MoocOpenCoursePlan> util = new ExcelUtil<MoocOpenCoursePlan>(MoocOpenCoursePlan.class);
        util.exportExcel(response, list, "公开课开课计划数据");
    }

    /**
     * 获取公开课开课计划详细信息
     */
    @GetMapping(value = "/{planId}")
    public AjaxResult getInfo(@PathVariable("planId") Long planId) {
        return success(moocOpenCoursePlanService.selectMoocOpenCoursePlanByPlanId(planId));
    }

    /**
     * 新增公开课开课计划
     */
    @Log(title = "新增公开课开课计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocOpenCoursePlan moocOpenCoursePlan) {
        return toAjax(moocOpenCoursePlanService.insertMoocOpenCoursePlan(moocOpenCoursePlan));
    }

    /**
     * 修改公开课开课计划
     */
    @Log(title = "修改公开课开课计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocOpenCoursePlan moocOpenCoursePlan) {
        return toAjax(moocOpenCoursePlanService.updateMoocOpenCoursePlan(moocOpenCoursePlan));
    }

    /**
     * 删除公开课开课计划
     */

    @Log(title = "删除公开课开课计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planIds}")
    public AjaxResult remove(@PathVariable Long[] planIds) {
        return toAjax(moocOpenCoursePlanService.deleteMoocOpenCoursePlanByPlanIds(Arrays.asList(planIds)));
    }
}
