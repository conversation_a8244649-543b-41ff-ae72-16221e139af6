package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswer;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import cn.dutp.edu.service.IMoocSmartCourseSurveyAnswerService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;
import org.springframework.util.CollectionUtils;

/**
 * 互动课堂的问卷调查回答Controller
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@RestController
@RequestMapping("/surveyAnswer")
public class MoocSmartCourseSurveyAnswerController extends BaseController
{
    @Autowired
    private IMoocSmartCourseSurveyAnswerService moocSmartCourseSurveyAnswerService;

    /**
     * 查询互动课堂的问卷调查回答列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        startPage();
        List<MoocSmartCourseSurveyAnswer> list = moocSmartCourseSurveyAnswerService.selectMoocSmartCourseSurveyAnswerList(moocSmartCourseSurveyAnswer);
        return getDataTable(list);
    }

    /**
     * 获取用户指定问卷的作答记录
     */
    @GetMapping("/getSubmittedAnswer")
    public AjaxResult getSubmittedAnswer(Long surveyId) {
        MoocSmartCourseSurveyAnswer query = new MoocSmartCourseSurveyAnswer();
        query.setSurveyId(surveyId);
        query.setUserId(SecurityUtils.getUserId());
        List<MoocSmartCourseSurveyAnswer> list = moocSmartCourseSurveyAnswerService.selectMoocSmartCourseSurveyAnswerList(query);
        if (CollectionUtils.isEmpty(list)) {
            return success();
        }
        return success(list.get(0));
    }

    /**
     * 导出互动课堂的问卷调查回答列表
     */

    @Log(title = "导出互动课堂的问卷调查回答", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        List<MoocSmartCourseSurveyAnswer> list = moocSmartCourseSurveyAnswerService.selectMoocSmartCourseSurveyAnswerList(moocSmartCourseSurveyAnswer);
        ExcelUtil<MoocSmartCourseSurveyAnswer> util = new ExcelUtil<MoocSmartCourseSurveyAnswer>(MoocSmartCourseSurveyAnswer.class);
        util.exportExcel(response, list, "互动课堂的问卷调查回答数据");
    }

    /**
     * 获取互动课堂的问卷调查回答详细信息
     */

    @GetMapping(value = "/{answerId}")
    public AjaxResult getInfo(@PathVariable("answerId") Long answerId)
    {
        return success(moocSmartCourseSurveyAnswerService.selectMoocSmartCourseSurveyAnswerByAnswerId(answerId));
    }

    /**
     * 新增互动课堂的问卷调查回答
     */

    @Log(title = "新增互动课堂的问卷调查回答", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        Long userId = SecurityUtils.getUserId();
        moocSmartCourseSurveyAnswer.setUserId(userId);
        if (moocSmartCourseSurveyAnswer.getAnswerItems() != null) {
            moocSmartCourseSurveyAnswer.getAnswerItems().forEach(item -> item.setUserId(userId));
        }
        return toAjax(moocSmartCourseSurveyAnswerService.insertMoocSmartCourseSurveyAnswer(moocSmartCourseSurveyAnswer));
    }

    /**
     * 修改互动课堂的问卷调查回答
     */

    @Log(title = "修改互动课堂的问卷调查回答", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseSurveyAnswer moocSmartCourseSurveyAnswer)
    {
        return toAjax(moocSmartCourseSurveyAnswerService.updateMoocSmartCourseSurveyAnswer(moocSmartCourseSurveyAnswer));
    }

    /**
     * 删除互动课堂的问卷调查回答
     */

    @Log(title = "删除互动课堂的问卷调查回答", businessType = BusinessType.DELETE)
    @DeleteMapping("/{answerIds}")
    public AjaxResult remove(@PathVariable Long[] answerIds)
    {
        return toAjax(moocSmartCourseSurveyAnswerService.deleteMoocSmartCourseSurveyAnswerByAnswerIds(Arrays.asList(answerIds)));
    }
}
