package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

import cn.dutp.edu.domain.MoocSmartCourse;
import cn.dutp.edu.service.IMoocSmartCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 智能课程Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/course")
public class MoocSmartCourseController extends BaseController
{
    @Autowired
    private IMoocSmartCourseService moocSmartCourseService;

    /**
     * 查询智能课程列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourse moocSmartCourse)
    {
        startPage();
        List<MoocSmartCourse> list = moocSmartCourseService.selectMoocSmartCourseList(moocSmartCourse);
        return getDataTable(list);
    }

    /**
     * 导出智能课程列表
     */
    @Log(title = "导出智能课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourse moocSmartCourse)
    {
        List<MoocSmartCourse> list = moocSmartCourseService.selectMoocSmartCourseList(moocSmartCourse);
        ExcelUtil<MoocSmartCourse> util = new ExcelUtil<MoocSmartCourse>(MoocSmartCourse.class);
        util.exportExcel(response, list, "智能课程数据");
    }

    /**
     * 获取智能课程详细信息
     */
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Long courseId)
    {
        return success(moocSmartCourseService.selectMoocSmartCourseByCourseId(courseId));
    }

    /**
     * 新增智能课程
     */
    @Log(title = "新增智能课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourse moocSmartCourse)
    {
        return toAjax(moocSmartCourseService.insertMoocSmartCourse(moocSmartCourse));
    }

    /**
     * 修改智能课程
     */
    @Log(title = "修改智能课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourse moocSmartCourse)
    {
        return toAjax(moocSmartCourseService.updateMoocSmartCourse(moocSmartCourse));
    }

    /**
     * 删除智能课程
     */
    @Log(title = "删除智能课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Long[] courseIds)
    {
        return toAjax(moocSmartCourseService.deleteMoocSmartCourseByCourseIds(Arrays.asList(courseIds)));
    }
}
