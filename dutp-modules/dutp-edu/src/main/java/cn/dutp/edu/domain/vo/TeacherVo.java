package cn.dutp.edu.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: dutp
 * @date: 2025/7/18 11:32
 */
@Data
public class TeacherVo {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;
    /**
     *教师名称
     */
    private String realName;

    /**
     * 手机号码
     */
    private String phonenumber;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long  specialityId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long academyId;

    /**
     *教师账号
     */
    private String userName;

    /**
     *学号/工号
     */
    private String userNo;

    /**
     *所属学校
     */
    private String schoolName;

    /**
     *所属学院
     */
    private String academyName;

    /**
     *所属专业
     */
    private String subjectName;

    /**
     *备注
     */
    private String remark;

    /**
     *邮箱
     */
    private String email;

    /**
     *密码
     */
    private String password;

    /**
     *创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;



}
