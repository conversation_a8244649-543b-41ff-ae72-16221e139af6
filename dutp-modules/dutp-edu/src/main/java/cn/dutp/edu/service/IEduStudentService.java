package cn.dutp.edu.service;

import cn.dutp.edu.domain.vo.EduStudentVo;
import cn.dutp.edu.domian.DutpUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2025/7/21 9:45
 */
public interface IEduStudentService extends IService<DutpUser> {

    /**
     * 查询当前学校下的学生
     * @param student 学生
     * @return 结果
     */
    List<EduStudentVo> studentListInSchool(EduStudentVo student);


    /**
     * 查询班级下的学生
     * @param student 学生
     * @return 结果
     */
    List<EduStudentVo> studentListInClass(EduStudentVo student);

    boolean addStudentInfo(DutpUser student);
}
