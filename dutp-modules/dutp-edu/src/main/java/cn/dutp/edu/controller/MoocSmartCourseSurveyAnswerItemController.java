package cn.dutp.edu.controller;

import java.util.List;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.security.annotation.RequiresPermissions;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.edu.domain.MoocSmartCourseSurveyAnswerItem;
import cn.dutp.edu.service.IMoocSmartCourseSurveyAnswerItemService;
import cn.dutp.common.core.utils.poi.ExcelUtil;
import cn.dutp.common.core.web.page.TableDataInfo;

/**
 * 问卷调查答题明细（按题目存储）Controller
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@RestController
@RequestMapping("/edu/urveyAnswerItem")
public class MoocSmartCourseSurveyAnswerItemController extends BaseController
{
    @Autowired
    private IMoocSmartCourseSurveyAnswerItemService moocSmartCourseSurveyAnswerItemService;

    /**
     * 查询问卷调查答题明细（按题目存储）列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        startPage();
        List<MoocSmartCourseSurveyAnswerItem> list = moocSmartCourseSurveyAnswerItemService.selectMoocSmartCourseSurveyAnswerItemList(moocSmartCourseSurveyAnswerItem);
        return getDataTable(list);
    }

    /**
     * 导出问卷调查答题明细（按题目存储）列表
     */

    @Log(title = "导出问卷调查答题明细（按题目存储）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        List<MoocSmartCourseSurveyAnswerItem> list = moocSmartCourseSurveyAnswerItemService.selectMoocSmartCourseSurveyAnswerItemList(moocSmartCourseSurveyAnswerItem);
        ExcelUtil<MoocSmartCourseSurveyAnswerItem> util = new ExcelUtil<MoocSmartCourseSurveyAnswerItem>(MoocSmartCourseSurveyAnswerItem.class);
        util.exportExcel(response, list, "问卷调查答题明细（按题目存储）数据");
    }

    /**
     * 获取问卷调查答题明细（按题目存储）详细信息
     */

    @GetMapping(value = "/{answerItemId}")
    public AjaxResult getInfo(@PathVariable("answerItemId") Long answerItemId)
    {
        return success(moocSmartCourseSurveyAnswerItemService.selectMoocSmartCourseSurveyAnswerItemByAnswerItemId(answerItemId));
    }

    /**
     * 新增问卷调查答题明细（按题目存储）
     */

    @Log(title = "新增问卷调查答题明细（按题目存储）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        return toAjax(moocSmartCourseSurveyAnswerItemService.insertMoocSmartCourseSurveyAnswerItem(moocSmartCourseSurveyAnswerItem));
    }

    /**
     * 修改问卷调查答题明细（按题目存储）
     */

    @Log(title = "修改问卷调查答题明细（按题目存储）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MoocSmartCourseSurveyAnswerItem moocSmartCourseSurveyAnswerItem)
    {
        return toAjax(moocSmartCourseSurveyAnswerItemService.updateMoocSmartCourseSurveyAnswerItem(moocSmartCourseSurveyAnswerItem));
    }

    /**
     * 删除问卷调查答题明细（按题目存储）
     */

    @Log(title = "删除问卷调查答题明细（按题目存储）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{answerItemIds}")
    public AjaxResult remove(@PathVariable Long[] answerItemIds)
    {
        return toAjax(moocSmartCourseSurveyAnswerItemService.deleteMoocSmartCourseSurveyAnswerItemByAnswerItemIds(Arrays.asList(answerItemIds)));
    }
}
