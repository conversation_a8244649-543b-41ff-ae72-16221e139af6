<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseBrainstormReplyMapper">
    
    <resultMap type="MoocSmartCourseBrainstormReply" id="MoocSmartCourseBrainstormReplyResult">
        <result property="replyId"    column="reply_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="doingsId"    column="doings_id"    />
        <result property="doingsRespond"    column="doings_respond"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseBrainstormReplyVo">
        select reply_id, student_id, doings_id, doings_respond, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_brainstorm_reply
    </sql>

    <select id="selectMoocSmartCourseBrainstormReplyList" parameterType="MoocSmartCourseBrainstormReply" resultMap="MoocSmartCourseBrainstormReplyResult">
        <include refid="selectMoocSmartCourseBrainstormReplyVo"/>
        <where>  
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="doingsId != null "> and doings_id = #{doingsId}</if>
            <if test="doingsRespond != null  and doingsRespond != ''"> and doings_respond = #{doingsRespond}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseBrainstormReplyByReplyId" parameterType="Long" resultMap="MoocSmartCourseBrainstormReplyResult">
        <include refid="selectMoocSmartCourseBrainstormReplyVo"/>
        where reply_id = #{replyId}
    </select>

    <insert id="insertMoocSmartCourseBrainstormReply" parameterType="MoocSmartCourseBrainstormReply">
        insert into mooc_smart_course_brainstorm_reply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="replyId != null">reply_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="doingsId != null">doings_id,</if>
            <if test="doingsRespond != null">doings_respond,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="replyId != null">#{replyId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="doingsId != null">#{doingsId},</if>
            <if test="doingsRespond != null">#{doingsRespond},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseBrainstormReply" parameterType="MoocSmartCourseBrainstormReply">
        update mooc_smart_course_brainstorm_reply
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="doingsId != null">doings_id = #{doingsId},</if>
            <if test="doingsRespond != null">doings_respond = #{doingsRespond},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where reply_id = #{replyId}
    </update>

    <delete id="deleteMoocSmartCourseBrainstormReplyByReplyId" parameterType="Long">
        delete from mooc_smart_course_brainstorm_reply where reply_id = #{replyId}
    </delete>

    <delete id="deleteMoocSmartCourseBrainstormReplyByReplyIds" parameterType="String">
        delete from mooc_smart_course_brainstorm_reply where reply_id in 
        <foreach item="replyId" collection="array" open="(" separator="," close=")">
            #{replyId}
        </foreach>
    </delete>
</mapper>