package cn.dutp.edu.service;

import java.util.List;
import cn.dutp.edu.domain.MoocSmartCourseResource;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * 互动课堂课程资源发送记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface IMoocSmartCourseResourceService extends IService<MoocSmartCourseResource>
{
    /**
     * 查询互动课堂课程资源发送记录
     *
     * @param sentId 互动课堂课程资源发送记录主键
     * @return 互动课堂课程资源发送记录
     */
    public MoocSmartCourseResource selectMoocSmartCourseResourceBySentId(Long sentId);

    /**
     * 查询互动课堂课程资源发送记录列表
     *
     * @param moocSmartCourseResource 互动课堂课程资源发送记录
     * @return 互动课堂课程资源发送记录集合
     */
    public List<MoocSmartCourseResource> selectMoocSmartCourseResourceList(MoocSmartCourseResource moocSmartCourseResource);

    /**
     * 新增互动课堂课程资源发送记录
     *
     * @param moocSmartCourseResource 互动课堂课程资源发送记录
     * @return 结果
     */
    public boolean insertMoocSmartCourseResource(MoocSmartCourseResource moocSmartCourseResource);

    /**
     * 修改互动课堂课程资源发送记录
     *
     * @param moocSmartCourseResource 互动课堂课程资源发送记录
     * @return 结果
     */
    public boolean updateMoocSmartCourseResource(MoocSmartCourseResource moocSmartCourseResource);

    /**
     * 批量删除互动课堂课程资源发送记录
     *
     * @param sentIds 需要删除的互动课堂课程资源发送记录主键集合
     * @return 结果
     */
    public boolean deleteMoocSmartCourseResourceBySentIds(List<Long> sentIds);

}
