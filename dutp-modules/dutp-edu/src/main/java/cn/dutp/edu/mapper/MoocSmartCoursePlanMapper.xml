<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCoursePlanMapper">
    
    <resultMap type="MoocSmartCoursePlan" id="MoocSmartCoursePlanResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseCode"    column="course_code"    />
        <result property="categoryId"    column="category_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="description"    column="description"    />
        <result property="hour"    column="hour"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="userIds"    column="user_ids"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCoursePlanVo">
        select course_id, course_name, course_code, category_id, book_id, description, hour, cover_image_url, user_ids, status, created_by, updated_by, del_flag, create_time, update_time from mooc_smart_course_plan
    </sql>
    
    <select id="selectMoocSmartCoursePlanByCourseId" parameterType="Long" resultMap="MoocSmartCoursePlanResult">
        <include refid="selectMoocSmartCoursePlanVo"/>
        where course_id = #{courseId}
    </select>

    <insert id="insertMoocSmartCoursePlan" parameterType="MoocSmartCoursePlan">
        insert into mooc_smart_course_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="courseName != null and courseName != ''">course_name,</if>
            <if test="courseCode != null">course_code,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="bookId != null">book_id,</if>
            <if test="description != null">description,</if>
            <if test="hour != null">hour,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="userIds != null">user_ids,</if>
            <if test="status != null">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="courseName != null and courseName != ''">#{courseName},</if>
            <if test="courseCode != null">#{courseCode},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="bookId != null">#{bookId},</if>
            <if test="description != null">#{description},</if>
            <if test="hour != null">#{hour},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="userIds != null">#{userIds},</if>
            <if test="status != null">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCoursePlan" parameterType="MoocSmartCoursePlan">
        update mooc_smart_course_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name = #{courseName},</if>
            <if test="courseCode != null">course_code = #{courseCode},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="hour != null">hour = #{hour},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="userIds != null">user_ids = #{userIds},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteMoocSmartCoursePlanByCourseId" parameterType="Long">
        delete from mooc_smart_course_plan where course_id = #{courseId}
    </delete>

    <delete id="deleteMoocSmartCoursePlanByCourseIds" parameterType="String">
        delete from mooc_smart_course_plan where course_id in 
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>

    <select id="selectMoocSmartCoursePlanList" parameterType="cn.dutp.edu.domain.MoocSmartCoursePlan" resultType="cn.dutp.edu.domain.MoocSmartCoursePlan">
        select
            mp.course_id,
            mp.course_name,
            mp.course_code,
            mp.category_id,
            mp.book_id,
            mp.description,
            mp.hour,
            mp.cover_image_url,
            mp.user_ids,
            ds.school_name,
            ds.school_id,
            db.book_name,
            GROUP_CONCAT(DISTINCT p.real_name SEPARATOR ', ') as realName,
            mp.status
        from
            mooc_smart_course_plan mp
        left join
            dutp_school ds on mp.category_id = ds.school_id and ds.del_flag = 0
        left join
            dutp_user p on FIND_IN_SET(p.user_id, mp.user_ids) > 0
        left join
            dtb_book db on db.book_id = mp.book_id and db.del_flag = 0
        where
            mp.del_flag = 0
            <if test="schoolId != null">
                and ds.school_id = #{schoolId}
            </if>
            <if test="categoryId != null">
                and mp.category_id = #{categoryId}
            </if>
            <if test="courseName != null and courseName != ''">
                and mp.course_name like concat('%', #{courseName}, '%')
            </if>
            <if test="bookName != null and bookName != ''">
                and db.book_name like concat('%', #{bookName}, '%')
            </if>
            <if test="status != null">
                and mp.status = #{status}
            </if>
        group by
            mp.course_id, mp.course_name, mp.course_code, mp.category_id, mp.book_id, mp.description,
            mp.hour, mp.cover_image_url, mp.user_ids, ds.school_name, mp.status, ds.school_name,
            db.book_name, mp.status
            <if test="realName != null and realName != ''">
                having
                GROUP_CONCAT(DISTINCT p.real_name SEPARATOR ', ') like concat('%', #{realName}, '%')
            </if>
        order by mp.create_time desc
    </select>



    <select id="getSchoolByCoursePlan" resultType="cn.dutp.edu.domain.DutpSchool">
        select
            distinct ds.school_id,
            ds.school_name,
            ds.school_code,
            ds.data_type
        from dutp_school ds
        inner join mooc_smart_course_plan mp on ds.school_id = mp.category_id AND mp.del_flag = 0
        where ds.parent_id = #{schoolId} and ds.del_flag = 0
    </select>
    <select id="selectPlanListNoPage" resultType="cn.dutp.edu.domain.MoocSmartCoursePlan">
        select
            mp.course_id,
            mp.course_name,
            mp.course_code,
            mp.category_id,
            mp.book_id,
            mp.description,
            mp.hour,
            mp.cover_image_url,
            mp.user_ids,
            ds.school_name,
            ds.school_id,
            db.book_name,
            mp.status
        from
            mooc_smart_course_plan mp
        left join
            dutp_school ds on mp.category_id = ds.school_id and ds.del_flag = 0
        left join
            dtb_book db on db.book_id = mp.book_id and db.del_flag = 0
        where
            mp.del_flag = 0
            and mp.status = 0
        order by mp.create_time desc
    </select>
</mapper>