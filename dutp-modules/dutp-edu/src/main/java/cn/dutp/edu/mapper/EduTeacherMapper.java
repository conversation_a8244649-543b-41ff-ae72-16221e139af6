package cn.dutp.edu.mapper;

import cn.dutp.edu.domain.vo.TeacherVo;
import cn.dutp.edu.domian.DutpUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2025/7/18 10:07
 */
@Repository
public interface EduTeacherMapper extends BaseMapper<DutpUser> {

    List<DutpUser> selectTeacherList(TeacherVo teacher);

    TeacherVo selectTeacherById(Long userId);

    ;
}
