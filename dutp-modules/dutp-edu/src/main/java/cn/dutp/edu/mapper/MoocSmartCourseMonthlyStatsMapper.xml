<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseMonthlyStatsMapper">
    
    <resultMap type="MoocSmartCourseMonthlyStats" id="MoocSmartCourseMonthlyStatsResult">
        <result property="statId"    column="stat_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="classId"    column="class_id"    />
        <result property="statYear"    column="stat_year"    />
        <result property="statMonth"    column="stat_month"    />
        <result property="attendanceRate"    column="attendance_rate"    />
        <result property="studentsPresentCount"    column="students_present_count"    />
        <result property="expectedAttendanceCount"    column="expected_attendance_count"    />
        <result property="totalLearningDurationSeconds"    column="total_learning_duration_seconds"    />
        <result property="activeLearnersCount"    column="active_learners_count"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseMonthlyStatsVo">
        select stat_id, course_id, class_id, stat_year, stat_month, attendance_rate, students_present_count, expected_attendance_count, total_learning_duration_seconds, active_learners_count, created_by, create_time, updated_by, update_time from mooc_smart_course_monthly_stats
    </sql>

    <select id="selectMoocSmartCourseMonthlyStatsList" parameterType="MoocSmartCourseMonthlyStats" resultMap="MoocSmartCourseMonthlyStatsResult">
        <include refid="selectMoocSmartCourseMonthlyStatsVo"/>
        <where>  
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="statYear != null "> and stat_year = #{statYear}</if>
            <if test="statMonth != null "> and stat_month = #{statMonth}</if>
            <if test="attendanceRate != null "> and attendance_rate = #{attendanceRate}</if>
            <if test="studentsPresentCount != null "> and students_present_count = #{studentsPresentCount}</if>
            <if test="expectedAttendanceCount != null "> and expected_attendance_count = #{expectedAttendanceCount}</if>
            <if test="totalLearningDurationSeconds != null "> and total_learning_duration_seconds = #{totalLearningDurationSeconds}</if>
            <if test="activeLearnersCount != null "> and active_learners_count = #{activeLearnersCount}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="updatedBy != null "> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseMonthlyStatsByStatId" parameterType="Long" resultMap="MoocSmartCourseMonthlyStatsResult">
        <include refid="selectMoocSmartCourseMonthlyStatsVo"/>
        where stat_id = #{statId}
    </select>

    <insert id="insertMoocSmartCourseMonthlyStats" parameterType="MoocSmartCourseMonthlyStats" useGeneratedKeys="true" keyProperty="statId">
        insert into mooc_smart_course_monthly_stats
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="statYear != null">stat_year,</if>
            <if test="statMonth != null">stat_month,</if>
            <if test="attendanceRate != null">attendance_rate,</if>
            <if test="studentsPresentCount != null">students_present_count,</if>
            <if test="expectedAttendanceCount != null">expected_attendance_count,</if>
            <if test="totalLearningDurationSeconds != null">total_learning_duration_seconds,</if>
            <if test="activeLearnersCount != null">active_learners_count,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="statYear != null">#{statYear},</if>
            <if test="statMonth != null">#{statMonth},</if>
            <if test="attendanceRate != null">#{attendanceRate},</if>
            <if test="studentsPresentCount != null">#{studentsPresentCount},</if>
            <if test="expectedAttendanceCount != null">#{expectedAttendanceCount},</if>
            <if test="totalLearningDurationSeconds != null">#{totalLearningDurationSeconds},</if>
            <if test="activeLearnersCount != null">#{activeLearnersCount},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseMonthlyStats" parameterType="MoocSmartCourseMonthlyStats">
        update mooc_smart_course_monthly_stats
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="statYear != null">stat_year = #{statYear},</if>
            <if test="statMonth != null">stat_month = #{statMonth},</if>
            <if test="attendanceRate != null">attendance_rate = #{attendanceRate},</if>
            <if test="studentsPresentCount != null">students_present_count = #{studentsPresentCount},</if>
            <if test="expectedAttendanceCount != null">expected_attendance_count = #{expectedAttendanceCount},</if>
            <if test="totalLearningDurationSeconds != null">total_learning_duration_seconds = #{totalLearningDurationSeconds},</if>
            <if test="activeLearnersCount != null">active_learners_count = #{activeLearnersCount},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where stat_id = #{statId}
    </update>

    <delete id="deleteMoocSmartCourseMonthlyStatsByStatId" parameterType="Long">
        delete from mooc_smart_course_monthly_stats where stat_id = #{statId}
    </delete>

    <delete id="deleteMoocSmartCourseMonthlyStatsByStatIds" parameterType="String">
        delete from mooc_smart_course_monthly_stats where stat_id in 
        <foreach item="statId" collection="array" open="(" separator="," close=")">
            #{statId}
        </foreach>
    </delete>
</mapper>