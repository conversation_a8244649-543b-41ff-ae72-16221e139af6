<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocSmartCourseSurveyAnswerItemMapper">
    
    <resultMap type="MoocSmartCourseSurveyAnswerItem" id="MoocSmartCourseSurveyAnswerItemResult">
        <result property="answerItemId"    column="answer_item_id"    />
        <result property="answerId"    column="answer_id"    />
        <result property="surveyId"    column="survey_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="userId"    column="user_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="answerValue"    column="answer_value"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMoocSmartCourseSurveyAnswerItemVo">
        select answer_item_id, answer_id, survey_id, course_id, user_id, question_id, question_type, answer_value, del_flag, create_by, create_time, update_by, update_time from mooc_smart_course_survey_answer_item
    </sql>

    <select id="selectMoocSmartCourseSurveyAnswerItemList" parameterType="MoocSmartCourseSurveyAnswerItem" resultMap="MoocSmartCourseSurveyAnswerItemResult">
        <include refid="selectMoocSmartCourseSurveyAnswerItemVo"/>
        <where>  
            <if test="answerId != null "> and answer_id = #{answerId}</if>
            <if test="surveyId != null "> and survey_id = #{surveyId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
            <if test="answerValue != null  and answerValue != ''"> and answer_value = #{answerValue}</if>
        </where>
    </select>
    
    <select id="selectMoocSmartCourseSurveyAnswerItemByAnswerItemId" parameterType="Long" resultMap="MoocSmartCourseSurveyAnswerItemResult">
        <include refid="selectMoocSmartCourseSurveyAnswerItemVo"/>
        where answer_item_id = #{answerItemId}
    </select>

    <insert id="insertMoocSmartCourseSurveyAnswerItem" parameterType="MoocSmartCourseSurveyAnswerItem" useGeneratedKeys="true" keyProperty="answerItemId">
        insert into mooc_smart_course_survey_answer_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="answerId != null">answer_id,</if>
            <if test="surveyId != null">survey_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="answerValue != null">answer_value,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="answerId != null">#{answerId},</if>
            <if test="surveyId != null">#{surveyId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="answerValue != null">#{answerValue},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMoocSmartCourseSurveyAnswerItem" parameterType="MoocSmartCourseSurveyAnswerItem">
        update mooc_smart_course_survey_answer_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="answerId != null">answer_id = #{answerId},</if>
            <if test="surveyId != null">survey_id = #{surveyId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="answerValue != null">answer_value = #{answerValue},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where answer_item_id = #{answerItemId}
    </update>

    <delete id="deleteMoocSmartCourseSurveyAnswerItemByAnswerItemId" parameterType="Long">
        delete from mooc_smart_course_survey_answer_item where answer_item_id = #{answerItemId}
    </delete>

    <delete id="deleteMoocSmartCourseSurveyAnswerItemByAnswerItemIds" parameterType="String">
        delete from mooc_smart_course_survey_answer_item where answer_item_id in 
        <foreach item="answerItemId" collection="array" open="(" separator="," close=")">
            #{answerItemId}
        </foreach>
    </delete>
</mapper>