package cn.dutp.edu.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.edu.mapper.MoocSmartCourseDiscussionReplyMapper;
import cn.dutp.edu.domain.MoocSmartCourseDiscussionReply;
import cn.dutp.edu.service.IMoocSmartCourseDiscussionReplyService;

/**
 * 互动课堂的课上主题讨论消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MoocSmartCourseDiscussionReplyServiceImpl extends ServiceImpl<MoocSmartCourseDiscussionReplyMapper, MoocSmartCourseDiscussionReply> implements IMoocSmartCourseDiscussionReplyService
{
    @Autowired
    private MoocSmartCourseDiscussionReplyMapper moocSmartCourseDiscussionReplyMapper;

    /**
     * 查询互动课堂的课上主题讨论消息
     *
     * @param replyId 互动课堂的课上主题讨论消息主键
     * @return 互动课堂的课上主题讨论消息
     */
    @Override
    public MoocSmartCourseDiscussionReply selectMoocSmartCourseDiscussionReplyByReplyId(Long replyId)
    {
        return this.getById(replyId);
    }

    /**
     * 查询互动课堂的课上主题讨论消息列表
     *
     * @param moocSmartCourseDiscussionReply 互动课堂的课上主题讨论消息
     * @return 互动课堂的课上主题讨论消息
     */
    @Override
    public List<MoocSmartCourseDiscussionReply> selectMoocSmartCourseDiscussionReplyList(MoocSmartCourseDiscussionReply moocSmartCourseDiscussionReply)
    {
        LambdaQueryWrapper<MoocSmartCourseDiscussionReply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionReply.getDiscussionId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionReply::getDiscussionId
                ,moocSmartCourseDiscussionReply.getDiscussionId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionReply.getStudentId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionReply::getStudentId
                ,moocSmartCourseDiscussionReply.getStudentId());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionReply.getReplyContent())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionReply::getReplyContent
                ,moocSmartCourseDiscussionReply.getReplyContent());
            }
                if(ObjectUtil.isNotEmpty(moocSmartCourseDiscussionReply.getParentReplyId())) {
                lambdaQueryWrapper.eq(MoocSmartCourseDiscussionReply::getParentReplyId
                ,moocSmartCourseDiscussionReply.getParentReplyId());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增互动课堂的课上主题讨论消息
     *
     * @param moocSmartCourseDiscussionReply 互动课堂的课上主题讨论消息
     * @return 结果
     */
    @Override
    public boolean insertMoocSmartCourseDiscussionReply(MoocSmartCourseDiscussionReply moocSmartCourseDiscussionReply)
    {
        return this.save(moocSmartCourseDiscussionReply);
    }

    /**
     * 修改互动课堂的课上主题讨论消息
     *
     * @param moocSmartCourseDiscussionReply 互动课堂的课上主题讨论消息
     * @return 结果
     */
    @Override
    public boolean updateMoocSmartCourseDiscussionReply(MoocSmartCourseDiscussionReply moocSmartCourseDiscussionReply)
    {
        return this.updateById(moocSmartCourseDiscussionReply);
    }

    /**
     * 批量删除互动课堂的课上主题讨论消息
     *
     * @param replyIds 需要删除的互动课堂的课上主题讨论消息主键
     * @return 结果
     */
    @Override
    public boolean deleteMoocSmartCourseDiscussionReplyByReplyIds(List<Long> replyIds)
    {
        return this.removeByIds(replyIds);
    }

}
