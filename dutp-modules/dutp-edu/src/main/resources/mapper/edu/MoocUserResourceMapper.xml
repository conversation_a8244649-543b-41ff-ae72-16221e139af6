<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.MoocUserResourceMapper">

    <resultMap type="cn.dutp.edu.domain.MoocUserResource" id="MoocUserResourceResult">
        <result property="resourceId"    column="resource_id"    />
        <result property="userId"    column="user_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileSize"    column="file_size"    />
        <result property="questionId"    column="question_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="folderId"    column="folder_id"    />
        <result property="folderName"    column="folder_name"    />
    </resultMap>

    <sql id="selectMoocUserResourceVo">
        select resource_id, user_id, file_name, file_url, file_type, file_size, question_id, create_by, create_time, update_by, update_time, del_flag, folder_id from mooc_user_resource
    </sql>

    <select id="selectMoocUserResourceList" parameterType="cn.dutp.edu.domain.MoocUserResource" resultMap="MoocUserResourceResult">
        <include refid="selectMoocUserResourceVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="folderId != null "> and folder_id = #{folderId}</if>
        </where>
    </select>

    <select id="selectMoocUserResourceByResourceId" parameterType="Long" resultMap="MoocUserResourceResult">
        <include refid="selectMoocUserResourceVo"/>
        where resource_id = #{resourceId}
    </select>

    <insert id="insertMoocUserResource" parameterType="cn.dutp.edu.domain.MoocUserResource" useGeneratedKeys="true" keyProperty="resourceId">
        insert into mooc_user_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="questionId != null">question_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="folderId != null">folder_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="folderId != null">#{folderId},</if>
        </trim>
    </insert>

    <update id="updateMoocUserResource" parameterType="cn.dutp.edu.domain.MoocUserResource">
        update mooc_user_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
        </trim>
        where resource_id = #{resourceId}
    </update>

    <delete id="deleteMoocUserResourceByResourceId" parameterType="Long">
        delete from mooc_user_resource where resource_id = #{resourceId}
    </delete>

    <delete id="deleteMoocUserResourceByResourceIds" parameterType="String">
        delete from mooc_user_resource where resource_id in
        <foreach item="resourceId" collection="array" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </delete>

    <select id="selectMoocUserResourceListByRecycle" parameterType="cn.dutp.edu.domain.MoocUserResource" resultMap="MoocUserResourceResult">
        select a.resource_id, a.user_id, a.file_name, a.file_url, a.file_type, a.file_size, a.question_id,
        a.create_by, a.create_time, a.update_by, a.update_time, a.del_flag, a.folder_id,
        ifnull(b.folder_name,'根目录') as folder_name
        from mooc_user_resource a
        left join mooc_user_resource_folder b on a.folder_id = b.user_folder_id
        <where>
            <if test="userId != null">
                AND a.user_id = #{userId}
            </if>
            <if test="fileName != null and fileName != ''">
                AND a.file_name like concat('%', #{fileName}, '%')
            </if>
            <if test="fileType != null and fileType != ''">
                AND a.file_type = #{fileType}
            </if>
            <if test="folderId != null">
                AND a.folder_id = #{folderId}
            </if>
                AND a.del_flag = '1'
        </where>
        order by a.update_time desc
    </select>

    <select id="selectMoocUserResourceByIds" resultMap="MoocUserResourceResult">
        select resource_id, user_id, file_name, file_url, file_type, file_size, question_id,
        create_by, create_time, update_by, update_time, del_flag, folder_id
        from mooc_user_resource
        where resource_id in
        <foreach collection="list" item="resourceId" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </select>

    <select id="getUsedSpaceByUserId" resultType="java.lang.Long">
        SELECT ifnull(sum(file_size), 0) FROM mooc_user_resource WHERE user_id = #{userId} AND del_flag = '0'
    </select>
</mapper> 