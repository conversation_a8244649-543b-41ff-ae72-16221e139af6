<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.edu.mapper.EduClassMapper">
    
    <resultMap type="EduClass" id="EduClassResult">
        <result property="eduClassId"    column="edu_class_id"    />
        <result property="academicYearId"    column="academic_year_id"    />
        <result property="schoolId"    column="school_id"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="majorId"    column="major_id"    />
        <result property="classCode"    column="class_code"    />
        <result property="className"    column="class_name"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEduClassVo">
        select edu_class_id, academic_year_id, school_id, organization_id, major_id, class_code, class_name, remarks, created_by, create_time, updated_by, update_time, del_flag from edu_class
    </sql>

    <select id="selectEduClassList" parameterType="EduClass" resultMap="EduClassResult">
        <include refid="selectEduClassVo"/>
        <where>  
            <if test="academicYearId != null "> and academic_year_id = #{academicYearId}</if>
            <if test="schoolId != null "> and school_id = #{schoolId}</if>
            <if test="organizationId != null "> and organization_id = #{organizationId}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="classCode != null  and classCode != ''"> and class_code = #{classCode}</if>
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
        </where>
    </select>
    
    <select id="selectEduClassByEduClassId" parameterType="Long" resultMap="EduClassResult">
        <include refid="selectEduClassVo"/>
        where edu_class_id = #{eduClassId}
    </select>

    <insert id="insertEduClass" parameterType="EduClass" useGeneratedKeys="true" keyProperty="eduClassId">
        insert into edu_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="academicYearId != null">academic_year_id,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="organizationId != null">organization_id,</if>
            <if test="majorId != null">major_id,</if>
            <if test="classCode != null and classCode != ''">class_code,</if>
            <if test="className != null and className != ''">class_name,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="academicYearId != null">#{academicYearId},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="organizationId != null">#{organizationId},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="classCode != null and classCode != ''">#{classCode},</if>
            <if test="className != null and className != ''">#{className},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEduClass" parameterType="EduClass">
        update edu_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="academicYearId != null">academic_year_id = #{academicYearId},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="organizationId != null">organization_id = #{organizationId},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="classCode != null and classCode != ''">class_code = #{classCode},</if>
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where edu_class_id = #{eduClassId}
    </update>

    <delete id="deleteEduClassByEduClassId" parameterType="Long">
        delete from edu_class where edu_class_id = #{eduClassId}
    </delete>

    <delete id="deleteEduClassByEduClassIds" parameterType="String">
        delete from edu_class where edu_class_id in 
        <foreach item="eduClassId" collection="array" open="(" separator="," close=")">
            #{eduClassId}
        </foreach>
    </delete>
</mapper>