package cn.dutp.shop.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: dutp
 * @date: 2025/2/10 9:43
 */
@Data
public class ReconcileLookDto {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;
    /**
     * 封面图
     */
    private String cover;

    /**
     * 教材名称
     */
    private String bookName;

    /**
     * isbn
     */
    private String isbn;

    /**
     * issn
     */
    private String issn;

    /**
     * 数量
     */
    private Integer bookQuantity;

    /**
     * 已使用购书码
     */
    private Integer useCode;

    /**
     * 未使用购书码
     *
     */
    private Integer noUseCode;

    /**
     * 总计
     *
     */
    private BigDecimal total;

    /**
     * 应付金额
     */
    private  BigDecimal shouldPay;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 学校名称
     */
    private String merchanName;

    /**
     * 书的售价
     */
    private  BigDecimal priceSale;

    /**
     * 折扣
     */
    private  BigDecimal discount;
}
