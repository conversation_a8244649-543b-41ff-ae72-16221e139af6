package cn.dutp.shop.mapper;

import cn.dutp.domain.DtbBookOrder;
import cn.dutp.shop.domain.DtbBookMerchant;
import cn.dutp.shop.domain.DtbUserInvoiceApply;
import cn.dutp.shop.domain.vo.DtbUserInvoiceApplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DUTP-DTB-035开票申请Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Repository
public interface DtbUserInvoiceApplyMapper extends BaseMapper<DtbUserInvoiceApply>
{

    @Update("update dtb_user_invoice_apply set apply_status = 2 where apply_id = #{applyId}")
    int changStatus(@Param("applyId") Long applyId);

    @Select("SELECT\n" +
            "\tuia.* ,\n" +
            "\tuif.invoice_code,\n" +
            "\tuif.file_url,\n" +
            "\tuif.file_name\n" +
            "FROM\n" +
            "\tdtb_user_invoice_apply AS uia\n" +
            "\tLEFT JOIN dtb_user_invoice_file AS uif ON uif.apply_id = uia.apply_id \n" +
            "WHERE\n" +
            "\tstatement_id = #{statementId} and uif.file_type = 0")
    List<DtbUserInvoiceApply> getStatementInvoice(Long statementId);

    /**
     * 教师学生端获查询发票信息
     *
     * @param dtbUserInvoiceApply 查询条件
     * @return 查询结果
     */
    public List<DtbUserInvoiceApplyVo> getInfoEducation(DtbUserInvoiceApply dtbUserInvoiceApply);

    @Select("SELECT\n" +
            "\tbo.*,\n" +
            "\tbs.statement_no \n" +
            "FROM\n" +
            "\tdtb_book_order AS bo\n" +
            "\tLEFT JOIN dtb_book_statement_order AS bso ON bso.order_id = bo.order_id\n" +
            "\tLEFT JOIN dtb_book_statement AS bs ON bs.statement_id = bso.statement_id \n" +
            "WHERE\n" +
            "\tbo.school_id = #{schoolId} \n" +
            "\tAND bs.statement_no = #{statementNo}")
    List<DtbBookOrder> selectBySchoolId(@Param("schoolId") Long schoolId, @Param("statementNo")String statementNo);

    @Select("select user_id from sys_user where user_name = #{userName}")
    Long selectUserIdByName(String createByName);
}
