package cn.dutp.shop.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.serialize.LongListToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * DUTP-DTB-034发票抬头对象 dtb_user_invoice_title
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@TableName("dtb_user_invoice_title")
public class DtbUserInvoiceTitle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long titleId;

    /** 结算单id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long statementId;

    /** 用户id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 订单id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableField(exist = false)
    private Long orderId;

    /**
     * 申请id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long applyId;

    /**
     * 文件id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileId;

    /** 发票类型1数电普票2数电专票 */
    @Excel(name = "发票类型")
    private Integer invoiceType;

    /** 1个人2企业 */
    @Excel(name = "抬头类型")
    private Integer titleType;

    /** 抬头名称 */
    @Excel(name = "抬头名称")
    private String titleName;

    /** 税号 */
    @Excel(name = "单位税号")
    private String taxNo;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registAddress;

    /** 注册手机号 */
    @Excel(name = "注册手机号")
    private String registTel;

    /** 开户行 */
    @Excel(name = "开户银行行")
    private String accountBank;

    /** 银行账户 */
    @Excel(name = "银行账户")
    private String accountNo;

    /** 用户姓名*/
    @TableField(exist = false)
    @Excel(name = "姓名")
    private String nickName;

    /** 用户账号*/
    @TableField(exist = false)
    @Excel(name = "账户")
    private String userName;

    /** 发票内容 1商品明细 2商品类别*/
    @Excel(name = "发票内容",readConverterExp = "1=商品明细,2=商品类别")
    @TableField(exist = false)
    private Integer applyType;

    /** 邮件*/
    @TableField(exist = false)
    private String email;

    /** 申请状态*/
    @TableField(exist = false)
    private Long applyStatus;

    /** 订单编号*/
    @TableField(exist = false)
    @Excel(name = "订单号")
    private String orderNo;

    /** 下单时间*/
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 实际支付金额*/
    @TableField(exist = false)
    private BigDecimal payAmount;

    /** 订单类型*/
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderType;

    /** 订单的类型*/
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer orderTypes;

    /** 发票状态 */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long invoiceStatus;

    /** 学校id*/
    @TableField(exist = false)
    private String schoolName;

    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /** 开票时间*/
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    @Excel(name = "教材名称")
    private String bookName;

    /**
     * ISBN序列号
     */
    @TableField(exist = false)
    @Excel(name = "ISBN")
    private String isbn;

    /**
     * 书籍数量
     */
    @TableField(exist = false)
    @Excel(name = "数量")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookQuantity;

    /**
     * 支付金额
     */
    @TableField(exist = false)
    @Excel(name = "支付金额")
    private BigDecimal priceOrderItem;

    /**
     * 发票编码
     */
    @TableField(exist = false)
    private String invoiceCode;

    /** 结算单号 */
    @TableField(exist = false)
    private String statementNo;

    /** 换开次数 */
    @TableField(exist = false)
    private Integer changeCount;

    /** 联系方式 */
    @TableField(exist = false)
    private String phonenumber;

    /** 开票金额 */
    @TableField(exist = false)
    private BigDecimal invoiceAmount;

    /** 收票人邮箱 */
    @TableField(exist = false)
    private String userEmail;

    /** 书商名称 */
    @TableField(exist = false)
    private String merchanName;

    /** 书商id */
    @TableField(exist = false)
    private String merchantId;

    /** 书商联系方式 */
    @TableField(exist = false)
    private String tel;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    @TableField(exist = false)
    private List<Date> createTimeList;

    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    @TableField(exist = false)
    private Integer schoolInvoiceType;

    @TableField(exist = false)
    private Integer schoolTitleType;

    @TableField(exist = false)
    private String schoolTitleName;

    @TableField(exist = false)
    private String schoolTaxNo;

    @TableField(exist = false)
    private String schoolRegistAddress;


    @TableField(exist = false)
    private String schoolRegistTel;

    @TableField(exist = false)
    private String schoolAccountBank;

    @TableField(exist = false)
    private String schoolAccountNo;

    @TableField(exist = false)
    private String schoolUserEmail;

    /** 开票金额 */
    @TableField(exist = false)
    private BigDecimal openPayAmount;

    /** 导出多选的id */
    @TableField(exist = false)
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> ids;

    /** 类型 1零售2批量采购 */
    @TableField(exist = false)
    private Integer type;

    /**
     * 发票数量
     */
    @TableField(exist = false)
    private Integer fileCount;


@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("titleId", getTitleId())
            .append("invoiceType", getInvoiceType())
            .append("titleType", getTitleType())
            .append("titleName", getTitleName())
            .append("taxNo", getTaxNo())
            .append("registAddress", getRegistAddress())
            .append("registTel", getRegistTel())
            .append("accountBank", getAccountBank())
            .append("accountNo", getAccountNo())
            .append("userId", getUserId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
        .toString();
        }
}
