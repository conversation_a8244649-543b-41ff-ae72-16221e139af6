<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookPurchaseCodeMapper">
    
    <resultMap type="DtbBookPurchaseCode" id="DtbBookPurchaseCodeResult">
        <result property="codeId"    column="code_id"    />
        <result property="bookId"    column="book_id"    />
        <result property="phone"    column="phone"    />
        <result property="code"    column="code"    />
        <result property="codeFrom"    column="code_from"    />
        <result property="state"    column="state"    />
        <result property="exchangeDate"    column="exchange_date"    />
        <result property="expiryDate"    column="expiry_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="bindDate"    column="bind_date"    />
        <result property="bookName"    column="book_name"    />
        <result property="orderId"    column="order_id"    />
        <result property="isbn"    column="isbn"    />
        <result property="issn"    column="issn"    />
        <result property="cover"    column="cover"    />
    </resultMap>

    <sql id="selectDtbBookPurchaseCodeVo">
        select code_id, book_id, phone, code, code_from, state, exchange_date, expiry_date, create_by, create_time, update_by, update_time, bind_date from dtb_book_purchase_code
    </sql>

    <select id="selectDtbBookPurchaseCodeList" parameterType="DtbBookPurchaseCode" resultMap="DtbBookPurchaseCodeResult">
        <include refid="selectDtbBookPurchaseCodeVo"/>
        <where>  
            <if test="bookId != null "> and book_id = #{bookId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="codeFrom != null "> and code_from = #{codeFrom}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="exchangeDate != null "> and exchange_date = #{exchangeDate}</if>
            <if test="expiryDate != null "> and expiry_date = #{expiryDate}</if>
            <if test="bindDate != null "> and bind_date = #{bindDate}</if>
        </where>
    </select>
    
    <select id="selectDtbBookPurchaseCodeByCodeId" parameterType="Long" resultMap="DtbBookPurchaseCodeResult">
        <include refid="selectDtbBookPurchaseCodeVo"/>
        where code_id = #{codeId}
    </select>

    <insert id="insertDtbBookPurchaseCode" parameterType="DtbBookPurchaseCode" useGeneratedKeys="true" keyProperty="codeId">
        insert into dtb_book_purchase_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bookId != null">book_id,</if>
            <if test="phone != null">phone,</if>
            <if test="code != null">code,</if>
            <if test="codeFrom != null">code_from,</if>
            <if test="state != null">state,</if>
            <if test="exchangeDate != null">exchange_date,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="bindDate != null">bind_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bookId != null">#{bookId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="code != null">#{code},</if>
            <if test="codeFrom != null">#{codeFrom},</if>
            <if test="state != null">#{state},</if>
            <if test="exchangeDate != null">#{exchangeDate},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="bindDate != null">#{bindDate},</if>
         </trim>
    </insert>

    <update id="updateDtbBookPurchaseCode" parameterType="DtbBookPurchaseCode">
        update dtb_book_purchase_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="bookId != null">book_id = #{bookId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="code != null">code = #{code},</if>
            <if test="codeFrom != null">code_from = #{codeFrom},</if>
            <if test="state != null">state = #{state},</if>
            <if test="exchangeDate != null">exchange_date = #{exchangeDate},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="bindDate != null">bind_date = #{bindDate},</if>
        </trim>
        where code_id = #{codeId}
    </update>

    <delete id="deleteDtbBookPurchaseCodeByCodeId" parameterType="Long">
        delete from dtb_book_purchase_code where code_id = #{codeId}
    </delete>

    <delete id="deleteDtbBookPurchaseCodeByCodeIds" parameterType="String">
        delete from dtb_book_purchase_code where code_id in 
        <foreach item="codeId" collection="array" open="(" separator="," close=")">
            #{codeId}
        </foreach>
    </delete>
    <select id="listEducation" parameterType="DtbBookPurchaseCode" resultMap="DtbBookPurchaseCodeResult">
        SELECT
            dbpc.code_id,
            dbpc.book_id,
            dbpc.phone,
            dbpc.`code`,
            dbpc.code_from,
            dbpc.state,
            dbpc.exchange_date,
            dbpc.expiry_date,
            dbpc.bind_date,
            db.book_name,
            db.isbn,
            db.issn,
            db.cover
        FROM
            dtb_book_purchase_code dbpc
            LEFT JOIN dtb_book db
            ON dbpc.book_id = db.book_id
        <where>
            <if test="userId != null">AND dbpc.user_id = #{userId}</if>
            <if test="bookId != null">AND dbpc.book_id = #{bookId}</if>
            <if test="bookName != null  and bookName != ''"> and (db.book_name like concat('%', #{bookName}, '%')</if>
            <if test="isbn != null  and isbn != ''"> or db.isbn like concat('%', #{isbn}, '%')</if>
            <if test="issn != null  and issn != ''"> or db.issn like concat('%', #{issn}, '%'))</if>
        </where>
        ORDER BY dbpc.create_time DESC
    </select>
    <select id="getInfoEducation" parameterType="DtbBookPurchaseCode" resultMap="DtbBookPurchaseCodeResult">
        SELECT
        code_id,
        book_id,
        phone,
        CODE,
        code_from,
        code_type,
        state,
        exchange_date,
        expiry_date,
        create_by,
        create_time,
        update_by,
        update_time,
        bind_date
        FROM
        dtb_book_purchase_code
        <where>
            <if test="codeId != null">AND code_id = #{codeId}</if>
            <if test="code != null">AND CODE = #{code}</if>
            <if test="state != null">AND state = #{state}</if>
        </where>
    </select>
    <select id="selectByOrderItemId" parameterType="Long" resultType="DtbBookPurchaseCode">
        select
            dpc.code_id
        from
            dtb_book_purchase_code dpc
        inner join
            dtb_book_order_code doc on dpc.code_id = doc.code_id
        inner join
            dtb_book_order_item doi on doi.order_item_id = doc.order_item_id
        where
            dpc.del_flag = 0 and dpc.state = 2 and is_frozen = 0 and doi.order_item_id = #{orderItemId}
    </select>
    <!--根据bookid查询库存状态且未冻结的code列表-->
    <select id="selectCodeIdListByBookId" parameterType="Long" resultType="Long">
        SELECT code_id
        FROM dtb_book_purchase_code
        where book_id = #{bookId}
          and state = 1
          and code_type = 1
          and is_frozen = 0
    </select>
    <!--将id的list中的购书码的状态改为2已绑定未兑换-->
    <update id="cancelCodeByCodeIdList" parameterType="Long">
        UPDATE dtb_book_purchase_code
        SET state = 2,
        update_by = #{updateBy},
        update_time = NOW(),
        bind_date = now()
        WHERE code_id IN
        <foreach collection='codeIdList' item='codeId' open='(' separator=',' close=')'>
            #{codeId}
        </foreach>
    </update>
    <!--将购书码表中id在集合中的:不是5作废状态的购书码且状态为冻结的数据，改为正常-->
    <update id="updateIsFrozenByCodeIdList">
        UPDATE dtb_book_purchase_code
        SET is_frozen = 0,
        update_by = #{updateBy},
        update_time = NOW()
        WHERE code_id IN
        <foreach collection='codeIdList' item='codeId' open='(' separator=',' close=')'>
            #{codeId}
        </foreach>
        AND is_frozen = 1
        AND state != 5
    </update>

    <!--将id的list中:不是5作废状态的购书码且已经冻结的购书码的状态改为5作废 冻结状态改为正常-->
    <update id="cancelIsFrozenByCodeIdList" parameterType="Long">
        UPDATE dtb_book_purchase_code
        SET state = 5,
        update_by = #{updateBy},
        update_time = NOW(),
        is_frozen = 0
        WHERE code_id IN
        <foreach collection='codeIdList' item='codeId' open='(' separator=',' close=')'>
            #{codeId}
        </foreach>
        AND is_frozen = 1
        AND state != 5
    </update>

    <select id="selectByOrderItemIds" parameterType="List" resultType="DtbBookPurchaseCode">
        select
            dpc.code_id
        from
            dtb_book_purchase_code dpc
        inner join
            dtb_book_order_code doc on dpc.code_id = doc.code_id
        inner join
            dtb_book_order_item doi on doi.order_item_id = doc.order_item_id
        where
            dpc.del_flag = 0 and is_frozen = 0
          and doi.order_item_id in
        <foreach collection='codeIdList' item='codeId' open='(' separator=',' close=')'>
            #{codeId}
        </foreach>
    </select>

    <select id="selectByOrderId" parameterType="Long" resultType="DtbBookPurchaseCode">
        select
            ppc.code,
            ppc.code_id,
            db.book_name,
            db.book_id,
            ppc.is_frozen,
            CASE
                WHEN isbn IS NOT NULL THEN isbn
                ELSE issn
            END AS isbn
        from
            dtb_book_purchase_code ppc
        inner join
            dtb_book_order_code dbc on dbc.code_id = ppc.code_id
        inner join
            dtb_book_order_item dbo on dbo.order_item_id = dbc.order_item_id
        left join
            dtb_book db on db.book_id = dbo.book_id and db.del_flag = 0
        where
            ppc.del_flag = 0 and ppc.state != 5 and dbo.order_id = #{orderId}
    </select>

    <!--根据orderId查询当前订单的子订单是否被全部作废-->
    <select id="selectItemIsAllVoid" parameterType="Long" resultType="Integer">
        SELECT CASE
                   WHEN COUNT(item.item_status) = 0 THEN 0 -- 如果没有记录，返回 0
                   WHEN COUNT(item.item_status) = SUM(CASE WHEN (item.item_status = 'cancel' or item.item_status = 'canceling') THEN 1 ELSE 0 END)
                       THEN 1
                   ELSE 0
                   END AS itemIsAllVoid
        FROM dtb_book_order dbo
                 LEFT JOIN
             dtb_book_order_item item
             ON
                 dbo.order_id = item.order_id
        WHERE dbo.order_id = #{orderId}
    </select>
    <select id="selectDtbBookById" parameterType="Long" resultType="cn.dutp.domain.DtbBook">
        SELECT
            b.book_id,
            b.book_name,
            b.author_label,
            b.author_value,
            b.cover,
            b.top_subject_id,
            b.second_subject_id,
            b.third_subject_id,
            b.forth_subject_id,
            b.isbn,
            b.issn,
            b.book_no,
            b.publish_date,
            b.current_version_id,
            b.last_version_id,
            b.shelf_time,
            b.unshelf_time,
            b.publish_organization,
            b.publish_status,
            b.shelf_state,
            b.school_id,
            b.house_id,
            b.book_type,
            b.master_flag,
            b.master_book_id,
            b.sold_quantity,
            b.read_quantity,
            b.download_count,
            b.price_counter,
            b.price_sale,
            b.min_discount,
            b.book_organize,
            b.topic_no,
            b.language_id,
            b.current_step_id,
            b.edition,
            b.table_number_type,
            b.image_number_type,
            b.dept_id
        FROM
            dtb_book AS b
        WHERE
            b.del_flag = '0'
          and b.book_id = #{bookId}
    </select>
    <select id="getUserInfo" parameterType="Long" resultType="cn.dutp.system.api.domain.DutpUser">
        SELECT
            dutp_user.user_id,
            dutp_user.school_id,
            dutp_user.speciality_id,
            dutp_user.academy_id,
            dutp_user.user_name,
            dutp_user.nick_name,
            dutp_user.user_no,
            dutp_user.user_type,
            dutp_user.email,
            dutp_user.phonenumber,
            dutp_user.sex,
            dutp_user.avatar,
            dutp_user.`password`,
            dutp_user.`status`,
            dutp_user.client_devices,
            dutp_user.login_ip,
            dutp_user.login_date,
            dutp_user.remark
        FROM
            dutp_user
        WHERE
            dutp_user.del_flag = '0'
          and dutp_user.user_id = #{userId}
    </select>
</mapper>