<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.shop.mapper.DtbBookStatementMapper">
    <select id="selectStatementList" resultType="cn.dutp.shop.domain.DtbBookStatement">
        SELECT
        DISTINCT
        bs.statement_id,
        bs.statement_no,
        bs.statement_status,
        bs.invoice_status,
        bs.create_by,
        bs.create_time,
        GROUP_CONCAT(DISTINCT sa.area_name) as area_name,
        bs.school_id,
        bm.merchant_id,
        SUM(DISTINCT bo.price) AS total_price,
        SUM(DISTINCT bo.pay_amount) AS total_pay_amount,
        SUM(boi.book_quantity) AS total_book_quantity,
        GROUP_CONCAT(DISTINCT s.school_name) AS school_name,
        GROUP_CONCAT(DISTINCT bm.merchan_name) AS unique_merchan_names
        FROM
        dtb_book_statement AS bs
        LEFT JOIN dtb_book_statement_order AS bso ON bso.statement_id = bs.statement_id
        LEFT JOIN dtb_book_order AS bo ON bo.order_id = bso.order_id
        LEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id
        LEFT JOIN dutp_school AS s ON s.school_id = bo.school_id
        LEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id
        LEFT JOIN dutp_sale_area AS sa ON sa.area_id = bo.area_id
        <where>
            <!-- 非聚合字段的过滤条件放在 WHERE 子句中 -->
            <if test="statementNo != null and statementNo != ''">AND bs.statement_no LIKE CONCAT('%', #{statementNo}, '%')</if>
            <if test="statementStatus != null and statementStatus != ''">AND bs.statement_status LIKE CONCAT('%', #{statementStatus}, '%')</if>
            <if test="invoiceStatus != null and invoiceStatus != ''">AND bs.invoice_status LIKE CONCAT('%', #{invoiceStatus}, '%')</if>
            <if test="startTime != null">AND bs.create_time >= #{startTime}</if>
            <if test="endTime != null">AND bs.create_time &lt;= #{endTime}</if>
            AND bs.del_flag = 0
        </where>
        GROUP BY
        bs.statement_id,
        bs.statement_no,
        bs.statement_status,
        bs.invoice_status,
        bs.create_by,
        bs.create_time,
        bs.school_id,
        bm.merchant_id
        <!-- 只有在有聚合字段过滤条件时才添加 HAVING 子句 -->
        <if test="areaName != null and areaName != '' or merchanName != null and merchanName != '' or schoolName != null and schoolName != ''">
            HAVING
            <if test="areaName != null and areaName != ''">GROUP_CONCAT(DISTINCT sa.area_name) LIKE CONCAT('%', #{areaName}, '%')</if>
            <if test="merchanName != null and merchanName != ''">
                <if test="areaName != null and areaName != ''">AND</if>
                GROUP_CONCAT(DISTINCT bm.merchan_name) LIKE CONCAT('%', #{merchanName}, '%')
            </if>
            <if test="schoolName != null and schoolName != ''">
                <if test="areaName != null and areaName != '' or merchanName != null and merchanName != ''">AND</if>
                GROUP_CONCAT(DISTINCT s.school_name) LIKE CONCAT('%', #{schoolName}, '%')
            </if>
        </if>
        ORDER BY
        bs.create_time DESC
    </select>
    <select id="selectReconcileList" resultType="cn.dutp.shop.domain.dto.ReconciliationDto">
        SELECT DISTINCT
        bo.order_id,
        bo.order_no,
        ( SELECT SUM( book_quantity ) FROM dtb_book_order_item WHERE order_id = bo.order_id ) AS payTotal,
        bo.price AS goodsTotals,
        bo.pay_amount AS shouldPay,
        (SELECT
        Count(* )
        FROM
        dtb_book_order_code AS boc
        LEFT JOIN dtb_book_order AS bo ON bo.order_id = boc.order_id
        LEFT JOIN dtb_book AS b ON b.book_id = boc.book_id
        WHERE
        boc.order_id = boi.order_id
        AND state = 3
        ) AS useCode,
        (
        SELECT
        Count(* )
        FROM
        dtb_book_order_code AS boc
        LEFT JOIN dtb_book_order AS bo ON bo.order_id = boc.order_id
        LEFT JOIN dtb_book AS b ON b.book_id = boc.book_id
        WHERE
        boc.order_id = boi.order_id
        AND state = 2
        ) AS noUseCode,
        bo.order_type,
        bm.merchan_name,
        bm.merchant_id,
        bo.school_id,
        s.school_name,
        u.nick_name,
        sa.area_name,
        bo.create_time
        FROM
        dtb_book_order AS bo
        LEFT JOIN dtb_book_order_item AS boi ON boi.order_id = bo.order_id
        LEFT JOIN dtb_book_merchant AS bm ON bm.merchant_id = bo.merchant_id
        LEFT JOIN dutp_school AS s ON s.school_id = bo.school_id
        LEFT JOIN sys_user AS u ON u.user_id = bo.operator_id
        LEFT JOIN dutp_sale_area AS sa ON sa.area_id = bo.area_id

        <where>
            <if test="orderNo != null and orderNo != ''">AND bo.order_no LIKE CONCAT('%', #{orderNo},
                '%')
            </if>
            <if test="schoolName != null and schoolName != ''">AND s.school_name LIKE CONCAT('%',
                #{schoolName}, '%')
            </if>
            <if test="merchanName != null and merchanName != ''">AND bm.merchan_name LIKE CONCAT('%',
                #{merchanName}, '%')
            </if>
            <if test="areaName != null and areaName != ''">AND sa.area_name LIKE CONCAT('%',
                #{areaName}, '%')
            </if>
            <if test="nickName != null and nickName != ''">AND u.nick_name LIKE CONCAT('%',
                #{nickName}, '%')
            </if>
            <if test="startTime != null">
                AND bo.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND bo.create_time &lt;= #{endTime}
            </if>
            and bo.order_type IN  (2,3,5)
            AND bo.order_status = 'settlement'
            AND bo.deleted = 1
            and bo.order_id not in (select order_id from dtb_book_statement_order)
        </where>

    </select>
</mapper>