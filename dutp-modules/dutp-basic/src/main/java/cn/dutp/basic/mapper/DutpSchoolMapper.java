package cn.dutp.basic.mapper;

import cn.dutp.basic.domain.DutpSchool;
import cn.dutp.basic.domain.dto.DutpAcademyDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 学校管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Repository
public interface DutpSchoolMapper extends BaseMapper<DutpSchool>
{

    List<DutpSchool> selectDutpSchoolList(DutpSchool dutpSchool);

    List<DutpSchool> listNoPage(DutpSchool dutpSchool);


    List<DutpSchool> selectAcademyBySchool(DutpSchool dutpSchool);

    List<DutpSchool> selectAcademyBySchoolNoPage(DutpSchool dutpSchool);

    List<DutpSchool> selectSubjectList (DutpSchool dutpSchool);

    List<DutpSchool> listNoPageOfBook(DutpSchool dutpSchool);

    @Select("select * from dutp_school\n" +
            "        where\n" +
            "             data_type = 1 and parent_id = #{schoolId}  and del_flag = 0")
    List<DutpAcademyDto> selectAcademyExport(DutpSchool dutpSchool);

    @Select("select * from dutp_school where data_type = 1 and parent_id = #{parentId}  and del_flag = 0")
    List<DutpSchool> selectEduAcademyList(DutpSchool dutpSchool);
}
