package cn.dutp.basic.domain;

import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 学年管理
 *
 * @author: dutp
 * @date: 2025/7/3 14:41
 */
@Data
@TableName("edu_academic_year")
public class EduAcademicYear extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *学年ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)

    private Long academicYearId;

    /**
     *学校ID
     */
    private Long schoolId;

    /**
     *学年名称
     */
    private String name;

    /**
     *学年层级 0专科, 1本科, 2研究生, 3博士生
     */
    private String level;

    /**
     *学年开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     *学年结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     *学年备注
     */
    private String remarks;

    private Long createdBy;

    private Long updatedBy;

    @TableField(exist = false)
    private String createBy;

    @TableField(exist = false)
    private String updateBy;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("academicYearId", getAcademicYearId())
                .append("schoolId", getSchoolId())
                .append("name", getName())
                .append("level", getLevel())
                .append("startDate", getStartDate())
                .append("endDate", getEndDate())
                .append("remarks", getRemarks())
                .append("createdBy", getCreatedBy())
                .append("updatedBy", getUpdatedBy())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }

}
