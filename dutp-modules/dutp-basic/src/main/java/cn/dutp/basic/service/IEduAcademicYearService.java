package cn.dutp.basic.service;

import cn.dutp.basic.domain.EduAcademicYear;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 学年管理接口
 *
 * <AUTHOR>
 * @description: TODO
 * @date 2025/7/3 14:37
 */

public interface IEduAcademicYearService extends IService<EduAcademicYear> {

    /**
     * 查询列表
     * @param eduAcademicYear 学年对象
      * @return 结果
     */
    public List<EduAcademicYear> selectAcademicYearList(EduAcademicYear eduAcademicYear);

    /**
     * 获取学年信息
     * @param academicYearId 学年id
     * @return
     */
    public EduAcademicYear getAcademicYearById(Long academicYearId);

    /**
     * 添加学年信息
     * @param eduAcademicYear 对象
     * @return 结果
     */
    public boolean addAcademicYear(EduAcademicYear eduAcademicYear);

    /**
     * 编辑学年信息
     * @param eduAcademicYear 对象
     * @return 结果
     */
    public boolean editAcademicYear(EduAcademicYear eduAcademicYear);

    /**
     * 删除学年信息
     * @param academicYearId id
     * @return 结果
     */
    public boolean deleteAcademicYear(Long academicYearId);

    /**
     * 复制学年信息
     *
     * @param eduAcademicYear@return 结果
     */
    public boolean copyAcademicYear(EduAcademicYear eduAcademicYear);




}
