package cn.dutp.basic.service.impl;


import cn.dutp.basic.domain.EduAcademicYear;
import cn.dutp.basic.mapper.EduAcademicYearMapper;
import cn.dutp.basic.service.IEduAcademicYearService;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: dutp
 * @date: 2025/7/3 14:39
 */
@Service
public class EduAcademicYearServiceImpl extends ServiceImpl<EduAcademicYearMapper, EduAcademicYear> implements IEduAcademicYearService {
    @Override
    public List<EduAcademicYear> selectAcademicYearList(EduAcademicYear eduAcademicYear) {
        LambdaQueryWrapper<EduAcademicYear> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(eduAcademicYear.getName())) {
            lambdaQueryWrapper.like(EduAcademicYear::getName
                    ,eduAcademicYear.getName());
        }
        if(ObjectUtil.isNotEmpty(eduAcademicYear.getLevel())) {
            lambdaQueryWrapper.eq(EduAcademicYear::getLevel
                    ,eduAcademicYear.getLevel());
        }

        return this.list(lambdaQueryWrapper);
    }

    @Override
    public EduAcademicYear getAcademicYearById(Long academicYearId) {
        return this.getById(academicYearId);
    }


    @Override
    public boolean addAcademicYear(EduAcademicYear eduAcademicYear) {
        return this.save(eduAcademicYear);
    }

    @Override
    public boolean editAcademicYear(EduAcademicYear eduAcademicYear) {
        return this.updateById(eduAcademicYear);
    }

    @Override
    public boolean deleteAcademicYear(Long academicYearId) {
        return this.removeById(academicYearId);
    }

    @Override
    public boolean copyAcademicYear(EduAcademicYear eduAcademicYear) {
        return false;
    }
}
