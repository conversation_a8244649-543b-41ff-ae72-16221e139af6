package cn.dutp.basic.controller;

import cn.dutp.basic.domain.EduAcademicYear;
import cn.dutp.basic.service.IEduAcademicYearService;
import cn.dutp.common.core.web.controller.BaseController;
import cn.dutp.common.core.web.domain.AjaxResult;
import cn.dutp.common.core.web.page.TableDataInfo;
import cn.dutp.common.log.annotation.Log;
import cn.dutp.common.log.enums.BusinessType;
import cn.dutp.common.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: dutp
 * @date: 2025/7/3 14:35
 */
@RestController
@RequestMapping("academicYear")
public class EduAcademicYearController extends BaseController {

    @Autowired
    private IEduAcademicYearService academicYearService;

    /**
     * 查询学年列表
     * @param eduAcademicYear 对象
     * @return 结果
     */
    @RequiresPermissions("basic:year:list")
    @GetMapping("/list")
    public TableDataInfo list(EduAcademicYear eduAcademicYear) {
        startPage();
        List<EduAcademicYear> list = academicYearService.selectAcademicYearList(eduAcademicYear);
        return getDataTable(list);
    }

    /**
     * 学年列表用于下拉框
     * @param eduAcademicYear
     * @return
     */
    @GetMapping("/yearListNoPage")
    public AjaxResult yearListNoPage(EduAcademicYear eduAcademicYear) {
        startPage();
        List<EduAcademicYear> list = academicYearService.selectAcademicYearList(eduAcademicYear);
        return success(list);
    }

    /**
     * 获取学年详细信息
     */
    @RequiresPermissions("basic:year:query")
    @GetMapping(value = "/{academicYearId}")
    public AjaxResult getInfo(@PathVariable("academicYearId") Long academicYearId) {
        return success(academicYearService.getAcademicYearById(academicYearId));

    }

    /**
     * 新增学年
     */
    @RequiresPermissions("basic:year:add")
    @Log(title = "新增学年", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EduAcademicYear eduAcademicYear)
    {
        return toAjax(academicYearService.addAcademicYear(eduAcademicYear));
    }

    /**
     * 修改学年
     */
    @RequiresPermissions("basic:year:edit")
    @Log(title = "修改学年", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EduAcademicYear eduAcademicYear)
    {
        return toAjax(academicYearService.editAcademicYear(eduAcademicYear));
    }

    /**
     * 删除学年
     */
    @RequiresPermissions("basic:year:remove")
    @Log(title = "删除学年", businessType = BusinessType.DELETE)
    @DeleteMapping("/{academicYearId}")
    public AjaxResult remove(@PathVariable Long academicYearId)
    {
        return toAjax(academicYearService.deleteAcademicYear(academicYearId));
    }

    /**
     * 复制学年
     */
    @RequiresPermissions("basic:year:copy")
    @Log(title = "复制学年", businessType = BusinessType.DELETE)
    @PostMapping("/{academicYearId}")
    public AjaxResult copyYear(@RequestBody EduAcademicYear eduAcademicYear)
    {
        return toAjax(academicYearService.copyAcademicYear(eduAcademicYear));
    }
}
