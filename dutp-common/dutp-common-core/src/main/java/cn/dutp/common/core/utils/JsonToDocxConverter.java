package cn.dutp.common.core.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.http.HttpRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.*;
import java.math.BigInteger;

@Slf4j
public class JsonToDocxConverter {

    private final static int DEFAULT_DPI = 96;

    private final static String key = "XNRB1NsC63BkmNTsADgeZg==";
    // accessKeyId accessKeySecret 密钥
    // private final static String key = "pVwKSycbZvgJ+jrNGsDA8g==";

    public static void main(String[] args) throws Exception {
        // System.out.println(Base64.encode(SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded()));
        // SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, Base64.decode(key));
        // // byte[] encrypt = aes.encrypt("******************************");
        // log.error("加密后:{}", aes.encryptHex("LTAI5tBsVzXTEWh61kC7BWrD"));
        // XWPFDocument document = new XWPFDocument();
        // ObjectMapper mapper = new ObjectMapper();
        // JsonNode docNode = mapper.readTree(new File("output.json"));
        //
        // processDocument(document, docNode);
        //
        // try (FileOutputStream out = new FileOutputStream("output.docx")) {
        //     document.write(out);
        // }
        // document.close();
        // System.out.println(aes.decryptStr("4026c7ed51fc6e2c4f17e3434a0c2c9a7adbe1333e2b59465f6951a37b4ff494", CharsetUtil.CHARSET_UTF_8));
        String aesKey = "pVwKSycbZvgJ+jrNGsDA8g==";
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, Base64.decode(aesKey));
        log.error("accessKeyId加密后:{}", aes.encryptHex("LTAI5tHt5x941chtrxg2NBEX"));
        log.error("accessKeySecret加密后:{}", aes.encryptHex("******************************"));
    }

    /**
     * 根据json生成word
     *
     * @param jsonStr
     * @param fileName
     */
    public static void analyzeJson(String jsonStr, String fileName) throws IOException {
        XWPFDocument document = null;
        try {
            document = new XWPFDocument();
            ObjectMapper mapper = new ObjectMapper();
            JsonNode docNode = mapper.readTree(jsonStr);
            processDocument(document, docNode);

            File file = new File(fileName);
            // 检查文件所在的目录是否存在，如果不存在则创建
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    throw new IOException("无法创建目录: " + parentDir.getAbsolutePath());
                }
            }
            // 如果文件不存在，则创建新文件
            if (!file.exists()) {
                if (!file.createNewFile()) {
                    throw new IOException("无法创建文件: " + file.getAbsolutePath());
                }
            }

            try (FileOutputStream out = new FileOutputStream(file)) {
                document.write(out);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    log.info("关闭文档失败: {}", e);
                }
            }
        }
    }

    /**
     * 处理文档
     *
     * @param document
     * @param docNode
     */
    private static void processDocument(XWPFDocument document, JsonNode docNode) {
        JsonNode pageNodeList = docNode.path("content");
        boolean isFirstPage = true;
        for (JsonNode pageNode : pageNodeList) {

            // 添加分页符
            if (!isFirstPage) {
                XWPFParagraph paragraph = document.createParagraph();
                paragraph.setPageBreak(true);
            }

            JsonNode nodeList = pageNode.path("content");

            if (nodeList.isMissingNode()) {
                continue;
            }

            for (JsonNode node : nodeList) {
                handleNode(document, null, null, node);
            }
            isFirstPage = false;
        }
    }

    /**
     * 处理节点
     *
     * @param document
     * @param node
     */
    private static void handleNode(XWPFDocument document, XWPFTableCell cell, XWPFParagraph paragraph, JsonNode node) {
        String type = node.path("type").asText();

        if ("table".equals(type) || "tablePlus".equals(type)) {
            log.info("表格节点：{}", type);
            processTable(document, node);
        } else if ("orderedList".equals(type) || "bulletList".equals(type)) {
            log.info("列表节点：{}", type);
            processList(document, cell, node);
        } else if ("blockquote".equals(type)) {
            // 引用节点
            log.info("引用节点：{}", type);
            processBlockquote(document, cell, paragraph, node);
        } else if ("layoutColumn".equals(type)) {
            // 布局节点
            log.info("布局节点：{}", type);
            processLayoutColumn(document, cell, paragraph, node);
        } else if ("heading".equals(type)) {
            log.info("标题节点：{}", type);
            processHeading(document, paragraph, node);
        } else if ("paragraph".equals(type)) {
            log.info("普通段落节点：{}", type);
            processParagraph(document, paragraph, node);
        } else if ("resourceCover".equals(type) || "papers".equals(type) || "questions".equals(type) || "video".equals(type) || "audio".equals(type) || "file".equals(type) || "papers".equals(type)) {
            // 资源节点---->要生成二维码
            log.info("资源节点：{}", type);
            processResource(document, paragraph, node);
        } else if ("image".equals(type) || "imageGallery".equals(type) || "imageLayout".equals(type) || "paperWrapping".equals(type)) {
            // 图片节点
            log.info("图片节点：{}", type);
            if (cell == null) {
                processImage(document, paragraph, node, 20, 20);
            } else {
                processImage(document, paragraph, node, 10, 10);
            }
        } else if ("codeBlock".equals(type)) {
            // 代码块节点
            log.info("代码块节点：{}", type);
            processCodeBlock(document, paragraph, node);
        } else if ("chapterHeader".equals(type)) {
            // TODO 章头节点
            log.info("章头节点：{}", type);
            processLayoutImg(document, paragraph, node);
        } else if ("jointHeader".equals(type)) {
            // TODO 节头节点
            log.info("节头节点：{}", type);
            processLayoutImg(document, paragraph, node);
        } else if ("horizontalRule".equals(type)) {
            // TODO 分割线节点
            log.info("分割线节点：{}", type);
        } else {
            log.info("暂不支持节点：{}", type);
        }
    }

    /**
     * 布局图片
     *
     * @param doc
     * @param paragraph
     * @param node
     */
    private static void processLayoutImg(XWPFDocument doc, XWPFParagraph paragraph, JsonNode node) {
        if (paragraph == null) {
            paragraph = doc.createParagraph();
        }
        JsonNode attrs = node.path("attrs");
        String imageUrl = attrs.path("bgImg").asText();
        String text = attrs.path("title").asText();
        XWPFRun run = paragraph.createRun();
        run.setText(text);

        insertImage(paragraph, imageUrl, "章头", 35, 10);
    }

    /**
     * 处理代码块
     *
     * @param document
     * @param node
     */
    private static void processCodeBlock(XWPFDocument document, XWPFParagraph paragraph, JsonNode node) {
        if (paragraph == null) {
            paragraph = document.createParagraph();
        }
        XWPFRun run = paragraph.createRun();
        run.setText(node.path("attrs").path("code").asText());
    }

    /**
     * 处理资源 -->生成二维码
     *
     * @param document
     * @param node
     */
    private static void processResource(XWPFDocument document, XWPFParagraph paragraph, JsonNode node) {

        String type = node.path("type").asText();
        if (paragraph == null) {
            paragraph = document.createParagraph();
        }
        // TODO 生成二维码
        String fileName = RandomUtil.randomString(10) + ".png";
        String resourceObj = node.toString();
        // 加密 跳转路径
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, Base64.decode(key));
        byte[] encrypt = aes.encrypt(resourceObj);
        resourceObj = Base64.encode(encrypt);

        try {
            // 生成二维码
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            QrCodeUtil.generate(resourceObj, 300, 300, "png", byteArrayOutputStream);
            XWPFRun run = paragraph.createRun();
            run.addPicture(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), XWPFDocument.PICTURE_TYPE_PNG, fileName, Units.toEMU(300), Units.toEMU(300));
            if ("questions".equals(type)) {
                JsonNode attrs = node.get("attrs");
                JsonNode questionsList = attrs.get("questionsList");
                JsonNode question = questionsList.get(0);
                int questionType = question.get("questionType").asInt();
                XWPFParagraph paragraph1 = document.createParagraph();
                paragraph1.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun run1 = paragraph1.createRun();
                run1.setText(getQuestionTypeName(questionType));
            }
        } catch (Exception e) {
            log.error("type:{},组件资源，生成二维码失败：,", type, e);
        }


    }

    /**
     * 获取题型名称
     *
     * @param questionType
     * @return
     */
    private static String getQuestionTypeName(int questionType) {
        // 题型数据 小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程
        String questionTypeName = "未知题型";
        switch (questionType) {
            case 1:
                questionTypeName = "单选";
                break;
            case 2:
                questionTypeName = "多选";
                break;
            case 3:
                questionTypeName = "填空";
                break;
            case 4:
                questionTypeName = "排序";
                break;
            case 5:
                questionTypeName = "连线";
                break;
            case 6:
                questionTypeName = "简答";
                break;
            case 7:
                questionTypeName = "判断";
                break;
            case 8:
                questionTypeName = "编程";
                break;

        }
        return questionTypeName;
    }

    /**
     * 处理图片
     *
     * @param document
     * @param node
     */
    private static void processImage(XWPFDocument document, XWPFParagraph paragraph, JsonNode node, int w, int h) {
        if (paragraph == null) {
            paragraph = document.createParagraph();
        }

        paragraph.setAlignment(ParagraphAlignment.CENTER);
        String type = node.path("type").asText();

        JsonNode attrs = node.path("attrs");

        if ("imageGallery".equals(type)) {
            // 画廊单独处理
            JsonNode imgList = attrs.path("imgList");
            for (JsonNode imgNode : imgList) {
                insertImage(paragraph, imgNode.path("src").asText(), imgNode.path("name").asText(), w, h);
            }
        } else {
            insertImage(paragraph, attrs.path("src").asText(), attrs.path("name").asText(), w, h);
        }
        if (attrs.path("isShowImageTitle").asInt(0) == 1) {
            XWPFParagraph paragraph1 = document.createParagraph();
            paragraph1.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph1.createRun();
            String pre = attrs.path("isShowNo").asInt(0) == 1 ? attrs.path("number").asText("图1") : "";
            run.setText(pre + attrs.path("imageTitle").asText("图片标题"));
        }
    }

    /**
     * 处理标题
     *
     * @param document
     * @param node
     */
    private static void processHeading(XWPFDocument document, XWPFParagraph paragraph, JsonNode node) {
        if (paragraph == null) {
            paragraph = document.createParagraph();
        }

        setParagraphStyle(node, paragraph);

        // 处理行内节点
        handleInLineNode(paragraph, node, true);
    }

    /**
     * 自定义标题样式的字体大小
     *
     * @param leve
     * @return
     */
    private static int getHeadFontSize(int leve) {
        if (leve == 1) {
            return 30;
        } else if (leve == 2) {
            return 24;
        } else if (leve == 3) {
            return 18;
        } else if (leve == 4) {
            return 14;
        } else if (leve == 5) {
            return 12;
        } else if (leve == 6) {
            return 10;
        }
        return 0;
    }

    /**
     * 处理表格
     *
     * @param doc
     * @param tableNode
     */
    private static void processTable(XWPFDocument doc, JsonNode tableNode) {
        JsonNode tablePlusAttrs = null;
        if ("tablePlus".equals(tableNode.path("type").asText())) {
            tablePlusAttrs = tableNode.path("attrs");
            tableNode = tableNode.path("content").get(0);
        }
        // 处理行数据
        JsonNode tableRowList = tableNode.path("content");

        int rowLen = 0;
        int cellLen = 0;
        int rowInd = 0;
        int cellInd = 0;
        int nextMergeNum = 0;
        // 计算表格大小
        for (JsonNode rowNode : tableRowList) {
            JsonNode cellNodeList = rowNode.path("content");
            cellInd = 0;
            for (JsonNode cellNode : cellNodeList) {
                JsonNode attrs = cellNode.path("attrs");
                if (rowInd == 0) {
                    int colspan = attrs.path("colspan").asInt(1);
                    cellLen += colspan;
                }
                if (cellInd == 0 && rowInd == nextMergeNum) {
                    int rowspan = attrs.path("rowspan").asInt(1);
                    rowLen += rowspan;
                    nextMergeNum = rowInd + rowspan;
                }
                cellInd++;
            }
            rowInd++;
        }
        log.info("创建表格{}行，{}列的表格", rowLen, cellLen);
        XWPFTable table = doc.createTable(rowLen, cellLen);

        // 设置表格样式
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) tblPr = table.getCTTbl().addNewTblPr();

        // 设置表格宽度
        CTTblWidth width = tblPr.addNewTblW();
        width.setType(STTblWidth.DXA);
        width.setW(BigInteger.valueOf(10000));

        int rowIndex = 0;
        for (JsonNode rowNode : tableRowList) {
            XWPFTableRow row = table.getRow(rowIndex);
            if (row == null) {
                row = table.createRow();
            }

            // 处理单元格
            int cellIndex = 0;
            JsonNode cellNodeList = rowNode.path("content");
            for (JsonNode cellNode : cellNodeList) {
                log.info("行：{}，列：{},列数：{}", rowIndex, cellIndex, row.getTableCells().size());
                JsonNode attrs = cellNode.path("attrs");
                int colspan = attrs.path("colspan").asInt(1);
                int rowspan = attrs.path("rowspan").asInt(1);

                XWPFTableCell cell = row.getCell(cellIndex);
                if (cell == null) cell = row.createCell();

                while (checkIsMerge(cell)) {
                    log.info("{},V:{},H{}", cellIndex, cell.getCTTc().getTcPr().isSetVMerge(), cell.getCTTc().getTcPr().isSetHMerge());
                    cellIndex++;
                    cell = row.getCell(cellIndex);
                }

                if (colspan != 1 && rowspan == 1) {
                    cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    handleCellMerge(row, cellIndex, colspan, row);
                }

                if (rowspan != 1 && colspan == 1) {
                    cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
                    for (int curRowIndex = rowIndex + 1; curRowIndex <= rowIndex - 1 + rowspan; curRowIndex++) {
                        XWPFTableRow nextMergeRow = table.getRow(curRowIndex);
                        if (nextMergeRow == null) {
                            nextMergeRow = table.createRow();
                        }
                        XWPFTableCell curMergeCell = nextMergeRow.getCell(cellIndex);
                        curMergeCell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
                    }

                }

                if (rowspan != 1 && colspan != 1) {
                    // 列合并
                    cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
                    for (int curRowIndex = rowIndex; curRowIndex <= rowIndex - 1 + rowspan; curRowIndex++) {
                        XWPFTableRow nextMergeRow = table.getRow(curRowIndex);
                        if (nextMergeRow == null) {
                            nextMergeRow = table.createRow();
                        }
                        handleCellMerge(row, cellIndex, colspan, nextMergeRow);
                    }
                    // 行合并
                    cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
                    for (int curRowIndex = rowIndex + 1; curRowIndex <= rowIndex - 1 + rowspan; curRowIndex++) {
                        XWPFTableRow nextMergeRow = table.getRow(curRowIndex);
                        if (nextMergeRow == null) {
                            nextMergeRow = table.createRow();
                        }
                        int curCellIndex = cellIndex;
                        XWPFTableCell curMergeCell = null;
                        while (curCellIndex < cellIndex + colspan) {
                            curMergeCell = nextMergeRow.getCell(curCellIndex);
                            if (curMergeCell == null) {
                                curMergeCell = nextMergeRow.createCell();
                            }
                            curMergeCell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
                            curCellIndex++;
                        }
                    }
                }

                // 处理单元格内容
                cell.removeParagraph(0);
                for (JsonNode content : cellNode.path("content")) {
                    processTableCell(doc, cell, content);
                }
                cellIndex++;
            }
            log.info("行：{}, 列数：{}", rowIndex, row.getTableCells().size());
            rowIndex++;
        }

        // 标题和表号
        if (tablePlusAttrs != null && tablePlusAttrs.path("isShowTitle").asBoolean(false) == true) {
            XWPFParagraph paragraph1 = doc.createParagraph();
            paragraph1.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph1.createRun();
            String pre = tablePlusAttrs.path("isShowNo").asInt(0) == 1 ? tablePlusAttrs.path("number").asText("表1") : "";
            run.setText(pre + tablePlusAttrs.path("name").asText("表格标题"));
        }

    }

    /**
     * 列单元格合并
     *
     * @param row
     * @param cellIndex
     * @param colspan
     * @param nextMergeRow
     */
    private static void handleCellMerge(XWPFTableRow row, int cellIndex, int colspan, XWPFTableRow nextMergeRow) {
        for (int curCellIndex = cellIndex + 1; curCellIndex <= cellIndex - 1 + colspan; curCellIndex++) {
            XWPFTableCell nextMergeCell = nextMergeRow.getCell(curCellIndex);
            if (nextMergeCell == null) {
                nextMergeCell = row.createCell();
            }
            nextMergeCell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
        }
    }

    /**
     * 检测是否被合并
     *
     * @param cell
     * @return
     */
    private static boolean checkIsMerge(XWPFTableCell cell) {
        CTTc ctTc = cell.getCTTc();
        CTTcPr tcPr = ctTc.getTcPr();
        if (tcPr == null) {
            tcPr = ctTc.addNewTcPr();
        }
        return tcPr.isSetVMerge() || tcPr.isSetHMerge();
    }

    /**
     * 表格单元格处理
     *
     * @param doc
     * @param node
     */
    private static void processTableCell(XWPFDocument doc, XWPFTableCell cell, JsonNode node) {
        handleNode(doc, cell, cell.addParagraph(), node);
    }

    /**
     * 处理分栏布局
     *
     * @param doc
     * @param paragraph
     * @param node
     */
    private static void processLayoutColumn(XWPFDocument doc, XWPFTableCell cell, XWPFParagraph paragraph, JsonNode node) {
        for (JsonNode content : node.path("content")) {
            for (JsonNode jsonNode : content.path("content")) {
                handleNode(doc, cell, paragraph, jsonNode);
            }
        }
    }

    /**
     * 处理引用
     *
     * @param doc
     * @param cell
     * @param paragraph
     * @param node
     */
    private static void processBlockquote(XWPFDocument doc, XWPFTableCell cell, XWPFParagraph paragraph, JsonNode node) {
        for (JsonNode content : node.path("content")) {
            handleNode(doc, cell, paragraph, content);
        }
    }

    /**
     * 处理段落
     *
     * @param doc
     * @param paraNode
     */
    private static void processParagraph(XWPFDocument doc, XWPFParagraph paragraph, JsonNode paraNode) {
        if (paragraph == null) {
            paragraph = doc.createParagraph();
        }

        setParagraphStyle(paraNode, paragraph);

        // 处理行内节点
        handleInLineNode(paragraph, paraNode, false);
    }

    /**
     * 设置段落样式
     *
     * @param paraNode
     * @param paragraph
     */
    private static void setParagraphStyle(JsonNode paraNode, XWPFParagraph paragraph) {
        String type = paraNode.path("type").asText();
        JsonNode attrs = paraNode.path("attrs");
        // 设置段落对齐
        String alignment = attrs.path("textAlign").asText("LEFT");
        try {
            paragraph.setAlignment(ParagraphAlignment.valueOf(alignment.toUpperCase()));
        } catch (IllegalArgumentException e) {
            log.info("设置段落对齐失败：{}", alignment, e);
        }

        // 设置缩进
        if (attrs.hasNonNull("indent")) {
            int indent = attrs.path("indent").asInt();
            log.info("设置缩进：{}, {}", type, indent);
            paragraph.setIndentationLeft(pxToTwip(indent));
        }

        // 设置行高
        if (attrs.hasNonNull("lineHeight")) {
            double lineHeight = attrs.path("lineHeight").asDouble();
            log.info("设置行高：{}, {}", type, lineHeight);
            paragraph.setSpacingBetween(lineHeight);
        }

        // 段落间距
        if (attrs.hasNonNull("margin")) {
            JsonNode margin = attrs.path("margin");
            if (!margin.isEmpty()) {
                log.info("段落间距：{}, {}", type, margin);
                if (margin.has("top")) {
                    paragraph.setSpacingBefore(margin.path("top").asInt());
                }
                if (margin.has("bottom")) {
                    paragraph.setSpacingAfter(margin.path("bottom").asInt());
                }
            }

        }

        // 段落边框
        if (attrs.hasNonNull("backgroundBorder")) {
            JsonNode backgroundBorder = attrs.path("backgroundBorder");
            if (!backgroundBorder.isEmpty()) {
                log.info("段落边框：{}, {}", type, backgroundBorder);
                // 获取段落的 CTPPr 对象
                CTPPr pPr = paragraph.getCTP().getPPr();
                if (pPr == null) {
                    pPr = paragraph.getCTP().addNewPPr();
                }

                String borderColor = backgroundBorder.path("borderColor").asText();
                borderColor = StringUtils.rightPad(borderColor, 6, "0");
                int borderWidth = backgroundBorder.path("borderWidth").asInt();
                String borderStyle = backgroundBorder.path("borderStyle").asText();

                // 创建段落边框对象
                CTPBdr pBdr = pPr.isSetPBdr() ? pPr.getPBdr() : pPr.addNewPBdr();

                // 设置上边框
                CTBorder topBorder = pBdr.addNewTop();
                setBorder(topBorder, borderWidth, borderColor);

                // 设置下边框
                CTBorder bottomBorder = pBdr.addNewBottom();
                setBorder(bottomBorder, borderWidth, borderColor);

                // 设置左边框
                CTBorder leftBorder = pBdr.addNewLeft();
                setBorder(leftBorder, borderWidth, borderColor);

                // 设置右边框
                CTBorder rightBorder = pBdr.addNewRight();
                setBorder(rightBorder, borderWidth, borderColor);
            }


        }
    }

    /**
     * 处理行内节点
     *
     * @param paragraph
     * @param paraNode
     */
    private static void handleInLineNode(XWPFParagraph paragraph, JsonNode paraNode, boolean isHeading) {
        JsonNode attrs;
        for (JsonNode inLineNode : paraNode.path("content")) {
            String type = inLineNode.path("type").asText();
            // 文本
            if ("text".equals(type)) {
                processTextRun(paragraph, inLineNode, isHeading, paraNode);
            }
            // 行内图片
            if ("imageInLine".equals(type) || "imageIcon".equals(type)) {
                attrs = inLineNode.path("attrs");
                insertImage(paragraph, attrs.path("src").asText(), attrs.path("name").asText(), 1, 1);
            }
            // 链接
            if ("links".equals(type)) {
                attrs = inLineNode.path("attrs");
                createHyperlink(paragraph, attrs.path("href").asText(), attrs.path("title").asText() + "(" + attrs.path("href").asText() + ")");
            }
            // 气泡
            if ("bubbleInline".equals(type)) {
                // TODO 气泡节点
                log.info("气泡节点：{}", type);
                XWPFRun run = paragraph.createRun();
                run.setText(inLineNode.path("attrs").path("bubbleContent").asText());
            }
        }
    }

    /**
     * 处理列表
     *
     * @param doc
     * @param listNode
     */
    private static void processList(XWPFDocument doc, XWPFTableCell cell, JsonNode listNode) {
        // 创建列表模板
        CTAbstractNum abstractNum = CTAbstractNum.Factory.newInstance();
        CTLvl lvl = abstractNum.addNewLvl();
        // 设置编号格式
        if ("orderedList".equals(listNode.path("type").asText())) {
            lvl.addNewNumFmt().setVal(STNumberFormat.DECIMAL);
            lvl.addNewLvlText().setVal("%1.");
            abstractNum.setAbstractNumId(BigInteger.valueOf((int) (Math.random() * 10000) + 15));
        } else {
            lvl.addNewNumFmt().setVal(STNumberFormat.BULLET);
            lvl.addNewLvlText().setVal("•");
            abstractNum.setAbstractNumId(BigInteger.valueOf((int) (Math.random() * 10000) + 1));
        }

        lvl.addNewStart().setVal(BigInteger.ONE);

        // 注册模板到文档
        XWPFNumbering numbering = doc.getNumbering();
        if (numbering == null) {
            numbering = doc.createNumbering();
        }

        // 添加抽象编号并获取ID
        XWPFAbstractNum xwpfAbstractNum = new XWPFAbstractNum(abstractNum);
        BigInteger abstractNumId = numbering.addAbstractNum(xwpfAbstractNum);

        // 创建具体编号实例
        BigInteger numID = numbering.addNum(abstractNumId);
        // 添加列表项
        for (JsonNode itemNode : listNode.path("content")) {
            XWPFParagraph paragraph = null;
            if (cell == null) {
                paragraph = doc.createParagraph();
            } else {
                paragraph = cell.addParagraph();
            }
            paragraph.setNumID(numID);
            // 处理列表项内容
            for (JsonNode content : itemNode.path("content")) {
                processListItem(doc, cell, paragraph, content);
            }
        }
    }

    /**
     * 处理列表项
     *
     * @param doc
     * @param cell
     * @param paragraph
     * @param node
     */
    private static void processListItem(XWPFDocument doc, XWPFTableCell cell, XWPFParagraph paragraph, JsonNode node) {
        handleNode(doc, cell, paragraph, node);
    }


    /**
     * 插入图片
     *
     * @param paragraph
     * @param imageUrl
     */
    public static void insertImage(XWPFParagraph paragraph, String imageUrl, String imageName, int width, int height) {
        if (ObjectUtil.isEmpty(imageUrl)) {
            return;
        }
        XWPFRun run = paragraph.createRun();
        try (InputStream imageStream = new ByteArrayInputStream(IOUtils.toByteArray(HttpRequest.get(imageUrl).header("Referer", "https://ebook.dutp.cn").execute().bodyStream()))) {
            run.addPicture(imageStream, XWPFDocument.PICTURE_TYPE_JPEG, imageName, Units.toEMU(pxToTwip(width)), Units.toEMU(pxToTwip(height)));
        } catch (Exception e) {
            log.info("图片获取异常，url{},", imageUrl, e);
        }

    }


    /**
     * 设置边框
     *
     * @param border
     * @param borderWidth
     * @param borderColor
     */
    private static void setBorder(CTBorder border, Integer borderWidth, String borderColor) {
        border.setVal(STBorder.SINGLE);
        border.setSz(BigInteger.valueOf(pxToHalfPound(borderWidth))); // 边框宽度，单位为 1/8 磅
        border.setColor(getDocxColor(borderColor));
    }

    /**
     * 获取颜色
     *
     * @param color
     * @return
     */
    private static String getDocxColor(String color) {
        if (ObjectUtil.isNotEmpty(color) && !"null".equals(color)) {
            try {
                if (color.startsWith("rgb(")) {
                    color = rgbToHex(color);
                }
                color = StringUtils.rightPad(color.replace("#", ""), 6, "0");
            } catch (Exception e) {
                log.info("颜色转化异常：{}", color, e);
            }
            return color;
        }
        return "000000";
    }


    /**
     * 创建超链接
     *
     * @param paragraph 段落对象
     * @param url       超链接的 URL
     * @param text      超链接显示的文本
     */
    private static void createHyperlink(XWPFParagraph paragraph, String url, String text) {
        // 创建一个超链接
        CTHyperlink ctHyperlink = paragraph.getCTP().addNewHyperlink();
        ctHyperlink.setId(paragraph.getDocument().getPackagePart().addExternalRelationship(url, XWPFRelation.HYPERLINK.getRelation()).getId());

        // 创建一个运行对象
        CTR ctr = ctHyperlink.addNewR();
        CTText ctText = ctr.addNewT();
        ctText.setStringValue(text);

        // 创建 XWPFRun 对象
        XWPFRun run = new XWPFRun(ctr, paragraph);
        // 设置超链接文本的颜色为蓝色
        run.setColor("0000FF");
        // 设置下划线样式为单下划线
        run.setUnderline(UnderlinePatterns.SINGLE);
    }

    /**
     * 处理文本样式
     *
     * @param paragraph
     * @param textNode
     */
    private static void processTextRun(XWPFParagraph paragraph, JsonNode textNode, boolean isHeading, JsonNode headingNode) {
        XWPFRun run = paragraph.createRun();
        run.setText(textNode.path("text").asText());

        if (isHeading) {
            JsonNode attrs = headingNode.path("attrs");
            // TODO 样式处理
            run.setBold(true);
            run.setFontSize(getHeadFontSize(attrs.path("level").asInt()));
        }

        try {
            for (JsonNode mark : textNode.path("marks")) {
                String markType = mark.path("type").asText();
                JsonNode markAttrs = mark.path("attrs");

                log.info("markType:{}", markType);
                switch (markType) {
                    case "textStyle":
                        applyTextStyle(run, markAttrs);
                        break;
                    case "textBorder":
                        CTRPr rPr = run.getCTR().getRPr();
                        if (rPr == null) {
                            rPr = run.getCTR().addNewRPr();
                        }

                        JsonNode textBorder = markAttrs.path("textBorder");
                        String borderColor = textBorder.path("borderColor").asText();
                        String docxColor = getDocxColor(borderColor);
                        if (ObjectUtil.isNotEmpty(docxColor)) {
                            log.info("文字边框颜色：{}", docxColor);
                            CTBorder ctBorder = rPr.addNewBdr();
                            ctBorder.setColor(docxColor);
                            String borderWidth = textBorder.path("borderWidth").asText();
                            if (ObjectUtil.isEmpty(borderWidth)) {
                                borderWidth = "1";
                            }
                            ctBorder.setSz(BigInteger.valueOf(pxToHalfPound(Integer.valueOf(borderWidth))));
                            ctBorder.setVal(STBorder.SINGLE);
                        }

                        break;
                    case "highlight":
                        log.info("高亮：{}", markAttrs);
                        String color = markAttrs.path("color").asText();
                        if (ObjectUtil.isNotEmpty(color) && !color.equals("null")) {
                            CTRPr rPr2 = run.getCTR().getRPr();
                            if (rPr2 == null) {
                                rPr2 = run.getCTR().addNewRPr();
                            }
                            CTShd ctShd = rPr2.addNewShd();
                            ctShd.setFill(StringUtils.rightPad(color.replace("#", ""), 6, "0"));
                        }
                        break;
                    case "bold":
                        run.setBold(true);
                        break;
                    case "italic":
                        run.setItalic(true);
                        break;
                    case "underline":
                        run.setUnderline(UnderlinePatterns.SINGLE);
                        break;
                    case "strike":
                        run.setStrikeThrough(true);
                        break;
                    case "textShadow":
                        run.setShadow(true);
                        break;
                    case "superscript":
                        run.setSubscript(VerticalAlign.SUPERSCRIPT);
                        break;
                    case "subscript":
                        run.setSubscript(VerticalAlign.SUBSCRIPT);
                        break;
                }
            }
        } catch (Exception e) {
            log.info("样式处理失败：", e);
        }
    }

    /**
     * 应用文本样式
     *
     * @param run
     * @param attrs
     */
    private static void applyTextStyle(XWPFRun run, JsonNode attrs) {
        try {
            if (attrs.has("color")) {
                log.info("字体颜色：{}", attrs);
                JsonNode node = attrs.path("color");
                if (!node.isMissingNode()) {
                    String color = node.asText();
                    String docxColor = getDocxColor(color);
                    if (ObjectUtil.isNotEmpty(docxColor)) {
                        run.setColor(docxColor);
                    }
                }

            }
            if (attrs.has("letterSpacing")) {
                String letterSpacing = attrs.path("letterSpacing").asText();
                if (ObjectUtil.isNotEmpty(letterSpacing) && !"null".equals(letterSpacing)) {
                    letterSpacing = letterSpacing.replace("px", "");
                    try {
                        run.setCharacterSpacing(pxToTwip(Integer.valueOf(letterSpacing)));
                    } catch (NumberFormatException e) {
                        run.setCharacterSpacing(pxToTwip(1));
                    }

                }
            }
            if (attrs.has("fontSize")) {
                String text = attrs.path("fontSize").asText();
                if (ObjectUtil.isNotEmpty(text) && !"null".equals(text)) {
                    String fontSizeStr = text.replace("px", "").replace("pt", "");
                    int fontSize = (int) Math.round(Double.valueOf(fontSizeStr));
                    run.setFontSize(pxToPound(fontSize));
                }
            }
            if (attrs.has("fontFamily")) {
                String fontFamily = attrs.path("fontFamily").asText();
                if (ObjectUtil.isNotEmpty(fontFamily)) {
                    run.setFontFamily(fontFamily);
                }
            }
        } catch (Exception e) {
            log.info("应用文本样式失败：", e);
        }
    }

    /**
     * rgb转Hex
     *
     * @param rgb
     * @return
     */
    private static String rgbToHex(String rgb) {
        // 去除字符串首尾的空格
        rgb = rgb.trim();
        // 检查输入是否符合 "rgb(x, y, z)" 格式
        if (!rgb.startsWith("rgb(") || !rgb.endsWith(")")) {
            throw new IllegalArgumentException("输入不是有效的 RGB 格式。");
        }
        // 去掉 "rgb(" 和 ")"，并按逗号分割字符串
        String[] values = rgb.substring(4, rgb.length() - 1).split(",");
        if (values.length != 3) {
            throw new IllegalArgumentException("输入不是有效的 RGB 格式。");
        }
        try {
            // 提取红、绿、蓝通道的值并转换为整数
            int red = Integer.parseInt(values[0].trim());
            int green = Integer.parseInt(values[1].trim());
            int blue = Integer.parseInt(values[2].trim());
            // 检查每个通道的值是否在 0 到 255 之间
            validateColorValue(red);
            validateColorValue(green);
            validateColorValue(blue);
            // 将每个通道的值转换为两位十六进制字符串，并组合起来
            return "#" + String.format("%02x", red) + String.format("%02x", green) + String.format("%02x", blue);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入不是有效的 RGB 格式。", e);
        }
    }

    /**
     * 校验颜色
     *
     * @param value
     */
    private static void validateColorValue(int value) {
        if (value < 0 || value > 255) {
            throw new IllegalArgumentException("颜色通道值必须在 0 到 255 之间。");
        }
    }

    /**
     * 将像素值转换为缇值
     *
     * @param px
     * @return
     */
    private static int pxToTwip(int px) {
        // 先将px转换为英寸
        double inches = (double) px * 1440;
        // 再将英寸转换为像素
        return (int) (inches / DEFAULT_DPI);
    }

    /**
     * px转半磅
     *
     * @param px
     * @return
     */
    private static long pxToHalfPound(Integer px) {
        return (long) (px * 2l / (DEFAULT_DPI / 72.0));
    }

    /**
     * px转磅
     *
     * @param px
     * @return
     */
    private static int pxToPound(Integer px) {
        return (int) (px / (DEFAULT_DPI / 72.0));
    }

}
