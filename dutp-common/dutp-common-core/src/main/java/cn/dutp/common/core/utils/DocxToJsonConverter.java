package cn.dutp.common.core.utils;

import cn.dutp.common.core.domain.UploadFileDto;
import cn.dutp.common.core.exception.ServiceException;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.batik.transcoder.TranscoderException;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;


/**
 * docx转json工具类
 */
@Slf4j
@Component
public class DocxToJsonConverter {

    private final int DEFAULT_DPI = 96;

    @Autowired
    private AliyunOssStsUtil aliyunOssStsUtil;

    // public void main(String[] args) throws Exception {
    //
    //     Map<String, Object> doc = analyzeWord(new File("D:\\桌面tem\\测试\\教材正文项目三.docx"));
    //     // 写入JSON
    //     ObjectMapper mapper = new ObjectMapper();
    //     mapper.enable(SerializationFeature.INDENT_OUTPUT);
    //     mapper.writeValue(new File("output.json"), doc);
    // }

    /**
     * 解析word文件生成json
     *
     * @param wordFile
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> analyzeWord(MultipartFile wordFile) throws Exception {
        String fileName = wordFile.getOriginalFilename();
        if (!fileName.endsWith(".docx")) {
            throw new ServiceException("文件格式不正确，请上传.docx文件");
        }
        return analyzeWord(wordFile.getInputStream());
    }

    /**
     * 解析word文件生成json
     *
     * @param wordFile
     * @return
     * @throws FileNotFoundException
     */
    public List<Map<String, Object>> analyzeWord(File wordFile) throws Exception {
        String fileName = wordFile.getName();
        if (!fileName.endsWith(".docx")) {
            throw new ServiceException("文件格式不正确，请上传.docx文件");
        }
        return analyzeWord(new FileInputStream(wordFile));
    }

    /**
     * 解析word文件生成json
     *
     * @param docInputStream
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> analyzeWord(InputStream docInputStream) throws Exception {
        List<Map<String, Object>> docList = new ArrayList<>();
        // 保存原始阈值
        double originalRatio = ZipSecureFile.getMinInflateRatio();
        // 设置临时阈值（低于当前文件的实际压缩比 0.0086）
        ZipSecureFile.setMinInflateRatio(0.0001);
        try (XWPFDocument document = new XWPFDocument(docInputStream)) {
            Map<String, Object> doc = null;
            List<Object> content = null;
            Map<String, Object> page = null;
            List<Object> pagetContentList = null;
            boolean isGenerateDoc = false;

            // 遍历文档元素
            for (IBodyElement element : document.getBodyElements()) {
                if (element instanceof XWPFParagraph) {
                    XWPFParagraph paragraph = (XWPFParagraph) element;
                    String styleId = paragraph.getStyleID();
                    if (styleId != null) {
                        // 标题
                        // 根据样式 ID
                        XWPFStyle style = document.getStyles().getStyle(styleId);
                        String headerName = style.getName();
                        if (headerName.startsWith("heading")) {
                            // 提取标题级别
                            int level = Integer.parseInt(headerName.substring(7).trim());
                            if (level == 1) {
                                isGenerateDoc = true;
                            }
                        }
                    }
                    if (isGenerateDoc) {
                        doc = new HashMap<>();
                        doc.put("type", "doc");
                        doc.put("attrs", Collections.emptyMap());
                        content = new ArrayList<>();
                        page = new HashMap<>();
                        pagetContentList = new ArrayList<>();
                        page.put("type", "page");
                        page.put("attrs", Collections.emptyMap());
                        page.put("content", pagetContentList);
                        content.add(page);
                        doc.put("content", content);
                        docList.add(doc);
                        isGenerateDoc = false;
                    }
                    if (pagetContentList == null) {
                        throw new ServiceException("word格式不正确，第一行必须是一级标题，且每章标题必须是一级标题");
                    }
                    processParagraph(paragraph, pagetContentList);
                } else if (element instanceof XWPFTable) {
                    if (pagetContentList == null) {
                        throw new ServiceException("word格式不正确，第一行必须是一级标题，且每章标题必须是一级标题");
                    }
                    processTable((XWPFTable) element, pagetContentList);
                }
            }
        } finally {
            try {
                docInputStream.close();
            } catch (Exception e) {
                log.info("word文件流关闭异常", e);
            }
            ZipSecureFile.setMinInflateRatio(originalRatio);
        }
        return docList;
    }

    /**
     * 处理段落
     *
     * @param paragraph
     * @param parentContent
     */
    private void processParagraph(XWPFParagraph paragraph, List<Object> parentContent) {

        if (isListItem(paragraph)) {
            HashMap<String, Object> curListNode = null;
            String type = "";
            if (parentContent.size() > 0) {
                curListNode = (HashMap<String, Object>) parentContent.get(parentContent.size() - 1);
                type = (String) curListNode.get("type");
            }

            String listType = determineListType(paragraph);
            log.info("listType:{}, {}", listType, type);
            if (!"orderedList".equals(type) && !"bulletList".equals(type) || (
                    !listType.equals(type) && (
                            !"orderedList".equals(type) || !"bulletList".equals(type)
                    )
            )) {
                // 列表段落
                curListNode = new HashMap<>();

                curListNode.put("type", listType);
                curListNode.put("attrs", new HashMap<String, Object>() {{
                    // TODO 暂时先默认
                    if (listType.equals("orderedList")) {
                        put("listType", "decimal");
                    } else {
                        put("listType", "circle");
                    }
                }});
                ArrayList<Object> childrenList = new ArrayList<>();
                curListNode.put("content", childrenList);

                parentContent.add(curListNode);
            }


            ArrayList<Object> childrenList = new ArrayList<>();

            // 添加段落内容
            processNormalParagraph(paragraph, childrenList);

            if (ObjectUtil.isNotEmpty(childrenList)) {
                Map<String, Object> listItemParagraph = new HashMap<>();
                listItemParagraph.put("type", "listItem");
                listItemParagraph.put("content", childrenList);
                ((List<Object>) curListNode.get("content")).add(listItemParagraph);
            }
        } else {
            String styleId = paragraph.getStyleID();
            if (styleId != null) {
                // 标题
                // 根据样式 ID
                XWPFStyle style = paragraph.getDocument().getStyles().getStyle(styleId);
                String headerName = style.getName();
                if (headerName.startsWith("heading")) {
                    try {
                        // 提取标题级别
                        int level = Integer.parseInt(headerName.substring(7).trim());
                        String titleText = paragraph.getText();
                        HashMap<Object, Object> header = new HashMap<>();
                        header.put("type", "heading");
                        HashMap<String, Object> nodeAttrs = new HashMap<>();
                        nodeAttrs.put("level", level);
                        // 获取段落属性
                        getParagraphAttrs(paragraph, nodeAttrs);
                        header.put("attrs", nodeAttrs);
                        if (ObjectUtil.isEmpty(titleText)) {
                            titleText = " ";
                        }
                        String finalTitleText = titleText;
                        header.put("content", new ArrayList<Object>() {{
                            add(new HashMap<String, Object>() {{
                                put("text", finalTitleText);
                                put("type", "text");
                            }});
                        }});
                        parentContent.add(header);

                    } catch (Exception e) {
                        log.info("无法解析标题级别，样式 ID: {}, 异常：{}", headerName, e.getMessage());
                        processNormalParagraph(paragraph, parentContent);
                    }
                    return;
                }
            }
            // 普通段落
            processNormalParagraph(paragraph, parentContent);
        }


    }

    /**
     * 处理普通段落
     *
     * @param paragraph
     * @param parentContent
     */
    private void processNormalParagraph(XWPFParagraph paragraph, List<Object> parentContent) {
        Map<String, Object> curNode = null;
        List<Object> childrenContent = null;
        HashMap<String, Object> nodeAttrs = new HashMap<>();
        // TODO 获取段落样式
        getParagraphAttrs(paragraph, nodeAttrs);

        // 处理段落中的文本和图片
        for (XWPFRun run : paragraph.getRuns()) {
            if (childrenContent == null) {
                childrenContent = new ArrayList<>();
            }

            // 处理文本
            String text = run.getText(0);
            List<XWPFPicture> pictureList = run.getEmbeddedPictures();

            if (text != null && !text.trim().isEmpty()) {
                log.info("文本节点:{}", run);
                childrenContent.add(createTextNode(run, text));
            } else if (ObjectUtil.isNotEmpty(pictureList)) {
                // TODO 处理图片 先统一作为版式图片处理
                log.info("图片节点:{}", run);
                // 将文本节点放入段落
                if (ObjectUtil.isNotEmpty(childrenContent)) {
                    curNode = new HashMap<>();
                    curNode.put("type", "paragraph");
                    curNode.put("content", childrenContent);
                    curNode.put("attrs", nodeAttrs);
                    childrenContent = null;
                }

                // 创建图片节点
                parentContent.add(createImageNode(pictureList));

            } else {
                // TODO 其他类型
                log.info("其他节点:{}", run);
            }

        }

        if (ObjectUtil.isNotEmpty(childrenContent)) {
            curNode = new HashMap<>();
            curNode.put("type", "paragraph");
            curNode.put("content", childrenContent);
            curNode.put("attrs", nodeAttrs);
            parentContent.add(curNode);
        }
    }

    /**
     * 获取段落属性
     *
     * @param paragraph
     * @param nodeAttrs
     */
    private void getParagraphAttrs(XWPFParagraph paragraph, HashMap<String, Object> nodeAttrs) {
        // 段落对齐方式
        String alignment = paragraph.getAlignment().name();
        nodeAttrs.put("textAlign", alignment);
        // 段落缩进
        int indent = paragraph.getFirstLineIndent();
        if (indent != -1) {
            int importIndent = twipToPixel(indent);
            log.info("段落缩进：{}，{}", indent, importIndent);
            importIndent = importIndent / 24;
            if (importIndent > 5) {
                importIndent = 5;
            }
            if (importIndent < 0) {
                importIndent = 1;
            }
            nodeAttrs.put("indent", importIndent);
        }

        // 段落行间距
        double spacingBetween = paragraph.getSpacingBetween();
        if (spacingBetween != -1) {
            int lineHeight = twipToPixel(spacingBetween);
            nodeAttrs.put("lineHeight", lineHeight <= 0 ? 1.5 : lineHeight);
        }

        // 段落间距
        int spacingBefore = paragraph.getSpacingBefore();
        int spacingAfter = paragraph.getSpacingAfter();
        if (spacingBefore != -1 || spacingAfter != -1) {
            HashMap<String, Object> margin = new HashMap<>();
            margin.put("top", spacingBefore == -1 ? 0 : twipToPixel(spacingBefore));
            margin.put("bottom", spacingAfter == -1 ? 0 : twipToPixel(spacingAfter));
            nodeAttrs.put("margin", margin);
        }

        // 段落边框
        if (paragraph.getCTP().getPPr().isSetPBdr()) {
            HashMap<String, Object> backgroundBorder = new HashMap<>();
            CTPBdr pBdr = paragraph.getCTP().getPPr().getPBdr();
            CTBorder left = pBdr.getLeft();
            CTBorder top = pBdr.getTop();
            CTBorder right = pBdr.getRight();
            CTBorder bottom = pBdr.getBottom();
            if (ObjectUtil.isNotEmpty(left)) {
                setBorder(backgroundBorder, left);
            } else if (ObjectUtil.isNotEmpty(top)) {
                setBorder(backgroundBorder, top);
            } else if (ObjectUtil.isNotEmpty(right)) {
                setBorder(backgroundBorder, right);
            } else if (ObjectUtil.isNotEmpty(bottom)) {
                setBorder(backgroundBorder, bottom);
            }
            nodeAttrs.put("backgroundBorder", backgroundBorder);
        }
    }

    /**
     * 处理表格
     *
     * @param table
     * @param parentContent
     */
    private void processTable(XWPFTable table, List<Object> parentContent) {
        Map<String, Object> tableNode = new HashMap<>();
        tableNode.put("type", "table");
        tableNode.put("attrs", Collections.emptyMap());
        List<Object> rowsContent = new ArrayList<>();

        HashMap<String, Map<String, Object>> rowMergeMap = new HashMap<>();
        int curColNum = 0;
        for (XWPFTableRow row : table.getRows()) {
            Map<String, Object> rowNode = new HashMap<>();
            rowNode.put("type", "tableRow");
            List<Object> cellsContent = new ArrayList<>();
            curColNum = 0;
            for (XWPFTableCell cell : row.getTableCells()) {

                Map<String, Object> cellNode = new HashMap<>();
                cellNode.put("type", "tableCell");
                HashMap<String, Object> attrs = new HashMap<>();

                // 处理单元格时检查合并状态
                if (cell.getCTTc().getTcPr().getGridSpan() != null) {
                    curColNum += cell.getCTTc().getTcPr().getGridSpan().getVal().intValue();
                    attrs.put("colspan", cell.getCTTc().getTcPr().getGridSpan().getVal());
                } else {
                    curColNum++;
                    attrs.put("colspan", 1);
                }
                if (cell.getCTTc().getTcPr().getVMerge() != null) {
                    if (cell.getCTTc().getTcPr().getVMerge().isSetVal() && "restart".equals(cell.getCTTc().getTcPr().getVMerge().getVal().toString())) {
                        attrs.put("rowspan", 1);
                        rowMergeMap.put(String.valueOf(curColNum), attrs);
                    } else {
                        Map<String, Object> lastRowMergeMap = rowMergeMap.get(String.valueOf(curColNum));
                        int rowspan = (int) lastRowMergeMap.get("rowspan");
                        lastRowMergeMap.put("rowspan", rowspan + 1);
                        continue;
                    }
                } else {
                    attrs.put("rowspan", 1);
                }

                cellNode.put("attrs", attrs);
                List<Object> cellContent = new ArrayList<>();
                // 处理单元格内容（可能包含段落和嵌套表格）
                for (IBodyElement elem : cell.getBodyElements()) {
                    if (elem instanceof XWPFParagraph) {
                        processParagraph((XWPFParagraph) elem, cellContent);
                    } else if (elem instanceof XWPFTable) {
                        processTable((XWPFTable) elem, cellContent);
                    }
                }

                if (cellContent.isEmpty()) {
                    // 给一个默认的p
                    cellContent.add(new HashMap<String, Object>() {
                        {
                            put("type", "paragraph");
                            put("content", Collections.emptyList());
                            put("attrs", new HashMap<String, Object>());
                        }
                    });
                }

                cellNode.put("content", cellContent);
                cellsContent.add(cellNode);
            }

            rowNode.put("content", cellsContent);
            rowsContent.add(rowNode);
        }

        tableNode.put("content", rowsContent);
        parentContent.add(tableNode);
    }


    /**
     * 判断是否是列表项
     */
    private boolean isListItem(XWPFParagraph para) {
        return para.getNumID() != null && ObjectUtil.isNotEmpty(para.getNumID());
    }

    /**
     * 确定列表类型
     */
    private String determineListType(XWPFParagraph para) {
        try {
            if (para.getNumFmt() != null) {
                return "bullet".equals(para.getNumFmt()) ? "bulletList" : "orderedList";
            }
        } catch (Exception e) {
            log.error("判断列表类型失败", e);
        }
        return "bulletList";
    }

    /**
     * 创建文本节点
     *
     * @param run
     * @param text
     * @return
     */
    private Map<String, Object> createTextNode(XWPFRun run, String text) {
        Map<String, Object> textNode = new HashMap<>();
        if (ObjectUtil.isEmpty(text)) {
            text = " ";
        }
        textNode.put("type", "text");
        textNode.put("text", text);

        // 创建mark
        List<Object> marks = createMarksNodeList(run);
        if (ObjectUtil.isNotEmpty(marks)) {
            textNode.put("marks", marks);
        }

        return textNode;
    }

    /**
     * 根据run创建mark列表
     *
     * @param run
     * @return
     */
    private List<Object> createMarksNodeList(XWPFRun run) {
        List<Object> marks = new ArrayList<>();
        String color = run.getColor();
        int fontSize = run.getFontSize();
        String fontFamily = run.getFontFamily();
        // textStyle
        if (ObjectUtil.isNotEmpty(color) || isSetSpacing(run) || fontSize != -1 || ObjectUtil.isNotEmpty(fontFamily)) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "textStyle");
            HashMap<String, Object> attrs = new HashMap<>();
            if (ObjectUtil.isNotEmpty(color)) {
                // 颜色
                log.info("markColor:{}", color);
                attrs.put("color", "#" + color);
            }
            if (isSetSpacing(run)) {
                // 字符间距
                // 获取字符间距（以缇为单位）
                Integer charSpacing = run.getCTR().getRPr().getSpacing().getVal().intValue();
                // 将缇转换为像素
                int pxSpacing = twipToPixel(charSpacing);
                log.info("字符间距（像素）: {}", pxSpacing);
                attrs.put("letterSpacing", pxSpacing + "px");
            }
            if (fontSize != -1) {
                // 字体大小
                // 将磅转换为像素
                int pxFontSize = poundToPx(fontSize);
                log.info("字体大小（像素）: {}", pxFontSize);
                attrs.put("fontSize", pxFontSize + "px");
            }
            if (ObjectUtil.isNotEmpty(fontFamily)) {
                // 字体
                log.info("fontFamily:{}", fontFamily);
                attrs.put("fontFamily", fontFamily);
            }
            mark.put("attrs", attrs);
            marks.add(mark);
        }

        // 粗体
        if (run.isBold()) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "bold");
            marks.add(mark);
        }

        // 斜体
        if (run.isItalic()) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "italic");
            marks.add(mark);
        }

        // 下划线
        UnderlinePatterns underline = run.getUnderline();
        if (underline.getValue() != 18) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "underline");
            marks.add(mark);
        }

        // 删除线
        if (run.isStrikeThrough() || run.isDoubleStrikeThrough()) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "strike");
            marks.add(mark);
        }

        // 高亮
        if (run.isHighlighted() || run.getCTR().getRPr().isSetShd()) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "highlight");
            HashMap<String, Object> attrs = new HashMap<>();
            if (run.isHighlighted()) {
                STHighlightColor.Enum textHightlightColor = run.getTextHightlightColor();
                log.info("Highlighted高亮颜色，{}", textHightlightColor.toString());
                attrs.put("color", textHightlightColor.toString());
            }
            if (run.getCTR().getRPr().isSetShd()) {
                Object shdColor = run.getCTR().getRPr().getShd().getColor();
                log.info("Shd高亮颜色：{}", shdColor);
                attrs.put("color", shdColor);
            }
            mark.put("attrs", attrs);
            marks.add(mark);
        }

        // 阴影
        if (run.isShadowed()) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "textShadow");
            marks.add(mark);
        }

        // 文字边框
        if (run.getCTR().getRPr().isSetBdr()) {
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "textBorder");
            HashMap<String, Object> attrs = new HashMap<>();
            HashMap<String, Object> textBorder = new HashMap<>();
            CTBorder bdr = run.getCTR().getRPr().getBdr();
            setBorder(textBorder, bdr);
            attrs.put("textBorder", textBorder);
            mark.put("attrs", attrs);
            marks.add(mark);
        }

        STVerticalAlignRun.Enum verticalAlign = run.getVerticalAlignment();
        if (verticalAlign == STVerticalAlignRun.SUPERSCRIPT) {
            // 上标
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "superscript");
            marks.add(mark);
        } else if (verticalAlign == STVerticalAlignRun.SUBSCRIPT) {
            // 下标
            HashMap<String, Object> mark = new HashMap<>();
            mark.put("type", "subscript");
            marks.add(mark);
        }

        if (marks.size() == 0) {
            return null;
        } else {
            return marks;
        }

    }

    /**
     * 将缇值转换为像素值
     *
     * @param twip
     * @return
     */
    public int twipToPixel(int twip) {
        // 先将缇转换为英寸
        double inches = (double) twip / 1440;
        // 再将英寸转换为像素
        return (int) (inches * DEFAULT_DPI);
    }

    /**
     * 将缇值转换为像素值
     *
     * @param twip
     * @return
     */
    public int twipToPixel(double twip) {
        // 先将缇转换为英寸
        double inches = twip / 1440;
        // 再将英寸转换为像素
        return (int) (inches * DEFAULT_DPI);
    }

    /**
     * 半磅转px
     *
     * @param halfPound
     * @return
     */
    private int halfPoundToPx(Integer halfPound) {
        return (int) (halfPound / 2 * (DEFAULT_DPI / 72.0));
    }

    /**
     * 磅转px
     *
     * @param pound
     * @return
     */
    private int poundToPx(Integer pound) {
        return (int) (pound * (DEFAULT_DPI / 72.0));
    }

    /**
     * 是否检测设置间距
     *
     * @param run
     * @return
     */
    private boolean isSetSpacing(XWPFRun run) {
        if (ObjectUtil.isNotEmpty(run.getCTR())) {
            if (ObjectUtil.isNotEmpty(run.getCTR().getRPr())) {
                return run.getCTR().getRPr().isSetSpacing();
            }
        }
        return false;
    }

    /**
     * 边框样式
     *
     * @param textBorder
     * @param bdr
     */
    private void setBorder(HashMap<String, Object> textBorder, CTBorder bdr) {
        Object colorObj = bdr.getColor();
        String color = getBorderColor(colorObj);
        textBorder.put("borderColor", "auto".equalsIgnoreCase(color) ? "#000000" : "#" + color);
        textBorder.put("borderStyle", getBorderStyle(bdr.getVal()));
        textBorder.put("borderWidth", halfPoundToPx(bdr.getSz().intValue()));
    }

    /**
     * 获取边框颜色
     *
     * @param colorObj
     * @return
     */
    private String getBorderColor(Object colorObj) {
        String color;
        if (colorObj instanceof byte[]) {
            byte[] colorBytes = (byte[]) colorObj;

            // 字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : colorBytes) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            color = hexString.toString();
        } else {
            color = String.valueOf(colorObj);
        }
        return color;
    }

    /**
     * 获取边框样式
     *
     * @param style
     * @return
     */
    private String getBorderStyle(STBorder.Enum style) {
        if (style == null) {
            return "solid";
        }
        switch (style.intValue()) {
            case STBorder.INT_DOTTED:
                return "dotted";
            case STBorder.INT_DASHED:
                return "dashed";
            default:
                return "solid";
        }
    }

    /**
     * 创建图片节点
     *
     * @param pictureList
     * @return
     */
    private Map<String, Object> createImageNode(List<XWPFPicture> pictureList) {
        Map<String, Object> imageNode = new HashMap<>();
        try {
            if (pictureList.size() == 1) {
                // 创建版式图片
                imageNode.put("type", "imageLayout");
                imageNode.put("content", new ArrayList<Object>() {
                    {
                        add(new HashMap<String, Object>() {
                            {
                                put("type", "paragraph");
                                put("content", Collections.singletonList(new HashMap<String, Object>() {
                                    {
                                        put("text", "图片标题");
                                        put("type", "text");
                                    }
                                }));
                                put("attrs", new HashMap<String, Object>());
                            }
                        });
                    }
                });
                HashMap<String, Object> attrs = new HashMap<>();
                imageNode.put("attrs", attrs);
                XWPFPicture xwpfPicture = pictureList.get(0);
                UploadFileDto imgFile = updateImgFile(xwpfPicture);
                attrs.put("src", imgFile.getFileUrl());
                attrs.put("name", imgFile.getFileName());
                attrs.put("size", imgFile.getFileSize());
                // 默认不展示图片标题
                attrs.put("isShowImageTitle", "0");


            } else {
                // 创建画廊
                List<UploadFileDto> imgList = new ArrayList<>();
                for (XWPFPicture picture : pictureList) {
                    imgList.add(updateImgFile(picture));
                }

                imageNode.put("type", "imageGallery");
                imageNode.put("attrs", new HashMap<String, Object>() {{
                    put("imgList", imgList.stream().map(o -> new HashMap<String, Object>() {{
                        put("src", o.getFileUrl());
                        put("name", o.getFileName());
                        put("size", o.getFileSize());
                        put("imageTitle", o.getFileName());
                    }}).collect(Collectors.toList()));
                }});
            }
        } catch (Exception e) {
            log.error("图片组件导入失败：", e);
            // 导入失败转化图片组件为段落组件
            imageNode.put("type", "paragraph");
            imageNode.put("content", Collections.emptyList());
        }
        return imageNode;
    }

    /**
     * 上传图片
     *
     * @param picture
     * @return
     */
    private UploadFileDto updateImgFile(XWPFPicture picture) throws IOException, TranscoderException {
        XWPFPictureData pictureData = picture.getPictureData();
        byte[] bytes = pictureData.getData();
        long l = System.currentTimeMillis();
        String fileName = pictureData.getFileName();
        // emf格式图片转化为png
        if (pictureData.getPictureType() == Document.PICTURE_TYPE_EMF) {
            TranscoderInput input = new TranscoderInput(new ByteArrayInputStream(bytes));
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            TranscoderOutput output = new TranscoderOutput(byteArrayOutputStream);
            PNGTranscoder transcoder = new PNGTranscoder();
            transcoder.transcode(input, output);
            bytes = byteArrayOutputStream.toByteArray();
            // 读取 EMF 数据流
            // try (EMFInputStream emfStream = new EMFInputStream(new ByteArrayInputStream(bytes), EMFInputStream.DEFAULT_VERSION)) {
            //     // 创建 EMF 渲染器
            //     EMFRenderer emfRenderer = new EMFRenderer(emfStream);
            //     EMFHeader emfHeader = emfStream.readHeader();
            //
            //     // 获取 EMF 文件的实际尺寸
            //     int width = (int) emfHeader.getBounds()
            //             .getWidth();
            //     int height = (int) emfHeader.getBounds()
            //             .getHeight();
            //
            //     // 创建目标位图（BufferedImage）
            //     BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            //     Graphics2D g2d = image.createGraphics();
            //
            //     // 设置渲染质量
            //     g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            //     g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            //
            //     // 将 EMF 绘制到 BufferedImage
            //     emfRenderer.paint(g2d);
            //
            //     // 保存为 PNG
            //     bytes = convertToPngBytes(image);
            //     // 释放资源
            //     g2d.dispose();
            // } catch (Exception e) {
            //     log.info("emf转为异常：", e);
            // }
            fileName = "emf.png";
        }

        // 上传oss
        UploadFileDto uploadFile = aliyunOssStsUtil.uploadFile(l + fileName, (long) bytes.length, bytes);
        return uploadFile;
    }

    /**
     * 将 BufferedImage 转换为 PNG 格式的字节数组
     */
    public static byte[] convertToPngBytes(BufferedImage image) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            // 写入 PNG 格式数据到字节流
            boolean success = ImageIO.write(image, "PNG", baos);
            if (!success) {
                throw new IOException("无法生成 PNG 数据");
            }
            return baos.toByteArray();
        }
    }
}