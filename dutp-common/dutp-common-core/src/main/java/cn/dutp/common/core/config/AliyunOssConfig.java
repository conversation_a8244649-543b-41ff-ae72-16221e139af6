package cn.dutp.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Repository;

@Data
@Repository("aliyunOssConfig")
@ConfigurationProperties(prefix = "aliyun")
public class AliyunOssConfig {

    private String regionId;

    private String roleArn;

    private String ossEndPoint;

    private String stsEndPoint;

    private String accessKeyId;

    private String accessKeySecret;

    private Integer urlExpirationMinute;

    private String bucket;
}

