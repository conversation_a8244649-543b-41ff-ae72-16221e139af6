package cn.dutp.common.encrypt.utils;


import cn.dutp.common.core.exception.UtilException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 加密工具类，包含SHA-256加密算法
 *
 * <AUTHOR>
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShaUtils {

    /**
     * 使用SHA-256算法生成给定输入字符串的哈希值
     * 此方法首先将输入字符串转换为字节数组，然后使用SHA-256算法计算字节数组的哈希值，
     * 最后将计算出的哈希值转换为十六进制字符串返回
     *
     * @param input 需要生成哈希值的输入字符串
     * @return 返回输入字符串的SHA-256哈希值，表示为十六进制字符串
     */
    public static String sha256(@NotNull String input) throws UtilException {

        // 非空判断
        if (Strings.isEmpty(input)) {
            throw new UtilException("输入字符串为空，无法生成哈希值");
        }

        // 获取SHA-256消息摘要算法的实例
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new UtilException("组件MessageDigest初始化失败：" + e.getMessage(), e);
        }
        // 计算输入字符串的SHA-256哈希值
        byte[] messageDigest = md.digest(input.getBytes());
        // 用于存储哈希值的十六进制形式的字符串
        StringBuilder hexString = new StringBuilder();
        // 遍历字节数组，将每个字节转换为十六进制字符串
        for (byte b : messageDigest) {
            // 将字节与0xff进行位与操作，确保结果为正数，然后转换为十六进制字符串
            String hex = Integer.toHexString(0xff & b);
            // 如果转换后的十六进制字符串长度为1，则在其前补一个0，确保每位哈希值都是两位十六进制数
            if (hex.length() == 1) {
                hexString.append('0');
            }
            // 将十六进制字符串追加到结果中
            hexString.append(hex);
        }
        // 返回最终的十六进制字符串形式的哈希值
        return hexString.toString();
    }


    /**
     * @param bytes
     * @return
     * @throws NoSuchAlgorithmException
     * @throws IOException
     */
    public static String sha256(byte[] bytes) throws NoSuchAlgorithmException, IOException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(bytes);
        return bytesToHex(hashBytes);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }
}
