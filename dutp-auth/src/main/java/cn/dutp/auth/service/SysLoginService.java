package cn.dutp.auth.service;

import cn.dutp.api.common.constant.DutpConstant;
import cn.dutp.common.core.constant.CacheConstants;
import cn.dutp.common.core.constant.Constants;
import cn.dutp.common.core.constant.SecurityConstants;
import cn.dutp.common.core.constant.UserConstants;
import cn.dutp.common.core.domain.R;
import cn.dutp.common.core.enums.UserStatus;
import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.text.Convert;
import cn.dutp.common.core.utils.DateUtils;
import cn.dutp.common.core.utils.MessageUtils;
import cn.dutp.common.core.utils.StringUtils;
import cn.dutp.common.core.utils.ip.IpUtils;
import cn.dutp.common.redis.service.RedisService;
import cn.dutp.common.security.utils.SecurityUtils;
import cn.dutp.common.sms.utils.AliyunSmsUtil;
import cn.dutp.system.api.RemoteUserService;
import cn.dutp.system.api.domain.DutpUser;
import cn.dutp.system.api.domain.DutpUserWithCode;
import cn.dutp.system.api.domain.SysUser;
import cn.dutp.system.api.model.LoginDutpUser;
import cn.dutp.system.api.model.LoginUser;
import cn.hutool.core.util.ObjectUtil;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.http.Header;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    private RedisService redisService;

    /**
     * 登录
     */
    public LoginUser login(String username, String password, String entrance) {
        int userType = entrance == "edu" ? 2 : 0;
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写", userType);
            throw new ServiceException(MessageUtils.message("user.username.password.not.null"));
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围", userType);
            throw new ServiceException(MessageUtils.message("user.password.not.match"));
//            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围", userType);
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.username.not.blank"));
            throw new ServiceException(MessageUtils.message("user.username.not.match"));
//            throw new ServiceException("用户名不在指定范围");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单", userType);
            throw new ServiceException(MessageUtils.message("user.black.list"));
        }
        // 查询用户信息
        // 教务
        R<LoginUser> userResult;
        if (StringUtils.isNotEmpty(entrance) && entrance.equals("edu")) {
            userResult = remoteUserService.getEduUserInfo(username, SecurityConstants.INNER);
        } else {
            userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        }

        if (R.FAIL == userResult.getCode()) {

            // 为了让这部分往下走，再确认一下是不是登录了错误的平台
            if (StringUtils.isNotEmpty(entrance) && entrance.equals("edu")) {
                userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
            }else {
                userResult = remoteUserService.getEduUserInfo(username, SecurityConstants.INNER);
            }

            if (R.FAIL == userResult.getCode()) {
                throw new ServiceException(userResult.getMsg());
            }
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if(StringUtils.isNotEmpty(entrance) && entrance.equals("edu") && !ObjectUtil.isNotEmpty(user.getSchoolId())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号未绑定学校", userType);
            throw new ServiceException(DutpConstant.USER_NO_SCHOOL_ID);
        }
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", userType);
            throw new ServiceException(MessageUtils.message("user.password.delete", username));
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", userType);
            throw new ServiceException(DutpConstant.USER_BLOCKED);
        }
        Set<String> roles = userInfo.getRoles();
        // 作者编辑端不允许登录管理后台和教务
        if (StringUtils.isNotEmpty(entrance) && ("sys".equals(entrance) || "edu".equals(entrance)) && ObjectUtil.isNotEmpty(roles) && (roles.contains("editor") || roles.contains("writer"))) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起,你没有权限登录此平台", userType);
            throw new ServiceException("对不起，你没有权限登录此平台");
        }
        // 管理后台不允许登录作者编辑端和教务
        if (StringUtils.isNotEmpty(entrance) && ("aut".equals(entrance) || "edu".equals(entrance)) && ObjectUtil.isNotEmpty(roles) && (!roles.contains("editor") && !roles.contains("writer") && !roles.contains("edu"))) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起,你没有权限登录此平台", userType);
            throw new ServiceException("对不起，你没有权限登录此平台");
        }
        // 教务不允许登录作者编辑端和和管理后台
        if (StringUtils.isNotEmpty(entrance) && ("aut".equals(entrance) || "sys".equals(entrance)) && ObjectUtil.isNotEmpty(roles) && roles.contains("edu")) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起,你没有权限登录此平台", userType);
            throw new ServiceException("对不起，你没有权限登录此平台");
        }
        passwordService.validate(user, password, userType);
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功", userType);
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 学生教师端登录
     */
    public LoginDutpUser loginEducation(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写", 1);
            throw new ServiceException(DutpConstant.USER_USERNAME_PASSWORD_NOT_NULL);
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围", 1);
            throw new ServiceException(DutpConstant.USER_PASSWORD_NOT_MATCH);
//            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围", 1);
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.username.not.blank"));
            throw new ServiceException(DutpConstant.USER_PASSWORD_NOT_MATCH);
//            throw new ServiceException("用户名不在指定范围");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单", 1);
            throw new ServiceException(DutpConstant.USER_BLACK_LIST);
        }
        // 查询用户信息
        R<LoginDutpUser> userResult = remoteUserService.getDutpUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        LoginDutpUser userInfo = userResult.getData();
        DutpUser user = userResult.getData().getDutpUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", 1);
            throw new ServiceException(DutpConstant.USER_PASSWORD_DELETE);
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", 1);
            throw new ServiceException(DutpConstant.USER_BLOCKED);
        }
        passwordService.validate(user, password);
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, DutpConstant.LOGIN_SUCCESS, 1);
        recordLoginInfo(user.getUserId());
        return userInfo;
    }


    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        // 更新用户登录IP
        sysUser.setLoginIp(IpUtils.getIpAddr());
        // 更新用户登录时间
        sysUser.setLoginDate(DateUtils.getNowDate());
        remoteUserService.recordUserLogin(sysUser, SecurityConstants.INNER);
    }

    public void logout(String loginName, String entrance) {
        int userType = entrance == "edu" ? 2 : 0;
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功", userType);
    }

    /**
     * 注册
     */
    public void register(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException(MessageUtils.message("user.username.password.not.null"));
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException(MessageUtils.message("user.username.length.valid", 2, 10));
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException(MessageUtils.message("user.password.length.valid", 5, 20));
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功", 0);
    }

    /**
     * 学生教师端注册
     */
    public void registerEducation(String username, String phoneNumber, String password, String code) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException(DutpConstant.USER_USERNAME_PASSWORD_NOT_NULL);
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException(MessageUtils.message(DutpConstant.USER_USERNAME_NOT_MATCH, 2, 10));
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException(MessageUtils.message(DutpConstant.USER_PASSWORD_NOT_MATCH, 5, 20));
        }
        Integer checkCode = AliyunSmsUtil.checkCode(phoneNumber, code, "SMS_314725755");
        if (checkCode != 200) {
            throw new ServiceException(DutpConstant.VERIFICATION_CODE_ERROR, 7001);

        }
        // 注册用户信息
        DutpUser dutpUser = new DutpUser();
        dutpUser.setUserName(username);
        dutpUser.setNickName(username);
        dutpUser.setPhonenumber(username);
        dutpUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerDutpUserUserInfo(dutpUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功", 1);
    }

    public LoginUser codeLogin(String phoneNumber, String code, String entrance) {
        int userType = entrance == "edu" ? 2 : 0;
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(phoneNumber, code)) {
            recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "电话/验证码必须填写", userType);
            throw new ServiceException(MessageUtils.message("user.username.password.not.null"));
        }
        // 先查询用户信息，电话号换成id
        // 教务
        R<LoginUser> userResult;
        if (StringUtils.isNotEmpty(entrance) && entrance.equals("edu")) {
            userResult = remoteUserService.infoEduByTel(phoneNumber, SecurityConstants.INNER);
        } else {
            userResult = remoteUserService.infoByTel(phoneNumber, SecurityConstants.INNER);
        }

        if (R.FAIL == userResult.getCode()) {
            recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "电话未注册", userType);
            throw new ServiceException(MessageUtils.message("user.username.not.match"));
        }

        LoginUser loginUser = userResult.getData();

        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单", userType);
            throw new ServiceException(MessageUtils.message("user.black.list"));
        }

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_FAIL, "对不起，您的账号已被删除", userType);
            throw new ServiceException(MessageUtils.message("user.password.delete", loginUser.getUsername()));
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_FAIL, "用户已停用，请联系管理员", userType);
            throw new ServiceException(DutpConstant.USER_BLOCKED);
        }

        Integer checkCode = AliyunSmsUtil.checkCode(user.getPhonenumber(), code, "SMS_314725755");
        if (checkCode != 200) {
            throw new ServiceException("验证码输入错误！");
        }

        recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS, "登录成功", userType);
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 学生教师端用户验证码登录
     */
    public LoginUser codeLoginEducation(String phoneNumber, String code) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(phoneNumber, code)) {
            recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "电话/验证码必须填写", 1);
            throw new ServiceException(DutpConstant.USER_USERNAME_VERIFICATION_CODE_NOT_NULL);
        }
        // 先查询用户信息，电话号换成id
        R<LoginUser> userResult = remoteUserService.infoDutpUserByTel(phoneNumber, SecurityConstants.INNER);
        if (R.FAIL == userResult.getCode()) {
            recordLogService.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, "电话未注册", 1);
            throw new ServiceException(DutpConstant.USER_PHONE_UNREGISTERED);
        }

        LoginUser loginUser = userResult.getData();

        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单", 1);
            throw new ServiceException(DutpConstant.USER_BLACK_LIST);
        }

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_FAIL, "对不起，您的账号已被删除", 1);
            throw new ServiceException(MessageUtils.message("user.password.delete", loginUser.getUsername()));
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_FAIL, "用户已停用，请联系管理员", 1);
            throw new ServiceException(DutpConstant.USER_BLOCKED);
        }

        Integer checkCode = AliyunSmsUtil.checkCode(user.getPhonenumber(), code, "SMS_314725755");
        if (checkCode != 200) {
            throw new ServiceException("验证码输入错误！", 7001);

        }

        recordLogService.recordLogininfor(loginUser.getUsername(), Constants.LOGIN_SUCCESS, "登录成功", 1);
        recordLoginInfo(user.getUserId());
        return userInfo;
    }

    /**
     * 学生教师端找回密码
     */
    public void forgotPasswordEducation(String username, String phoneNumber, String password, String code) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException(DutpConstant.USER_USERNAME_PASSWORD_NOT_NULL);
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException(MessageUtils.message(DutpConstant.USER_PASSWORD_NOT_MATCH, 5, 20));
        }
        Integer checkCode = AliyunSmsUtil.checkCode(phoneNumber, code, "SMS_314725755");
        if (checkCode != 200) {
            throw new ServiceException("验证码输入错误！", 7001);
        }
        // 用户信息
        DutpUserWithCode dutpUser = new DutpUserWithCode();
        dutpUser.setUserName(username);
        dutpUser.setPhonenumber(username);
        dutpUser.setCode(code);
        dutpUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.forgotPasswordEducation(dutpUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, DutpConstant.PASSWORD_RECOVERY_SUCCESSFUL, 1);
        // 删除验证码
        String key = Constants.PREFIX_PHONE_CODE + phoneNumber+ "_" + "SMS_314725755";
        redisService.deleteObject(key);
    }

    public void logoutEducation(String username) {
        recordLogService.recordLogininfor(username, Constants.LOGOUT, DutpConstant.LOGOUT_SUCCESSFUL, 1);
    }

    /*
     ** 二维码生成
     */
//    public String createQrCode(String content, int width, int height) throws IOException {
//        String resultImage = "";
//        if (!StringUtils.isEmpty(content)) {
//            ByteArrayOutputStream os = new ByteArrayOutputStream();
//            @SuppressWarnings("rawtypes")
//            HashMap<EncodeHintType, Comparable> hints = new HashMap<>();
//            // 指定字符编码为“utf-8”
//            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
//            // 指定二维码的纠错等级为中级
//            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
//            // 设置图片的边距
//            hints.put(EncodeHintType.MARGIN, 2);
//            try {
//                QRCodeWriter writer = new QRCodeWriter();
//                BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, width, height, hints);
//                BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
//                ImageIO.write(bufferedImage, "png", os);
//                // 原生转码前面没有 data:image/png;base64 这些字段，返回给前端是无法被解析，可以让前端加，也可以在下面加上
//                resultImage = new String("data:image/png;base64," + Base64.encode(os.toByteArray()));
//                return resultImage;
//            } catch (Exception e) {
//                return "生成二维码失败";
//            }
//        }
//        return null;
//    }
    public String createQrCode(String content,
                               int width,
                               int height,
                               String logoUrl,
                               Color foregroundColor,
                               Color backgroundColor) throws WriterException, IOException {
        // 生成二维码矩阵
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.MARGIN, 1);

        BitMatrix bitMatrix = new QRCodeWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 转换为图像并应用颜色
        BufferedImage qrImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                qrImage.setRGB(x, y, bitMatrix.get(x, y) ?
                        foregroundColor.getRGB() :
                        backgroundColor.getRGB());
            }
        }

        // 添加网络Logo
        BufferedImage logoImage = loadImageFromUrlWithToken(logoUrl);
        int maxLogoWidth = width / 5;
        int maxLogoHeight = height / 5;

        // 缩放Logo
        double scaleFactor = Math.min(
                (double) maxLogoWidth / logoImage.getWidth(),
                (double) maxLogoHeight / logoImage.getHeight()
        );
        int scaledWidth = (int) (logoImage.getWidth() * scaleFactor);
        int scaledHeight = (int) (logoImage.getHeight() * scaleFactor);

        Image scaledLogo = logoImage.getScaledInstance(scaledWidth, scaledHeight, Image.SCALE_SMOOTH);
        BufferedImage resizedLogo = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = resizedLogo.createGraphics();
        g.drawImage(scaledLogo, 0, 0, null);
        g.dispose();

        // 叠加Logo
        Graphics2D graphics = qrImage.createGraphics();
        graphics.drawImage(resizedLogo,
                (width - scaledWidth) / 2,
                (height - scaledHeight) / 2,
                null);
        graphics.dispose();

        // 转换为Base64字符串
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(qrImage, "PNG", os);
        return java.util.Base64.getEncoder().encodeToString(os.toByteArray());
    }

    private BufferedImage loadImageFromUrlWithToken(String imageUrl) throws IOException {
        // 1. URL有效性校验
        if (!StringUtils.startsWithAny(imageUrl, "http://", "https://")) {
            throw new IllegalArgumentException("非法的图片URL格式: " + imageUrl);
        }

        // 2. 增强的HttpClient配置
        try (CloseableHttpClient client = HttpClients.custom()
                .setSSLContext(SSLContexts.custom().loadTrustMaterial((chain, authType) -> true).build())
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(5000)
                        .setSocketTimeout(15000)
                        .build())
                .build()) {

            HttpGet request = new HttpGet(imageUrl);

            request.setHeader("Referer", "https://ebook.dutp.cn");

            // 4. 添加请求日志
            try (CloseableHttpResponse response = client.execute(request)) {
                // 5. 增强状态码处理
                StatusLine statusLine = response.getStatusLine();
                if (statusLine.getStatusCode() >= 400) {
                    throw new IOException("OSS请求失败: " + statusLine + " URL: " + imageUrl);
                }

                // 6. 内容类型校验
                Header contentType = response.getFirstHeader("Content-Type");
                if (contentType == null || !contentType.getValue().startsWith("image/")) {
                    throw new IOException("无效的图片响应类型: " + (contentType != null ? contentType.getValue() : "null"));
                }

                // 7. 使用try-with-resources确保流关闭
                try (InputStream inputStream = response.getEntity().getContent()) {
                    BufferedImage image = ImageIO.read(inputStream);
                    if (image == null) {
                        throw new IOException("无法解析图片数据");
                    }
                    return image;
                }
            }
        } catch (IllegalArgumentException e) {
            throw new IOException("图片URL格式错误", e);
        } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
            throw new RuntimeException(e);
        }
    }



    /**
     * 学生教师端登录
     */
    public R<LoginDutpUser> scanCodeToLoginEducation(String username, String passWord) {
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单", 1);
            throw new ServiceException(DutpConstant.USER_BLACK_LIST);
        }
        // 查询用户信息
        R<LoginDutpUser> userResult = remoteUserService.getDutpUserInfo(username, SecurityConstants.INNER);

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }
        if(!passWord.equals(userResult.getData().getDutpUser().getPassword())){
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "扫码登录密码错误", 1);
            throw new ServiceException(DutpConstant.PASSWORD_ERROR);
        }
        LoginDutpUser userInfo = userResult.getData();
        DutpUser user = userResult.getData().getDutpUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", 1);
            throw new ServiceException(DutpConstant.USER_PASSWORD_DELETE);
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", 1);
            throw new ServiceException(DutpConstant.USER_BLOCKED);
        }
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, DutpConstant.LOGIN_SUCCESS, 1);
        recordLoginInfo(user.getUserId());
        return R.ok(userInfo);
    }

}
