package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 互动课堂班级对象 mooc_smart_course_class
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@TableName("mooc_smart_course_class")
public class MoocSmartCourseClass extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 班级ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /**
     * 所属课程ID
     */
    @Excel(name = "所属课程ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 班级名称 (0-60个字符)
     */
    @Excel(name = "班级名称 (0-60个字符)")
    private String className;

    /**
     * 班级码 (例如: A1B2C3, 用于学生加入班级)
     */
    @Excel(name = "班级码 (例如: A1B2C3, 用于学生加入班级)")
    private String classCode;

    /**
     * 班级类型  1-独立班级, 2-平行班级
     */
    @Excel(name = "班级类型  1-独立班级, 2-平行班级")
    private Integer classType;

    /**
     * 授课教师用户
     */
    @Excel(name = "授课教师用户")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 关联云教材ID
     */
    @Excel(name = "关联云教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 班级描述 (最多200字符)
     */
    @Excel(name = "班级描述 (最多200字符)")
    private String description;

    /**
     * 班级封面图片URL
     */
    @Excel(name = "班级封面图片URL")
    private String coverImageUrl;

    /**
     * 班级状态  1-未开课, 2-进行中, 3-已归档
     */
    @Excel(name = "班级状态  1-未开课, 2-进行中, 3-已归档")
    private Integer status;

    /**
     * 关联教学班id 多选 用逗号分割
     */
    @Excel(name = "关联教学班id 多选 用逗号分割")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String eduClassId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    /**
     * 课程类别 1-教务课程, 2-自主课程
     */
    @TableField(exist = false)
    private Integer courseType;

    /**
     * 课程名称
     */
    @TableField(exist = false)
    private String courseName;

    /**
     * 课程编码
     */
    @TableField(exist = false)
    private String courseCode;

    /**
     * 授课教师名称
     */
    @TableField(exist = false)
    private String realName;

    /**
     * 学校ID
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /** 院系id */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long academyId;

    /**
     * 课时（小时）
     */
    @TableField(exist = false)
    private Long hour;

    /**
     * 实际使用课时
     */
    @TableField(exist = false)
    private Long useHour;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * 学校名称
     */
    @TableField(exist = false)
    private String schoolName;

    /**
     * 学生人数
     */
    @TableField(exist = false)
    private Integer studentCount;

    /**
     * 助教名称
     */
    @TableField(exist = false)
    private String assistantName;

    /**
     * 上课记录
     */
    @TableField(exist = false)
    private List<MoocSmartCourseLesson> lessonList;

    /**
     * 查询年份
     */
    @TableField(exist = false)
    private Integer year;

    /**
     * 查询月份
     */
    @TableField(exist = false)
    private Integer month;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("classId", getClassId())
                .append("courseId", getCourseId())
                .append("className", getClassName())
                .append("classCode", getClassCode())
                .append("classType", getClassType())
                .append("userId", getUserId())
                .append("bookId", getBookId())
                .append("description", getDescription())
                .append("coverImageUrl", getCoverImageUrl())
                .append("status", getStatus())
                .append("eduClassId", getEduClassId())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("delFlag", getDelFlag())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
