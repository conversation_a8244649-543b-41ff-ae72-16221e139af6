package cn.dutp.edu.domain;

    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂班级提问对象 mooc_smart_course_class_question
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_class_question")
public class MoocSmartCourseClassQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 问题ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long questionId;

    /** 课程ID */
        @Excel(name = "课程ID")
    private Long courseId;

    /** 提问者ID */
        @Excel(name = "提问者ID")
    private Long questionCreatorId;

    /** 问题标题 */
        @Excel(name = "问题标题")
    private String questionTitle;

    /** 问题内容 */
        @Excel(name = "问题内容")
    private String questionContent;

    /** 结束时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date questionEndDate;

    /** 是否过滤未签到学生（0：过滤，1：不过滤） */
        @Excel(name = "是否过滤未签到学生", readConverterExp = "0=：过滤，1：不过滤")
    private String signInFlag;

    /** 提问方式（0：随机提问，1：点名提问） */
        @Excel(name = "提问方式", readConverterExp = "0=：随机提问，1：点名提问")
    private String questionType;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("questionId", getQuestionId())
            .append("courseId", getCourseId())
            .append("questionCreatorId", getQuestionCreatorId())
            .append("questionTitle", getQuestionTitle())
            .append("questionContent", getQuestionContent())
            .append("questionEndDate", getQuestionEndDate())
            .append("signInFlag", getSignInFlag())
            .append("questionType", getQuestionType())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
