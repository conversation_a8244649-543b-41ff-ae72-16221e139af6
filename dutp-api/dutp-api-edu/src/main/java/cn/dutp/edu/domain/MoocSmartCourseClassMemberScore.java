package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂班级成员各项汇总成绩对象 mooc_smart_course_class_member_score
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_class_member_score")
public class MoocSmartCourseClassMemberScore extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 成员成绩ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long memberScoreId;

    /**
     * 班级成员ID
     */
    @Excel(name = "班级成员ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classMemberId;

    /**
     * 总成绩
     */
    @Excel(name = "总成绩")
    private Long totalScore;

    /**
     * 课件成绩
     */
    @Excel(name = "课件成绩")
    private Long coursewareScore;

    /**
     * 课堂提问成绩
     */
    @Excel(name = "课堂提问成绩")
    private Long classroomQuestionScore;

    /**
     * 作业成绩
     */
    @Excel(name = "作业成绩")
    private Long homeworkScore;

    /**
     * 考试成绩
     */
    @Excel(name = "考试成绩")
    private Long examScore;

    /**
     * 评语
     */
    @Excel(name = "评语")
    private String notes;

    /**
     * 班级ID
     */
    @Excel(name = "班级ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /**
     * 学生用户ID
     */
    @Excel(name = "学生用户ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 创建者用户ID
     */
    @Excel(name = "创建者用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long createdBy;

    /**
     * 最后更新者用户ID
     */
    @Excel(name = "最后更新者用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long updatedBy;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 答题人姓名
     */
    @TableField(exist = false)
    private String realName;

    /**
     * 答题人学号
     */
    @TableField(exist = false)
    private String userNo;

    /**
     * 课堂提问权重 (整数形式, 例如: 30 代表30%)
     */
    @TableField(exist = false)
    private Long classroomQuestionWeight;

    /**
     * 作业权重 (整数形式, 例如: 40 代表40%)
     */
    @TableField(exist = false)
    private Long homeworkWeight;

    /**
     * 考试权重 (整数形式, 例如: 30 代表30%)
     */
    @TableField(exist = false)
    private Long examWeight;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("memberScoreId", getMemberScoreId())
                .append("classMemberId", getClassMemberId())
                .append("totalScore", getTotalScore())
                .append("coursewareScore", getCoursewareScore())
                .append("classroomQuestionScore", getClassroomQuestionScore())
                .append("homeworkScore", getHomeworkScore())
                .append("examScore", getExamScore())
                .append("notes", getNotes())
                .append("classId", getClassId())
                .append("userId", getUserId())
                .append("createdBy", getCreatedBy())
                .append("updatedBy", getUpdatedBy())
                .append("delFlag", getDelFlag())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
