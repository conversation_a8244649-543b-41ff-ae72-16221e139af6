package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 互动课堂的问卷调查对象 mooc_smart_course_survey
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_survey")
public class MoocSmartCourseSurvey extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long surveyId;

    /** 课程ID */
        @Excel(name = "课程ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /** 创建者ID */
        @Excel(name = "创建者ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 参与率 */
        @Excel(name = "参与率")
    private Integer participationRate;

    /** 是否删除（0：存在，2：已删除） */
    private String delFlag;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 截至时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 问卷主题 */
    private String surveyTopic;

    /** 问卷名称 */
    private String surveyName;

    /** 进行状态（1：进行中，2：已结束） */
    @TableField(exist = false)
    private String progressStatus;

    /** 作答状态（0:未参与、1已参与*/
    @TableField(exist = false)
    private String answerStatus;

    /** 课程ID */
    @TableField(exist = false)
    private List<Long> courseIds;

    /**
     * 用户名id
     */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 授课教师名称
     */
    @TableField(exist = false)
    private String realName;


@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("surveyId", getSurveyId())
            .append("courseId", getCourseId())
            .append("creatorId", getCreatorId())
            .append("participationRate", getParticipationRate())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
