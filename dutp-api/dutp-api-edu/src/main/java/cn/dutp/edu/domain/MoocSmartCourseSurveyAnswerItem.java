package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 问卷调查答题明细（按题目存储）对象 mooc_smart_course_survey_answer_item
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
@TableName("mooc_smart_course_survey_answer_item")
public class MoocSmartCourseSurveyAnswerItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 答题项主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerItemId;

    /** 关联的原答案ID */
        @Excel(name = "关联的原答案ID")
    private Long answerId;

    /** 问卷ID */
        @Excel(name = "问卷ID")
    private Long surveyId;

    /** 课程ID */
        @Excel(name = "课程ID")
    private Long courseId;

    /** 参与者ID */
        @Excel(name = "参与者ID")
    private Long userId;

    /** 题目ID */
        @Excel(name = "题目ID")
    private Long questionId;

    /** 题目类型（单选/多选/填空等） */
        @Excel(name = "题目类型", readConverterExp = "单=选/多选/填空等")
    private String questionType;

    /** 答案内容（存储选项ID或文本） */
        @Excel(name = "答案内容", readConverterExp = "存=储选项ID或文本")
    private String answerValue;

    /** 是否删除 */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("answerItemId", getAnswerItemId())
            .append("answerId", getAnswerId())
            .append("surveyId", getSurveyId())
            .append("courseId", getCourseId())
            .append("userId", getUserId())
            .append("questionId", getQuestionId())
            .append("questionType", getQuestionType())
            .append("answerValue", getAnswerValue())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
