package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 拓展内容提交文件对象 mooc_smart_course_extension_record_file
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@TableName("mooc_smart_course_extension_record_file")
public class MoocSmartCourseExtensionRecordFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 文件ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileId;

    /** 关联的提交记录ID */
        @Excel(name = "关联的提交记录ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long recordId;

    /** 文件名 */
        @Excel(name = "文件名")
    private String fileName;

    /** 文件地址 */
        @Excel(name = "文件地址")
    private String fileUrl;

    /** 文件类型 */
        @Excel(name = "文件类型")
    private String fileType;

    /** 文件大小（字节） */
        @Excel(name = "文件大小", readConverterExp = "字=节")
    private Long fileSize;

    /** 删除标志（0：存在，2：已删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("fileId", getFileId())
            .append("recordId", getRecordId())
            .append("fileName", getFileName())
            .append("fileUrl", getFileUrl())
            .append("fileType", getFileType())
            .append("fileSize", getFileSize())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
