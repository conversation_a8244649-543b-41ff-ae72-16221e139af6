package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂互评对象 mooc_smart_course_mutual_evaluation
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@TableName("mooc_smart_course_mutual_evaluation")
public class MoocSmartCourseMutualEvaluation extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 主键ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long mutualEvaluationId;

    /** 考试/测试ID */
        @Excel(name = "考试/测试ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long assignmentId;

    /** 试卷ID */
        @Excel(name = "试卷ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long testPaperId;

    /** 课程ID */
        @Excel(name = "课程ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /** 班级ID */
        @Excel(name = "班级ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long classId;

    /** 评分方式（0：组间评分，1：组内评分） */
        @Excel(name = "评分方式", readConverterExp = "0=：组间评分，1：组内评分")
    private String gradingMethod;

    /** 评分人ID */
        @Excel(name = "评分人ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fromEvaluationId;

    /** 被评分人ID */
        @Excel(name = "被评分人ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long toEvaluationId;

    /** 分值 */
        @Excel(name = "分值")
    private Integer score;

    /** 是否删除（0：存在，2：已删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("mutualEvaluationId", getMutualEvaluationId())
            .append("assignmentId", getAssignmentId())
            .append("testPaperId", getTestPaperId())
            .append("courseId", getCourseId())
            .append("classId", getClassId())
            .append("gradingMethod", getGradingMethod())
            .append("fromEvaluationId", getFromEvaluationId())
            .append("toEvaluationId", getToEvaluationId())
            .append("score", getScore())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
