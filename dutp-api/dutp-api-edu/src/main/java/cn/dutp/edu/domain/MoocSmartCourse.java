package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 智能课程对象 mooc_smart_course
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course")
public class MoocSmartCourse extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 课程ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long courseId;

    /**
     * 课程名称
     */
    @Excel(name = "课程名称")
    private String courseName;

    /**
     * 课程编码 (0-60个字符)
     */
    @Excel(name = "课程编码 (0-60个字符)")
    private String courseCode;

    /**
     * 课程类别 1-教务课程, 2-自主课程
     */
    @Excel(name = "课程类别 1-教务课程, 2-自主课程")
    private Integer courseType;

    /**
     * 所属专业大类ID
     */
    @Excel(name = "所属专业大类ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long categoryId;

    /**
     * 关联教学计划ID
     */
    @Excel(name = "关联教学计划ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long planId;

    /**
     * 关联云教材ID
     */
    @Excel(name = "关联云教材ID ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 课程描述 (最多200字符)
     */
    @Excel(name = "课程描述 (最多200字符)")
    private String description;

    /**
     * 课程封面图片URL
     */
    @Excel(name = "课程封面图片URL")
    private String coverImageUrl;

    /**
     * 课堂提问权重 (整数形式, 例如: 30 代表30%)
     */
    @Excel(name = "课堂提问权重 (整数形式, 例如: 30 代表30%)")
    private Long classroomQuestionWeight;

    /**
     * 作业权重 (整数形式, 例如: 40 代表40%)
     */
    @Excel(name = "作业权重 (整数形式, 例如: 40 代表40%)")
    private Long homeworkWeight;

    /**
     * 考试权重 (整数形式, 例如: 30 代表30%)
     */
    @Excel(name = "考试权重 (整数形式, 例如: 30 代表30%)")
    private Long examWeight;

    /**
     * 课时（小时）
     */
    @Excel(name = "课时", readConverterExp = "小=时")
    private Long hour;

    /**
     * 课程状态 0未开始 1进行中 2已归档
     */
    @Excel(name = "课程状态 0未开始 1进行中 2已归档")
    private Integer status;

    /**
     * 所有者id
     */
    @Excel(name = "所有者id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 创建者用户ID
     */
    @Excel(name = "创建者用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long createdBy;

    /**
     * 最后更新者用户ID
     */
    @Excel(name = "最后更新者用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long updatedBy;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private Integer classTotal;

    @TableField(exist = false)
    private String realName;

    @TableField(exist = false)
    private String searchVar;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("courseId", getCourseId())
                .append("courseName", getCourseName())
                .append("courseCode", getCourseCode())
                .append("courseType", getCourseType())
                .append("categoryId", getCategoryId())
                .append("planId", getPlanId())
                .append("bookId", getBookId())
                .append("description", getDescription())
                .append("coverImageUrl", getCoverImageUrl())
                .append("classroomQuestionWeight", getClassroomQuestionWeight())
                .append("homeworkWeight", getHomeworkWeight())
                .append("examWeight", getExamWeight())
                .append("hour", getHour())
                .append("status", getStatus())
                .append("userId", getUserId())
                .append("createdBy", getCreatedBy())
                .append("updatedBy", getUpdatedBy())
                .append("delFlag", getDelFlag())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
