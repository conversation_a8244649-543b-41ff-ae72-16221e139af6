package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 作业提交文件对象 mooc_smart_course_homework_submission_file
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@TableName("mooc_smart_course_homework_submission_file")
public class MoocSmartCourseHomeworkSubmissionFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 文件ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileId;

    /** 关联的提交记录ID */
        @Excel(name = "关联的提交记录ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long submissionId;

    /** 文件名 */
        @Excel(name = "文件名")
    private String fileName;

    /** 文件地址 */
        @Excel(name = "文件地址")
    private String fileUrl;

    /** 文件类型 */
        @Excel(name = "文件类型")
    private String fileType;

    /** 文件大小（字节） */
        @Excel(name = "文件大小", readConverterExp = "字=节")
    private Long fileSize;

    /** 删除标志（0：存在，2：已删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("fileId", getFileId())
            .append("submissionId", getSubmissionId())
            .append("fileName", getFileName())
            .append("fileUrl", getFileUrl())
            .append("fileType", getFileType())
            .append("fileSize", getFileSize())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
