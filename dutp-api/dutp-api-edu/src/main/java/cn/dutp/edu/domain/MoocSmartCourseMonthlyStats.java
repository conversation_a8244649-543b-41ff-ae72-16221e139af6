package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 班级月度综合统计 (出勤与学习进度)对象 mooc_smart_course_monthly_stats
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_monthly_stats")
public class MoocSmartCourseMonthlyStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 月度统计ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long statId;

    /** 课程ID ( mooc_smart_course) */
        @Excel(name = "课程ID ( mooc_smart_course)")
    private Long courseId;

    /** 班级ID ( mooc_smart_course_class) */
        @Excel(name = "班级ID ( mooc_smart_course_class)")
    private Long classId;

    /** 统计年份 (例如: 2023) */
        @Excel(name = "统计年份 (例如: 2023)")
    private Integer statYear;

    /** 统计月份 (1-12) */
        @Excel(name = "统计月份 (1-12)")
    private Integer statMonth;

    /** 出勤率  */
        @Excel(name = "出勤率 ")
    private Long attendanceRate;

    /** 月度总出勤学生人次数 */
        @Excel(name = "月度总出勤学生人次数")
    private Long studentsPresentCount;

    /** 月度总应出勤人次数 */
        @Excel(name = "月度总应出勤人次数")
    private Long expectedAttendanceCount;

    /** 月度总学习时长 (秒) */
        @Excel(name = "月度总学习时长 (秒)")
    private Long totalLearningDurationSeconds;

    /** 月度活跃学习人数 */
        @Excel(name = "月度活跃学习人数")
    private Long activeLearnersCount;

    /** 创建者用户ID */
        @Excel(name = "创建者用户ID")
    private Long createdBy;

    /** 最后更新者用户ID */
        @Excel(name = "最后更新者用户ID")
    private Long updatedBy;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("statId", getStatId())
            .append("courseId", getCourseId())
            .append("classId", getClassId())
            .append("statYear", getStatYear())
            .append("statMonth", getStatMonth())
            .append("attendanceRate", getAttendanceRate())
            .append("studentsPresentCount", getStudentsPresentCount())
            .append("expectedAttendanceCount", getExpectedAttendanceCount())
            .append("totalLearningDurationSeconds", getTotalLearningDurationSeconds())
            .append("activeLearnersCount", getActiveLearnersCount())
            .append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
