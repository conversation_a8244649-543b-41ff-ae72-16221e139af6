package cn.dutp.edu.domain.dto;

import cn.dutp.common.core.web.domain.BaseEntity;
import cn.dutp.edu.domain.MoocSmartCourseClassAttendance;
import cn.dutp.edu.domain.MoocSmartCourseLesson;
import lombok.Data;

/**
 * 互动课堂班级活动Dto对象
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
public class MoocSmartCourseClassActivityDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 活动类型 */
    private Integer type;

    /** 互动课堂的单次课堂对象 */
    private MoocSmartCourseLesson moocSmartCourseLesson;

    /** 互动课堂班级签到活动对象 */
    private MoocSmartCourseClassAttendance moocSmartCourseClassAttendance;

}
