package cn.dutp.edu.domain.dto;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 互动课堂班级成员各项汇总成绩对象 mooc_smart_course_class_member_score
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class MoocSmartCourseClassMemberScoreDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 总成绩
     */
    @ExcelProperty(value = "总成绩", index = 3)
    private Long totalScore = 0L;

    /**
     * 课堂提问成绩
     */
    @ExcelProperty(value = "课堂提问成绩", index = 4)
    private Long classroomQuestionScore = 0L;

    /**
     * 作业成绩
     */
    @ExcelProperty(value = "作业成绩", index = 5)
    private Long homeworkScore = 0L;

    /**
     * 考试成绩
     */
    @ExcelProperty(value = "考试成绩", index = 6)
    private Long examScore = 0L;

    /**
     * 答题人姓名
     */
    @ExcelProperty(value = "姓名", index = 2)
    private String realName;

    /**
     * 答题人学号
     */
    @ExcelProperty(value = "学号", index = 1)
    private String userNo;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("totalScore", getTotalScore())
                .append("classroomQuestionScore", getClassroomQuestionScore())
                .append("homeworkScore", getHomeworkScore())
                .append("examScore", getExamScore())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
