package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 教务班级对象 edu_class
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
@TableName("edu_class")
public class EduClass extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 教务班级ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long eduClassId;

    /**
     * 所属学年ID (外键, 关联学年表)
     */
    @Excel(name = "所属学年ID (外键, 关联学年表)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long academicYearId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 所属组织ID (外键, 例如关联院系表)
     */
    @Excel(name = "所属组织ID (外键, 例如关联院系表)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long organizationId;

    /**
     * 所属专业ID (外键, 关联专业表)
     */
    @Excel(name = "所属专业ID (外键, 关联专业表)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long majorId;

    /**
     * 班级编号
     */
    @Excel(name = "班级编号 ")
    private String classCode;

    /**
     * 班级名称
     */
    @Excel(name = "班级名称")
    private String className;

    /**
     * 班级备注
     */
    @Excel(name = "班级备注")
    private String remarks;

    /**
     * 创建者用户标识
     */
    @Excel(name = "创建者用户标识")
    private String createdBy;

    /**
     * 最后更新者用户标识
     */
    @Excel(name = "最后更新者用户标识")
    private String updatedBy;

    /**
     * 删除标志 (0: 存在, 2: 删除)
     */
    private String delFlag;

    @TableField(exist = false)
    private String createBy;

    @TableField(exist = false)
    private String updateBy;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("eduClassId", getEduClassId())
                .append("academicYearId", getAcademicYearId())
                .append("schoolId", getSchoolId())
                .append("organizationId", getOrganizationId())
                .append("majorId", getMajorId())
                .append("classCode", getClassCode())
                .append("className", getClassName())
                .append("remarks", getRemarks())
                .append("createdBy", getCreatedBy())
                .append("createTime", getCreateTime())
                .append("updatedBy", getUpdatedBy())
                .append("updateTime", getUpdateTime())
                .append("delFlag", getDelFlag())
                .toString();
    }
}
