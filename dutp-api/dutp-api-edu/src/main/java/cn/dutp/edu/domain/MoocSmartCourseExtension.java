package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂的拓展内容对象 mooc_smart_course_extension
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_extension")
public class MoocSmartCourseExtension extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /** 扩展学习ID */
        @TableId(type = IdType.ASSIGN_ID)
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long extensionId;

    /** 课程ID */
        @Excel(name = "课程ID")
    private Long courseId;

    /** 扩展学习名称 */
        @Excel(name = "扩展学习名称")
    private String extensionName;

    /** 扩展学习要求 */
        @Excel(name = "扩展学习要求")
    private String extensionContent;

    /** 创建者ID */
        @Excel(name = "创建者ID")
    private Long extensionCreatorId;


    /** 参考答案材料ID集合 */
    @Excel(name = "参考答案材料ID集合")
    private String attachedMaterialIds;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("extensionId", getExtensionId())
            .append("courseId", getCourseId())
            .append("extensionName", getExtensionName())
            .append("extensionContent", getExtensionContent())
            .append("extensionCreatorId", getExtensionCreatorId())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
