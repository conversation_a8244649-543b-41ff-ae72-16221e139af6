package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 课程评价对象 mooc_smart_course_lesson_evaluation
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@TableName("mooc_smart_course_lesson_evaluation")
public class MoocSmartCourseLessonEvaluation extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 评价ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long evaluationId;

    /** 关联的课堂ID */
        @Excel(name = "关联的课堂ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lessonId;

    /** 评价的学生ID */
        @Excel(name = "评价的学生ID")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long studentId;

    /**
     * 评分星级 (1-5星)
     */
    @Excel(name = "评分星级 (1-5星)")
    private Integer rating;

    /**
     * 评价内容 (限500字)
     */
    @Excel(name = "评价内容 (限500字)")
    private String comment;



    /**
     * 删除标志 (0:有效, 2:删除)
     */
    private String delFlag;

@Override
public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("evaluationId", getEvaluationId())
            .append("lessonId", getLessonId())
            .append("studentId", getStudentId())
            .append("rating", getRating())
            .append("comment", getComment())
            .append("delFlag", getDelFlag())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
        .toString();
        }
        }
