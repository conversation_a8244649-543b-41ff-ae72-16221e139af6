package cn.dutp.edu.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 互动课堂学生考试/作业结果对象 mooc_smart_course_test_paper_answer
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("mooc_smart_course_test_paper_answer")
public class MoocSmartCourseTestPaperAnswer extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answertId;

    /**
     * 作业/考试ID
     */
    @Excel(name = "作业/考试ID")
    private Long assignmentId;

    /**
     * 作业秀ID
     */
    @Excel(name = "作业秀ID")
    private Long homeworkShowId;

    /**
     * 答题人ID
     */
    @Excel(name = "答题人ID")
    private Long userId;

    /**
     * 答题结果
     */
    @Excel(name = "答题结果")
    private String answerContent;

    /**
     * 评分
     */
    @Excel(name = "评分")
    private Integer score;

    /**
     * 状态（0：未提交，1：待批改，2：已批改）
     */
    @Excel(name = "状态", readConverterExp = "0=：未提交，1：待批改，2：已批改")
    private String answerType;

    /**
     * 是否删除（0：存在，2：已删除）
     */
    private String delFlag;

    /**
     * 答题人姓名
     */
    @TableField(exist = false)
    private String realName;

    /**
     * 答题人学号
     */
    @TableField(exist = false)
    private String userNo;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("answertId", getAnswertId())
                .append("assignmentId", getAssignmentId())
                .append("homeworkShowId", getHomeworkShowId())
                .append("userId", getUserId())
                .append("answerContent", getAnswerContent())
                .append("score", getScore())
                .append("answerType", getAnswerType())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
